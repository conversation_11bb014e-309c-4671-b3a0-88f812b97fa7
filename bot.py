# hiddy_bot/bot.py

import asyncio  # type: ignore
import logging  # type: ignore
import os  # type: ignore

from aiogram import <PERSON><PERSON>, Dispatch<PERSON>, types  # type: ignore
from aiogram.filters.command import Command  # type: ignore
from aiogram.filters.state import StateFilter  # type: ignore
from aiogram.fsm.context import FSMContext  # type: ignore
from aiogram.fsm.storage.memory import MemoryStorage  # type: ignore
from aiogram.client.default import DefaultBotProperties  # type: ignore
from apscheduler.schedulers.asyncio import AsyncIOScheduler  # type: ignore

from logging_config import setup_logging  # type: ignore
from config import API_TOKEN, PROJECT_DIR, DATABASE_FILE, ADMIN_USER_ID  # type: ignore
from database import create_database_table, get_user_from_database, sync_sqlite_to_json  # type: ignore
from scheduler_tasks import check_subscription_expiry, scheduled_update_panel_db, send_nightly_backup_to_admin, check_trial_expiry_warning, check_subscription_renewal_reminders, backup_all_users_data_to_json
from middlewares import ThrottlingMiddleware, ErrorHandlingMiddleware  # type: ignore
from handlers import common_handlers, user_handlers, admin_handlers  # type: ignore
from handlers import additional_admin_handlers  # type: ignore
from keyboards import create_main_menu, create_user_regular_menu, create_user_initial_menu  # type: ignore
from messages import Messages  # type: ignore

# تنظیم لاگ
setup_logging()
logger = logging.getLogger(__name__)

# ایجاد bot object
bot = Bot(token=API_TOKEN, default=DefaultBotProperties(parse_mode="HTML"))

async def main():
    """Initializes and starts the bot, dispatcher, and scheduler."""
    logger.info("--- Bot starting up ---")

    logger.debug("Checking for 'bank' directory.")
    bank_dir = os.path.join(PROJECT_DIR, "bank")
    if not os.path.exists(bank_dir):
        logger.warning(f"Directory '{bank_dir}' not found. Creating it.")
        os.makedirs(bank_dir, exist_ok=True)
    
    logger.debug("Initializing database table.")
    create_database_table()
    
    # همگام‌سازی اولیه JSON
    logger.info("Performing initial JSON synchronization...")
    try:
        sync_sqlite_to_json()
        logger.info("Initial JSON synchronization completed successfully")
    except Exception as e:
        logger.error(f"Error in initial JSON synchronization: {e}")
    
    logger.debug("Initializing MemoryStorage for FSM.")
    storage = MemoryStorage()

    logger.debug("Creating Bot and Dispatcher objects.")
    dp = Dispatcher(storage=storage)
    
    logger.debug("Applying ThrottlingMiddleware.")
    dp.update.middleware(ThrottlingMiddleware())
    
    logger.debug("Applying ErrorHandlingMiddleware.")
    dp.update.middleware(ErrorHandlingMiddleware())

    # --- Register /cancel command with top priority ---
    @dp.message(Command(commands=["cancel"]), StateFilter("*"))
    async def universal_cancel_handler(message: types.Message, state: FSMContext):
        user = message.from_user
        current_state = await state.get_state()
        logger.info(f"Universal cancel handler triggered by user {user.id} from state {current_state}.")
        
        await state.clear()
        logger.debug(f"State for user {user.id} has been cleared.")

        if user.id == ADMIN_USER_ID:
            logger.debug(f"User {user.id} is ADMIN. Sending admin main menu.")
            await message.answer(Messages.General.OPERATION_CANCELLED, reply_markup=create_main_menu(user.id))
        else:
            logger.debug(f"User {user.id} is a regular user. Checking authentication status for menu.")
            user_data = get_user_from_database(user.id)
            is_authenticated = user_data and user_data[4]
            markup = create_user_regular_menu(user.id) if is_authenticated else create_user_initial_menu()
            logger.debug(f"Sending user menu (authenticated={is_authenticated}).")
            await message.answer(Messages.General.OPERATION_CANCELLED, reply_markup=markup)

    logger.info("Universal /cancel handler registered.")

    # --- Register Routers in Correct Order ---
    logger.debug("Including routers: common_handlers, additional_admin_handlers, admin_handlers, user_handlers.")
    dp.include_router(common_handlers.router)
    dp.include_router(additional_admin_handlers.router)
    dp.include_router(admin_handlers.router)
    dp.include_router(user_handlers.router)
    logger.info("All primary routers have been included in the correct order.")
    
    logger.debug("Initializing AsyncIOScheduler.")
    scheduler = AsyncIOScheduler(timezone="Asia/Tehran")

    # Expose scheduler instance to scheduler_tasks for dynamic job management
    import scheduler_tasks  # type: ignore
    scheduler_tasks.scheduler = scheduler
    logger.debug("Adding scheduler jobs: daily_expiry_check, hourly_db_update, nightly_backup, trial_warnings, renewal_reminders.")
    
    # Existing jobs
    scheduler.add_job(check_subscription_expiry, 'cron', hour=0, minute=5, args=[bot], id="daily_expiry_check")
    scheduler.add_job(scheduled_update_panel_db, 'interval', hours=1, id="hourly_db_update")
    scheduler.add_job(send_nightly_backup_to_admin, 'cron', hour=2, minute=30, args=[bot], id="nightly_backup")
    
    # NEW: Trial expiry warnings (every 30 minutes)
    scheduler.add_job(check_trial_expiry_warning, 'interval', minutes=30, args=[bot], id="trial_expiry_warnings")
    
    # NEW: Subscription renewal reminders (runs at 12:00 PM Iran time daily)
    scheduler.add_job(check_subscription_renewal_reminders, 'cron', hour=12, minute=0, args=[bot], id="renewal_reminders")

    # NEW: Nightly comprehensive user data backup to JSON
    scheduler.add_job(backup_all_users_data_to_json, 'cron', hour=0, minute=0, id="nightly_user_data_backup")
    
    scheduler.start()
    logger.info("Scheduler started successfully.")
    
    logger.info("Starting initial DB update on startup.")
    try:
        await scheduled_update_panel_db()
        logger.info("Initial DB update on startup completed.")
    except Exception as e:
        logger.error(f"Initial DB update on startup failed: {e}", exc_info=True)

    logger.debug("Deleting any existing webhook configuration.")
    await bot.delete_webhook(drop_pending_updates=True)

    logger.info("Starting bot polling...")
    print("Bot is starting polling...")
    
    try:
        await dp.start_polling(bot)
    finally:
        logger.warning("Bot polling has stopped.")
        scheduler.shutdown()
        logger.info("Scheduler shut down.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit):
        logger.info("Bot stopped manually by KeyboardInterrupt or SystemExit.")
    except Exception as e:
        logger.critical(f"An unhandled exception occurred in the main execution block: {e}", exc_info=True)