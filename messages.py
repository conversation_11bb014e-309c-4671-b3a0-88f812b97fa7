# hiddy_bot/messages.py
import logging  # type: ignore

logger = logging.getLogger(__name__)

class Messages:
    # --- General / Common Messages ---
    class General:
        THROTTLE_MESSAGE = "⏳ لطفا کمی صبر کنید..."
        PROCESSING_MESSAGE = "⏳ در حال پردازش..."
        INTERNAL_ERROR_MESSAGE = "❌ خطای داخلی رخ داد. لطفا با پشتیبانی تماس بگیرید."
        OPERATION_CANCELLED = "❌ عملیات لغو شد."
        INVALID_INPUT = "❌ ورودی نامعتبر است."
        DATABASE_ERROR = "❌ خطای پایگاه داده. لطفا دوباره تلاش کنید."
        API_ERROR_FETCH_DETAILS = lambda error: f"خطای دریافت اطلاعات کاربر از سرور: {error}"
        API_ERROR_PARSE_DETAILS = "خطا در پردازش اطلاعات کاربر دریافت شده."
        BACK_TO_MAIN_MENU_MESSAGE = "به منوی اصلی بازگشتید."
        UNEXPECTED_MESSAGE_IN_STATE = "🤔 به نظر می‌رسد در وسط یک عملیات پیام دیگری ارسال کردید.\nلطفا از دکمه‌ها استفاده کنید یا برای لغو عملیات فعلی، دستور /cancel را ارسال نمایید."
        
        # NEW: Error Report Messages
        ERROR_OCCURRED_USER = "😔 متاسفانه ربات به یه مشکلی خورد که باید ادمین ربات سریعاً حلش کنه.\n\n📝 این بخش از گزارش رو به ادمین ربات بفرست تا مشکل حل بشه:\n\n<pre>{error_report}</pre>\n\n🔧 برای حل سریع‌تر مشکل، از دکمه زیر استفاده کنید:"
        ERROR_OCCURRED_ADMIN = "🚨 <b>گزارش خطای بحرانی</b>\n\n{error_report}\n\n⚠️ لطفاً در اسرع وقت این مشکل را بررسی و حل کنید."
        ERROR_REPORT_FORMAT = lambda error_type, persian_time, error_details: f"{error_type}\n{persian_time}\n{error_details}"
        ERROR_BUTTON_SUPPORT = "🆘 ارسال گزارش به پشتیبانی"

    # --- Common Button Texts ---
    class Buttons:
        CANCEL = "❌ لغو"
        BACK = "🔙 بازگشت"
        YES = "✅ بله"
        NO = "❌ خیر"
        CONFIRM = "✅ تایید"
        SUBMIT = "✅ ثبت"
        SKIP_OPTIONAL = lambda item_name: f"➡️ رد کردن {item_name} (اختیاری)"
        CONTACT_SUPPORT = "🧑‍💻 پشتیبانی"

    # --- [FIXED] Messages for the forwardable user info card ---
    class ForwardableUserInfo:
        TITLE = "🎉 <b>اشتراک شما با موفقیت ایجاد شد</b> 🎉\n\n"
        GREETING = lambda name: f"سلام <b>{name}</b> عزیز،\n"
        INTRO = "مشخصات اشتراک شما به شرح زیر است:\n\n"
        USERNAME_LINE = lambda username: f"<b>Username:</b> {username}\n"
        SUBLINK_TITLE_LINE = "<b>Subscription Link:</b>"
        SUB_LINK_CODE = lambda sub_link: f"<code>{sub_link}</code>"
        FOOTER = "\n\n👇 برای مشاهده آنلاین مشخصات و یا دانلود برنامه‌ها، از دکمه‌های زیر استفاده کنید:"
        BUTTON_GOTO_BOT = "🤖 ورود به ربات"
        BUTTON_DOWNLOAD_APPS_USER = "📱 نمایش برنامه‌ها"
        BUTTON_PANEL_ACCESS = "🔗 ورود به پنل کاربری"
        
        # Complete user info for enhanced QR message
        ENHANCED_TITLE = "🎉 <b>اطلاعات کامل اشتراک شما</b> 🎉\n\n"
        ENHANCED_GREETING = lambda name: f"سلام <b>{name}</b> عزیز،\n"
        ENHANCED_INFO = lambda username, expiry_date, remaining_days, volume_limit, volume_used, volume_remaining: (
            f"👤 <b>نام کاربری:</b> {username}\n"
            f"📅 <b>تاریخ انقضا:</b> {expiry_date}\n"
            f"⏰ <b>روزهای باقیمانده:</b> {remaining_days} روز\n"
            f"📊 <b>حجم کل:</b> {volume_limit:.2f} GB\n"
            f"📉 <b>حجم مصرف شده:</b> {volume_used:.2f} GB\n"
            f"📈 <b>حجم باقیمانده:</b> {volume_remaining:.2f} GB\n\n"
        )
        ENHANCED_QR_SECTION = "⬇️ <b>QR Code لینک اشتراک</b> ⬇️\n(برای افزودن آسان به برنامه‌ها اسکن کنید)\n\n🔗 <b>لینک:</b>\n"
        ENHANCED_FOOTER = "\n\n👇 برای مشاهده آنلاین مشخصات و یا دانلود برنامه‌ها، از دکمه‌های زیر استفاده کنید:"

    # --- User Flow Messages ---
    class User:
        class Initial:
            BUTTON_BUY_SUB = "🛒 خرید اشتراک"
            BUTTON_GET_TRIAL = "🧪 دریافت اشتراک تست"
            BUTTON_SUPPORT = "📞 پشتیبانی"
            GREETING_UNAUTHENTICATED = lambda username: (f"👋 سلام <b>{username}</b> عزیز!\n" "به ربات پیشرفته مدیریت و خرید MBN VPN خوش آمدید! 😊 (نسخه v2.0)\n\n" "شما هنوز اشتراک فعالی در ربات ثبت نکرده‌اید.\n\n" "🔑 اگر قبلا اشتراک تهیه کرده‌اید، برای فعال‌سازی یکی از موارد زیر را ارسال کنید:\n" "  - <b>کد UUID</b> اختصاصی شما\n" "  - <b>لینک پنل کاربری</b> که دریافت کرده‌اید\n" "  - یکی از <b>کانفیگ‌های VLESS</b> خودتان\n\n" "🛒 یا می‌توانید با دکمه 'خرید اشتراک'، سرویس جدیدی تهیه کنید.\n\n" "🧪 همچنین امکان دریافت 'اشتراک تست' رایگان وجود دارد.")
            PROMPT_FOR_INFO_INPUT = ("❌ ورودی نامعتبر است.\n" "لطفا <b>UUID</b>، <b>لینک پنل</b> یا یکی از <b>کانفیگ‌های VLESS</b> خود را به درستی وارد کنید، " "یا از دکمه‌های زیر استفاده نمایید.")
            AUTH_SUCCESS_FETCHING_INFO = "✅ احراز هویت موفق! در حال دریافت اطلاعات نهایی..."
            AUTH_SUCCESS_PANEL_ERROR = "✅ احراز هویت موفق بود، اما در نمایش اطلاعات خطایی رخ داد. لطفا دوباره /start را بزنید."
            UNAUTHENTICATED_PROMPT_START = "❌ شما هنوز احراز هویت نشده‌اید یا UUID شما ثبت نشده است. لطفا ابتدا دستور /start را بزنید و اطلاعات اشتراک خود را وارد کنید."
        
        # NEW: Emergency Config for Users
        class EmergencyConfig:
            USER_BUTTON_TEXT = "🚨 کانفیگ اضطراری"
            USER_NO_CONFIG = "⚠️ در حال حاضر پیام اضطراری‌ای تنظیم نشده است."
            USER_CONFIG_TITLE = "🚨 <b>پیام اضطراری</b> 🚨\n\n"
        
        class Menu:
            BUTTON_MY_ACCOUNT = "ℹ️ اکانت من / دریافت کانفیگ"
            BUTTON_RENEW_SUB = "🔄 تمدید اشتراک"
            BUTTON_GET_APPS = "📱 دریافت برنامه ها"
            BUTTON_SERVER_STATUS = "📊 وضعیت سرور"
            
        class Trial:
            CHECKING_ELIGIBILITY = "⏳ در حال بررسی و ایجاد اشتراک تست..."
            ALREADY_ACTIVE = "⚠️ شما در حال حاضر یک اشتراک تست فعال دارید. برای خرید اشتراک دائمی از دکمه 'خرید اشتراک' استفاده کنید."
            ALREADY_HAS_PAID_SUB = "⚠️ شما قبلاً یک اشتراک فعال (غیرتست) دریافت کرده‌اید. برای مشاهده اطلاعات آن /start را بزنید."
            API_ERROR_CREATE = lambda error_data: f"❌ متاسفانه در ایجاد اشتراک تست مشکلی پیش آمد.\nخطا:\n<pre>{error_data}</pre>\n\nلطفا با پشتیبانی تماس بگیرید."
            API_NO_UUID_RETURNED = "❌ خطای داخلی: اشتراک تست ایجاد شد اما شناسه آن دریافت نشد. لطفا با پشتیبانی تماس بگیرید."
            API_INVALID_RESPONSE = "❌ خطای داخلی: پاسخ ایجاد اشتراک تست نامعتبر بود. لطفا با پشتیبانی تماس بگیرید."
            DB_ERROR_POST_CREATE = "❌ خطای پایگاه داده پس از ایجاد اشتراک تست. لطفا /start را مجددا بزنید."
            ACTIVATED_SUCCESS = "✅ اشتراک تست ۱ روزه / ۱ گیگ برای شما فعال شد!"
            FETCHING_PANEL_INFO = "🔄 در حال دریافت اطلاعات پنل..."
            PANEL_INFO_ERROR_POST_CREATE = "اشتراک تست ایجاد شد، اما خطایی در نمایش اطلاعات رخ داد. لطفا /start را بزنید."
            EXPIRED_PROMPT_BUY = ("⏳ **اشتراک تست شما به پایان رسید!**\n\n" "امیدواریم از کیفیت سرویس رضایت داشته باشید. 😊\n\n" "برای ادامه استفاده و خرید اشتراک دائمی، لطفا از دکمه '🛒 خرید اشتراک' در منوی اصلی استفاده کنید.\n\n" "در صورت داشتن هرگونه سوال، دکمه '📞 پشتیبانی' را بزنید.")
        
        class Panel:
            FETCHING_LATEST_INFO = "🔄 در حال دریافت آخرین اطلاعات و لینک‌های اتصال..."
            SERVER_CONNECTION_ERROR = lambda error: f"خطایی در ارتباط با سرور رخ داد: {error}\nلطفا دقایقی دیگر مجددا تلاش کنید."
            USER_NOT_FOUND_ON_SERVER = "❌ اشتراک شما در سرور یافت نشد (یا توسط ادمین حذف شده است).\n\nبرای استفاده مجدد، می‌توانید اشتراک جدیدی خریداری کنید:"
            INFO_TITLE = "📊 <b>اطلاعات اکانت شما</b> 📊\n\n"
            USERNAME = lambda username: f"👤 نام کاربری: {username}\n"
            STATUS_ACTIVE = "🚦 وضعیت: <b>فعال ✅</b>\n"
            STATUS_INACTIVE = "🚦 وضعیت: <b>غیرفعال ❌</b>\n"
            PACKAGE_DAYS = lambda days: f"⏳ مدت کل بسته: {days} روز\n"
            EXPIRY_DATE = lambda date_str: f"🗓️ تاریخ انقضا: {date_str}\n"
            REMAINING_TIME = lambda time_str: f"⏱️ زمان باقیمانده: {time_str}\n\n"
            TOTAL_USAGE_LIMIT = lambda limit_gb: f"📊 حجم کل: {limit_gb} GB\n"
            CURRENT_USAGE = lambda current_gb: f"📉 حجم مصرف شده: {current_gb} GB\n"
            REMAINING_USAGE = lambda remaining_gb: f"📈 حجم باقیمانده: {remaining_gb} GB\n\n"
            SUB_LINK_TITLE = "⬇️ <b>لینک اشتراک (Subscription Link)</b> ⬇️\n\n"
            SUB_LINK_INSTRUCTIONS = ("این لینک رو کپی کنید و در برنامه خودتون (مثل Hiddify, V2rayNG, ...) وارد کنید تا همه سرورها اضافه بشن:\n" "<i>با استفاده از دکمه آموزش و دانلود برنامه ها ، سیستم عامل مورد نظر خودتون رو انتخاب و سپس برنامه را با استفاده از لینک مستقیم دانلود کنید.</i>\n" "<i>کانال راهنما :</i> @mbnvpnhelp\n\n")
            SUB_LINK_CODE = lambda panel_link_url: f"<code>{panel_link_url}</code>\n\n"
            SUB_LINK_NOTE = (
                "✨ <i>با این لینک، سرورها به صورت خودکار آپدیت میشن.</i>\n"
                "💡 همچنین می‌توانید با دکمه '📋 دریافت کانفیگ‌ها' بدون نیاز به باز کردن پنل، کانفیگ‌ها را همین‌جا دریافت کنید."
            )
            QR_SEND_ERROR = "❌ خطایی در ارسال اطلاعات اصلی اکانت رخ داد. لطفا دقایقی دیگر مجددا /start را بزنید."
            BUTTON_PANEL_LINK = "🔗 ورود به پنل کاربری 🔗"
            BUTTON_APPS_GUIDE = "📱 نمایش برنامه‌ها"
            BUTTON_GET_CONFIGS = "📋 دریافت کانفیگ‌ها"
            CONFIGS_FETCHING = "⏳ در حال دریافت کانفیگ‌ها..."
            TRIAL_ENDED_PROMPT = ("✨ امیدواریم از اشتراک تست راضی بوده باشید! ✨\n\n" "برای خرید اشتراک دائمی و ادامه استفاده از سرویس، " "می‌توانید از دکمه '🛒 خرید اشتراک' در منوی اصلی استفاده کنید.")
            EXPIRY_WARNING_TODAY = "🚨 <b>هشدار!</b> اشتراک شما <b>امروز</b> منقضی می‌شود.\n\nبرای جلوگیری از قطعی سرویس، لطفا با دکمه '🔄 تمدید اشتراک' اقدام کنید."
            EXPIRY_WARNING_DAYS = lambda days: f"⏳ <b>یادآوری تمدید:</b> فقط <b>{days} روز</b> از اشتراک شما باقیمانده است.\nبرای جلوگیری از قطعی، از دکمه '🔄 تمدید اشتراک' استفاده کنید."
            USAGE_WARNING_1GB = lambda remaining_gb: f"⚠️ <b>هشدار حجم:** کمتر از **1 گیگابایت** از حجم اشتراک شما باقی مانده است!\n(حجم باقیمانده: {remaining_gb:.2f} GB)\n\nبرای جلوگیری از قطعی، لطفا با استفاده از دکمه '🔄 تمدید اشتراک' اقدام به تمدید نمایید."
            USAGE_WARNING_2GB = lambda remaining_gb: f"⏳ **یادآوری حجم:** کمتر از **2 گیگابایت** از حجم اشتراک شما باقی مانده است.\n(حجم باقیمانده: {remaining_gb:.2f} GB)\n\nپیشنهاد می‌شود جهت جلوگیری از اتمام ناگهانی حجم، با استفاده از دکمه '🔄 تمدید اشتراک' اقدام به تمدید نمایید."
            MENU_AFTER_INFO = "منوی شما:"
        
        class Membership:
            REQUIRED_MESSAGE = lambda channel_username: (f"⚠️ برای استفاده از ربات، ابتدا باید در کانال ما عضو شوید:\n{channel_username}\n\n" "روی دکمه زیر کلیک کنید، عضو شوید و سپس دوباره دستور /start را ارسال نمایید.")
            BUTTON_JOIN_CHANNEL = "📢 عضویت در کانال 📢"
            NOT_FOUND_RETRY = lambda channel_username: f"⚠️ عضویت شما در کانال {channel_username} یافت نشد. لطفا عضو شوید و دوباره /start را بزنید."
        
        class Apps:
            GUIDE_TITLE = ("📱 <b>دانلود برنامه‌های اتصال</b> 📱\n\n" "برای اتصال به سرویس، پیشنهاد ما استفاده از برنامه <b>Hiddify</b> است که بر روی اکثر سیستم‌عامل‌ها کار می‌کند.\n\n" "برای دانلود برنامه مناسب سیستم‌عامل خود، روی دکمه‌های زیر کلیک کنید:\n👇👇👇")
            BUTTON_HIDDIFY = "⭐ Hiddify (تمام سیستم عامل ها)"
            BUTTON_V2RAYNG_ANDROID = "🤖 V2rayNG (اندروید)"
            BUTTON_V2RAYN_WINDOWS = "💻 V2rayN (ویندوز)"
            BUTTON_STREISAND_IOS = "🍏 Streisand (آیفون)"
            BUTTON_HAPP_IOS = "🍏 Happ (آیفون)"
            BUTTON_GUIDE_CHANNEL = "📖 کانال راهنما"
            
        class Support:
            MESSAGE = ("🚨 <b>نیاز به راهنمایی دارید؟</b> 🤝\n" "اگر در مراحل خرید، تمدید، یا استفاده از ربات و سرویس به مشکلی برخوردید، " "یا سوالی داشتید، می‌توانید با کلیک روی دکمه زیر به پشتیبانی ما پیام دهید:")
        
        class Purchase:
            FLOW_TITLE = "🛒 خرید اشتراک جدید 🛒\n\n"
            NO_PLANS_AVAILABLE = "❌ متاسفانه در حال حاضر پلنی برای خرید موجود نیست. لطفا با پشتیبانی تماس بگیرید."
            CHOOSE_PLAN_PROMPT = "لطفا پلن مورد نظر خود را انتخاب کنید:\n\n"
            PLAN_ITEM_FORMAT = lambda description, price_formatted: f"- **{description}**: {price_formatted}\n"
            PLAN_BUTTON_FORMAT = lambda description, price_formatted: f"{description} - {price_formatted}"
            BUTTON_CANCEL_BUY = "❌ لغو خرید"
            PLAN_SELECTION_INVALID = "❌ پلن انتخاب شده نامعتبر است."
            PLAN_ERROR_RETRY = "خطا: پلن نامعتبر.\nلطفا مجددا تلاش کنید:"
            PLAN_SELECTED_CONFIRMATION = lambda description: f"پلن '{description}' انتخاب شد."
            BANK_INFO_TITLE = "💳 اطلاعات واریز وجه 💳\n\n"
            BANK_INFO_AMOUNT_PAYABLE = lambda price_formatted: f"✅ 💰 مبلغ قابل پرداخت: {price_formatted}\n\n"
            BANK_INFO_ACCOUNT_NAME = lambda name: f"👤 نام صاحب حساب: `{name}`\n"
            BANK_INFO_CARD_NUMBER = lambda card_number: f"💳 شماره کارت: `{card_number}`\n"
            BANK_INFO_COPY_NOTE = "(روی شماره کارت کلیک کنید تا کپی شود)\n\n"
            BANK_INFO_PAY_EXACT_AMOUNT = "‼️ لطفا دقیقا مبلغ مشخص شده را واریز نمایید.\n"
            BANK_INFO_SEND_RECEIPT_PHOTO = "📄 پس از واریز، عکس واضح از فیش یا رسید پرداخت را در همین چت ارسال کنید."
            RECEIPT_PROCESSING_ADMIN = "⏳ در حال پردازش و ارسال فیش برای ادمین..."
            RECEIPT_SENT_SUCCESS_USER = "✅ عکس فیش شما دریافت شد و برای تایید به ادمین ارسال گردید.\nلطفا منتظر بمانید. پس از تایید، اشتراک شما فعال شده و به شما اطلاع داده خواهد شد."
            RECEIPT_USER_NOTIFY_ERROR = "❌ متاسفانه در ارسال فیش برای ادمین مشکلی پیش آمد. لطفا مجددا تلاش کنید."
            RECEIPT_PROCESSING_ERROR_GENERAL = "❌ خطای غیرمنتظره‌ای هنگام پردازش فیش رخ داد. لطفا با پشتیبانی تماس بگیرید."
            PROCESS_CANCELLED = "❌ عملیات خرید اشتراک لغو شد."
            COMMAND_CANCELLED = "❌ عملیات خرید اشتراک لغو شد."
            WRONG_CONTENT_FOR_RECEIPT = "❌ لطفا فقط عکس رسید پرداخت را ارسال کنید."
            
            # Message for successful purchase
            SUCCESS_MESSAGE = "🎉 خرید شما با موفقیت انجام شد!"

        class Renewal:
            FLOW_TITLE = "🔄 تمدید اشتراک 🔄\\n\\n"
            UNAUTHENTICATED_ERROR = "❌ شما هنوز احراز هویت نشده‌اید. لطفا ابتدا /start را بزنید."
            NO_PLANS_AVAILABLE = "❌ متاسفانه در حال حاضر پلنی برای تمدید موجود نیست. لطفا با پشتیبانی تماس بگیرید."
            CHOOSE_PLAN_PROMPT = "لطفا پلن تمدید مورد نظر خود را انتخاب کنید:\\n\\n"
            BUTTON_CANCEL_RENEWAL = "❌ لغو تمدید"
            PLAN_SELECTION_INVALID = "❌ پلن انتخاب شده نامعتبر است."
            PLAN_ERROR_RETRY = "خطا: پلن نامعتبر.\\nلطفا مجددا تلاش کنید:"
            PLAN_SELECTED_CONFIRMATION = lambda description: f"پلن تمدید '{description}' انتخاب شد."
            UUID_MISSING_ERROR = "❌ خطای داخلی رخ داد (UUID یافت نشد). لطفا فرآیند تمدید را مجددا آغاز کنید."
            PROCESS_CANCELLED = "❌ عملیات تمدید اشتراک لغو شد."
            COMMAND_CANCELLED = "❌ عملیات تمدید اشتراک لغو شد."
            USER_NOTIFY_FETCH_ERROR = "❌ خطایی در دریافت اطلاعات فعلی شما رخ داد. لطفا با پشتیبانی تماس بگیرید."
            USER_NOTIFY_PARSE_ERROR = "❌ خطایی در پردازش اطلاعات فعلی شما رخ داد. لطفا با پشتیبانی تماس بگیرید."
            
            # Simplified messages for after admin confirmation
            SUCCESS_MESSAGE = "🎉 تمدید شما با موفقیت انجام شد!"
            FAILURE_MESSAGE = "⚠️ متاسفانه در تمدید اشتراک شما مشکلی پیش آمد. لطفا با پشتیبانی تماس بگیرید."

        class ServerStatus:
            CHECKING = "📊 در حال بررسی وضعیت سرور و پینگ..."
            MESSAGE_FORMAT = lambda status_emoji, status_text, ping_text: (f"وضعیت سرور:\n\n" f"{status_emoji} وضعیت پنل: **{status_text}**\n" f"📶 پینگ سرور: **{ping_text}**")
        
        class Scheduler:
            TRIAL_EXPIRED = ("⏳ **اشتراک تست شما به پایان رسید!**\n\n" "امیدواریم از کیفیت سرویس رضایت داشته باشید. 😊\n\n" "برای ادامه استفاده و خرید اشتراک دائمی، لطفا از دکمه '🛒 خرید اشتراک' در منوی اصلی استفاده کنید.\n\n" "در صورت داشتن هرگونه سوال، دکمه '📞 پشتیبانی' را بزنید.")
            USAGE_LOW_1GB = lambda remaining_gb: (f"⚠️ **هشدار حجم:** کمتر از **1 گیگابایت** از حجم اشتراک شما باقی مانده است!\n" f"(حجم باقیمانده: {remaining_gb:.2f} GB)\n\n" "برای جلوگیری از قطعی، لطفا با استفاده از دکمه '🔄 تمدید اشتراک' اقدام به تمدید نمایید.")
            USAGE_LOW_2GB = lambda remaining_gb: (f"⏳ **یادآوری حجم:** کمتر از **2 گیگابایت** از حجم اشتراک شما باقی مانده است.\n" f"(حجم باقیمانده: {remaining_gb:.2f} GB)\n\n" "پیشنهاد می‌شود جهت جلوگیری از اتمام ناگهانی حجم، با استفاده از دکمه '🔄 تمدید اشتراک' اقدام به تمدید نمایید.")
            EXPIRY_5_DAYS = ("⏳ **یادآوری تمدید:** فقط **5 روز** تا پایان اعتبار اشتراک شما باقی مانده است.\n\n" "جهت جلوگیری از قطعی سرویس، لطفا با دکمه '🔄 تمدید اشتراک' اقدام نمایید.")
            EXPIRY_2_DAYS = ("⏳ **یادآوری تمدید:** تنها **2 روز** دیگر اشتراک شما فعال است.\n\n" "پیشنهاد می‌کنیم هرچه زودتر با دکمه '🔄 تمدید اشتراک' آن را تمدید کنید.")
            EXPIRY_1_DAY = ("🚨 **هشدار!** اشتراک شما **فردا** منقضی می‌شود (فقط **1 روز** باقیمانده).\n\n" "برای جلوگیری از قطعی سرویس، همین امروز با دکمه '🔄 تمدید اشتراک' اقدام کنید.")
            SUBSCRIPTION_EXPIRED = ("❌ **اشتراک شما منقضی شده است!**\n\n" "متاسفانه امکان اتصال شما وجود ندارد.\n\n" "برای فعال‌سازی مجدد، لطفا از دکمه '🔄 تمدید اشتراک' استفاده کنید.")

        # NEW: Complete User Guide System
        class Guide:
            BUTTON_TEXT = "📖 راهنمای استفاده"
            
            # Unauthenticated user guide
            UNAUTHENTICATED_TITLE = "📚 <b>راهنمای استفاده از ربات</b>\n\n"
            UNAUTHENTICATED_INTRO = "👋 خوش آمدید! در ادامه راهنمای کامل استفاده از ربات را مطالعه کنید:\n\n"
            
            # Authenticated user guide  
            AUTHENTICATED_TITLE = "📚 <b>راهنمای کاربران احراز هویت شده</b>\n\n"
            AUTHENTICATED_INTRO = "🎉 شما به عنوان کاربر احراز هویت شده دسترسی به تمام امکانات ربات دارید:\n\n"

            # Guide section button texts
            SECTION_ACCOUNT_INFO = "ℹ️ اطلاعات اکانت"
            SECTION_RENEWAL = "🔄 تمدید اشتراک"
            SECTION_APPS_DOWNLOAD = "📱 دریافت برنامه‌ها"
            SECTION_SERVER_STATUS = "📊 وضعیت سرور"
            SECTION_EMERGENCY_CONFIG = "🚨 کانفیگ اضطراری"
            SECTION_TROUBLESHOOTING = "🔧 عیب‌یابی"
            SECTION_SUPPORT_CONTACT = "📞 تماس با پشتیبانی"
            SECTION_AUTHENTICATION = "🔐 احراز هویت"
            SECTION_BUY_SUBSCRIPTION = "🛒 خرید اشتراک"
            SECTION_TRIAL_SUBSCRIPTION = "🧪 اشتراک تست"
            
            # Guide sections content
            AUTH_TITLE = "🔐 <b>راهنمای احراز هویت</b>\n\n"
            AUTH_CONTENT = (
                "برای احراز هویت در ربات:\n\n"
                "1️⃣ UUID (شناسه یکتا) خود را از پنل دریافت کنید\n"
                "2️⃣ UUID را در ربات ارسال کنید\n"
                "3️⃣ منتظر تایید و دریافت اطلاعات باشید\n\n"
                "💡 <b>نکته:</b> UUID شما در آدرس پنل کاربری موجود است"
            )
            
            BUY_TITLE = "🛒 <b>راهنمای خرید اشتراک</b>\n\n"
            BUY_CONTENT = (
                "مراحل خرید اشتراک:\n\n"
                "1️⃣ روی دکمه '🛒 خرید اشتراک' کلیک کنید\n"
                "2️⃣ پلن مورد نظر خود را انتخاب کنید\n"
                "3️⃣ مبلغ را به شماره کارت ارائه شده واریز کنید\n"
                "4️⃣ عکس فیش واریزی را ارسال کنید\n"
                "5️⃣ منتظر تایید ادمین باشید\n\n"
                "⚡️ <b>نکته:</b> حتماً مبلغ دقیق را واریز کنید"
            )
            
            TRIAL_TITLE = "🧪 <b>راهنمای اشتراک تست</b>\n\n"
            TRIAL_CONTENT = (
                "دریافت اشتراک تست:\n\n"
                "1️⃣ روی دکمه '🧪 دریافت اشتراک تست' کلیک کنید\n"
                "2️⃣ اشتراک تست خودکار برای شما ایجاد می‌شود\n"
                "3️⃣ اطلاعات اشتراک و QR کد دریافت کنید\n\n"
                "⏰ <b>مدت زمان:</b> 1 روز\n"
                "📊 <b>حجم:</b> 1 گیگابایت\n\n"
                "💰 برای ادامه استفاده، اشتراک پرداختی تهیه کنید"
            )
            
            SUPPORT_TITLE = "📞 <b>راهنمای پشتیبانی</b>\n\n"
            SUPPORT_CONTENT = (
                "راه‌های ارتباط با پشتیبانی:\n\n"
                "📱 <b>تلگرام:</b> از دکمه '📞 پشتیبانی' استفاده کنید\n"
                "🕐 <b>ساعات پاسخگویی:</b> 24 ساعته\n\n"
                "❓ <b>موارد قابل پیگیری:</b>\n"
                "• مشکلات فنی\n"
                "• سوالات مالی\n"
                "• راهنمایی نصب\n"
                "• گزارش خرابی"
            )
            
            # Authenticated sections
            ACCOUNT_TITLE = "ℹ️ <b>راهنمای اطلاعات اکانت</b>\n\n"
            ACCOUNT_CONTENT = (
                "مشاهده اطلاعات حساب کاربری:\n\n"
                "📋 <b>اطلاعات نمایش داده شده:</b>\n"
                "• نام کاربری\n"
                "• وضعیت اشتراک (فعال/غیرفعال)\n"
                "• تاریخ انقضا\n"
                "• حجم مصرفی و باقیمانده\n"
                "• لینک اشتراک و QR کد\n\n"
                "🔄 <b>نکته:</b> اطلاعات به صورت لحظه‌ای بروزرسانی می‌شود"
            )
            
            RENEWAL_TITLE = "🔄 <b>راهنمای تمدید اشتراک</b>\n\n"
            RENEWAL_CONTENT = (
                "مراحل تمدید اشتراک:\n\n"
                "1️⃣ روی دکمه '🔄 تمدید اشتراک' کلیک کنید\n"
                "2️⃣ پلن تمدید مورد نظر را انتخاب کنید\n"
                "3️⃣ مبلغ را واریز و فیش را ارسال کنید\n"
                "4️⃣ پس از تایید، اشتراک تمدید می‌شود\n\n"
                "⚠️ <b>توجه:</b> تمدید قبل از انقضا انجام دهید"
            )
            
            APPS_TITLE = "📱 <b>راهنمای دریافت برنامه‌ها</b>\n\n"
            APPS_CONTENT = (
                "برنامه‌های قابل استفاده:\n\n"
                "📱 <b>اندروید:</b> Hiddify, V2RayNG\n"
                "🖥️ <b>ویندوز:</b> V2RayN, Hiddify\n"
                "🍎 <b>iOS:</b> Streisand, HApp\n\n"
                "📚 <b>آموزش نصب:</b>\n"
                "لینک‌های آموزشی در کانال راهنما موجود است\n\n"
                "💡 <b>پیشنهاد:</b> Hiddify برای تمام پلتفرم‌ها"
            )
            
            SERVER_TITLE = "📊 <b>راهنمای وضعیت سرور</b>\n\n"
            SERVER_CONTENT = (
                "بررسی وضعیت سرور:\n\n"
                "✅ <b>آنلاین:</b> سرور در دسترس است\n"
                "❌ <b>آفلاین:</b> سرور موقتاً قطع است\n"
                "🔍 <b>پینگ:</b> سرعت اتصال نمایش داده می‌شود\n\n"
                "⚡️ <b>نکته:</b> در صورت آفلاین بودن، صبر کنید"
            )
            
            EMERGENCY_TITLE = "🚨 <b>راهنمای کانفیگ اضطراری</b>\n\n"
            EMERGENCY_CONTENT = (
                "استفاده از کانفیگ اضطراری:\n\n"
                "🔴 <b>زمان استفاده:</b>\n"
                "• هنگام فیلتر شدن سرور اصلی\n"
                "• مشکلات موقت اتصال\n"
                "• اعلام ادمین برای استفاده\n\n"
                "⚠️ <b>توجه:</b> فقط برای کاربران پرداختی\n"
                "📞 در صورت نیاز با پشتیبانی تماس بگیرید"
            )
            
            TROUBLESHOOTING_TITLE = "🔧 <b>عیب‌یابی و حل مشکل</b>\n\n"
            TROUBLESHOOTING_CONTENT = (
                "حل مشکلات رایج:\n\n"
                "🚫 <b>عدم اتصال:</b>\n"
                "• بررسی وضعیت سرور\n"
                "• تست کانفیگ‌های مختلف\n"
                "• بررسی تنظیمات برنامه\n\n"
                "⏰ <b>کندی اتصال:</b>\n"
                "• تغییر سرور\n"
                "• تغییر پروتکل\n"
                "• بررسی اینترنت\n\n"
                "📞 در صورت ادامه مشکل، با پشتیبانی تماس بگیرید"
            )
            
        # NEW: Smart Notification System
        class Notifications:
            # Trial expiry warning messages (30 minutes before)
            TRIAL_EXPIRY_TITLE = "⏳ اشتراک تست شما رو به اتمام است!"
            TRIAL_EXPIRING_30MIN_TITLE = "⏰ <b>هشدار انقضای اشتراک تست</b>\n\n"
            TRIAL_EXPIRING_30MIN_MESSAGE = (
                "🧪 اشتراک تست شما 30 دقیقه دیگر به پایان می‌رسد!\n\n"
                "😍 آیا از کیفیت سرویس راضی بودید؟\n"
                "🚀 الان بهترین زمان برای تهیه اشتراک دائمی است!\n\n"
                "💎 <b>مزایای اشتراک دائمی:</b>\n"
                "✅ سرعت بالا و پایدار\n"
                "✅ حجم بالا (تا 200 گیگ)\n"
                "✅ پشتیبانی 24 ساعته\n"
                "✅ چندین سرور در دسترس\n\n"
                "💰 <b>تخفیف ویژه:</b> الان خرید کنید و از تخفیف‌های ویژه بهره‌مند شوید!\n\n"
                "👇 روی دکمه زیر کلیک کنید و اشتراک خود را تهیه کنید:"
            )
            
            # Renewal reminder messages for paid users
            RENEWAL_5DAYS_TITLE = "📅 <b>یادآوری تمدید اشتراک</b>\n\n"
            RENEWAL_5DAYS_MESSAGE = lambda user_name, expiry_date: (
                f"سلام {user_name} عزیز! 👋\n\n"
                f"📆 اشتراک شما 5 روز دیگر منقضی می‌شود\n"
                f"📅 تاریخ انقضا: {expiry_date}\n\n"
                "🔄 برای تمدید اشتراک خود اقدام کنید!\n\n"
                "💡 <b>چرا باید زودتر تمدید کنید؟</b>\n"
                "✅ عدم قطعی سرویس\n"
                "✅ حفظ تنظیمات شخصی\n"
                "✅ دسترسی مداوم به اینترنت\n\n"
                "💳 همین الان تمدید کنید و از قطعی جلوگیری کنید!"
            )
            
            RENEWAL_3DAYS_TITLE = "⚠️ <b>هشدار! فقط 3 روز باقیمانده</b>\n\n"
            RENEWAL_3DAYS_MESSAGE = lambda user_name, expiry_date: (
                f"🚨 {user_name} عزیز، توجه!\n\n"
                f"⏰ فقط 3 روز تا انقضای اشتراک شما باقیمانده!\n"
                f"📅 تاریخ انقضا: {expiry_date}\n\n"
                "🔴 <b>خطر قطعی سرویس!</b>\n"
                "اگر الان اقدام نکنید، سرویس شما قطع خواهد شد.\n\n"
                "💎 <b>مزایای تمدید فوری:</b>\n"
                "⚡️ بدون قطعی\n"
                "📈 ادامه سرعت بالا\n"
                "🔒 حفظ امنیت\n\n"
                "👆 همین الان روی دکمه تمدید کلیک کنید!"
            )
            
            RENEWAL_1DAY_TITLE = "🚨 <b>اخطار نهایی! یک روز باقیمانده!</b>\n\n"
            RENEWAL_1DAY_MESSAGE = lambda user_name, expiry_date: (
                f"🔥 {user_name}! این آخرین فرصت است!\n\n"
                f"⏰ فقط یک روز تا قطع شدن اشتراک باقیمانده!\n"
                f"📅 انقضا: {expiry_date}\n\n"
                "🚨 <b>آخرین فرصت برای تمدید!</b>\n\n"
                "💔 <b>بدون تمدید:</b>\n"
                "❌ سرویس شما قطع می‌شود\n"
                "❌ دسترسی به اینترنت آزاد را از دست می‌دهید\n"
                "❌ مجبور به تهیه اشتراک جدید خواهید بود\n\n"
                "💝 <b>پیشنهاد ویژه:</b>\n"
                "🎁 تمدید فوری با بهترین قیمت!\n"
                "⚡️ فعال‌سازی آنی\n\n"
                "👇 الان همین الان تمدید کنید!"
            )
            
            # Notification buttons
            BUTTON_BUY_NOW = "🛒 خرید فوری اشتراک"
            BUTTON_VIEW_PLANS = "📋 مشاهده پلن‌ها"
            BUTTON_CONTACT_SUPPORT = "📞 تماس با پشتیبانی"
            BUTTON_RENEW_NOW = "🔄 تمدید فوری"

    # --- Admin Flow Messages ---
    class Admin:
        class Menu:
            GREETING = lambda username: f"👋 سلام ادمین {username}!"
            BUTTON_SERVER_STATS = "📊 وضعیت سرور"
            BUTTON_GET_BACKUP = "💾 دریافت بکاپ"
            BUTTON_SEND_MESSAGE_USERS = "📤 ارسال پیام به کاربران"
            BUTTON_USER_MANAGEMENT = "👤 مدیریت کاربران"
            BUTTON_PANEL_REPORT = "📊 گزارش کامل پنل"
            
        class ServerStats:
            FETCHING = "⏳ در حال دریافت اطلاعات سرور..."
            SSH_ERROR = lambda error: f"❌ خطای دریافت اطلاعات از طریق SSH:\n<pre>{error}</pre>"
            API_ERROR = lambda error: f"❌ خطای دریافت اطلاعات از API:\n<pre>{error}</pre>"
            API_STATS_INVALID_JSON = "پاسخ دریافتی معتبر نیست (JSON)."
        
        class Backup:
            FETCHING = "⏳ در حال دریافت آخرین فایل بکاپ از سرور..."
            FETCH_ERROR = lambda error: f"❌ خطایی در دریافت فایل بکاپ رخ داد:\n<pre>{error}</pre>"
            SENDING_CAPTION = lambda filename, proxy_path: (f"💾 فایل بکاپ شبانه (آخرین نسخه)\n\n" f"🏷️ نام فایل: <code>{filename}</code>\n" f"🔗 مسیر پروکسی: {proxy_path}")
            NIGHTLY_BACKUP_CAPTION = lambda time, filename: f"💾 بکاپ خودکار شبانه\n\n" f"⏰ زمان: {time}\n" f"🏷️ نام فایل: <code>{filename}</code>"
            NIGHTLY_BACKUP_FETCH_ERROR = lambda error: f"❌ خطای دریافت بکاپ شبانه:\n<pre>{error}</pre>"
            NIGHTLY_BACKUP_UNEXPECTED_ERROR = lambda error: f"❌ خطای غیرمنتظره در عملیات بکاپ شبانه:\n<pre>{error}</pre>"
        
        class UserManagement:
            TITLE = "🎛️ <b>مدیریت کاربران</b> 🎛️"
            BUTTON_SEARCH_UUID = "جست و جوی کاربر"
            BUTTON_ADD_USER = "➕ افزودن کاربر دستی"
            BUTTON_MANAGE_REGULAR = "⚙️ مدیریت کاربران دائمی"
            BUTTON_MANAGE_TRIAL = "⏳ مدیریت کاربران تستی"
            BUTTON_UPDATE_USAGE = "🔄 بروزرسانی حجم مصرفی کاربران"
        
        class UserList:
            API_FETCH_ERROR = lambda error: f"❌ خطای دریافت لیست کاربران:\n<pre>{error}</pre>"
            NO_USERS_FOUND = lambda list_name: f"هیچ کاربری در لیست '{list_name}' یافت نشد."
            BACK_TO_MGMT = "به منوی مدیریت کاربران بازگشتید."
            PAGINATION_FIRST = "⏮️ اولین"
            PAGINATION_LAST = "⏭️ آخرین"
        
        class AddUser:
            PROMPT_NAME = "لطفا نام کاربر را وارد کنید:\n\n(برای لغو /cancel را ارسال کنید)"
            NAME_EMPTY_ERROR = "❌ نام کاربر نمی‌تواند خالی باشد. لطفا دوباره تلاش کنید."
            PROMPT_NOTE = "یادداشت مربوط به کاربر را وارد کنید (اختیاری):\n\n(برای لغو /cancel را ارسال کنید)"
            NOTE_SKIPPED = "یادداشت رد شد."
            PROMPT_USAGE_LIMIT = "حجم مصرفی را به گیگابایت (GB) وارد کنید:\n\n(برای لغو /cancel را ارسال کنید)"
            INVALID_USAGE_LIMIT = "❌ حجم وارد شده نامعتبر است. لطفا یک عدد مثبت وارد کنید."
            PROMPT_PACKAGE_DAYS = "تعداد روزهای بسته را وارد کنید:\n\n(برای لغو /cancel را ارسال کنید)"
            INVALID_PACKAGE_DAYS = "❌ تعداد روزها نامعتبر است. لطفا یک عدد صحیح مثبت وارد کنید."
            PROMPT_MODE = "لطفا حالت ریست شدن حجم کاربر را انتخاب کنید:"
            MODE_SELECTED = lambda mode: f"حالت '{mode}' انتخاب شد."
            CONFIRMATION_TITLE = "<b>بررسی اطلاعات کاربر جدید:</b>\n\n"
            CONFIRMATION_NAME = lambda name: f"👤 نام: {name}\n"
            CONFIRMATION_NOTE = lambda note: f"📝 یادداشت: {note}\n"
            CONFIRMATION_USAGE = lambda usage_gb: f"📊 حجم: {usage_gb} GB\n"
            CONFIRMATION_DAYS = lambda days: f"⏳ روزهای بسته: {days} روز\n"
            CONFIRMATION_MODE = lambda mode: f"🔄 حالت ریست: {mode}\n\n"
            CONFIRMATION_QUERY = "آیا اطلاعات فوق را تایید می‌کنید؟"
            BUTTON_CONFIRM_ADD = "✅ بله، اضافه کن"
            BUTTON_CANCEL_ADD = "❌ خیر، لغو کن"
            CREATING_USER_PROCESSING = "⏳ در حال ساخت کاربر جدید از طریق API..."
            SUCCESS_MESSAGE_DETAILS = lambda name, uuid, limit_gb, days, mode: (
                f"✅ کاربر جدید با موفقیت ایجاد شد!\n\n"
                f"👤 نام: {name}\n"
                f"🔑 UUID: <code>{uuid}</code>\n"
                f"📊 حجم: {limit_gb} GB\n"
                f"⏳ روزها: {days}\n"
                f"🔄 حالت: {mode}"
            )
            SUCCESS_INVALID_JSON = lambda response: f"✅ کاربر ایجاد شد، اما پاسخ دریافتی معتبر نبود:\n<pre>{response}</pre>"
            API_ERROR_ADD_USER = lambda status_code, response_data: f"❌ خطایی در ایجاد کاربر رخ داد.\n\nکد وضعیت: {status_code}\nپاسخ:\n<pre>{response_data}</pre>"
        
        class SearchUser:
            PROMPT_UUID = "لطفا UUID کاربر مورد نظر را وارد کنید:\n\n(برای لغو /cancel را ارسال کنید)"
            INVALID_UUID_FORMAT = "❌ فرمت UUID وارد شده نامعتبر است."
        
        class UserInfoEdit:
            FETCHING_DETAILS = "⏳ در حال دریافت جزئیات کاربر..."
            API_FETCH_ERROR = lambda user_identifier, error_msg: f"❌ خطای دریافت اطلاعات کاربر '{user_identifier}':\n<pre>{error_msg}</pre>"
            API_PARSE_ERROR = "❌ خطایی در پردازش اطلاعات کاربر رخ داد."
            TITLE_DISPLAY = lambda name: f"<b>مشخصات کاربر: {name}</b>\n\n"
            COMMENT = lambda comment: f"📝 یادداشت: <i>{comment}</i>\n"
            UUID = lambda uuid: f"🔑 UUID: <code>{uuid}</code>\n"
            STATUS = lambda status_text: f"🚦 وضعیت: <b>{status_text}</b>\n"
            TELEGRAM_NUM_ID = lambda telegram_id, id_source: f"🆔 آیدی تلگرام: <code>{telegram_id}</code> {id_source}\n"
            TELEGRAM_USERNAME = lambda telegram_username: f"👤 یوزرنیم: @{telegram_username}\n"
            USAGE_LIMIT = lambda usage_limit_gb: f"📊 حجم کل: {usage_limit_gb} گیگ\n"
            USAGE_CURRENT = lambda current_usage_gb: f"📉 حجم مصرف شده: {current_usage_gb:.3f} گیگ\n"
            REMAINING_USAGE = lambda remaining_usage_gb: f"📈 حجم باقیمانده: {remaining_usage_gb:.3f} گیگ\n"
            PACKAGE_DAYS = lambda package_days: f"⏳ روزهای بسته: {package_days} روز\n"
            EXPIRY_DATE = lambda expiry_date_str: f"🗓️ تاریخ انقضا: {expiry_date_str}\n"
            REMAINING_DAYS = lambda remaining_days_str: f"⏰ زمان باقیمانده: {remaining_days_str}\n"
            RESET_MODE = lambda reset_mode: f"🔄 حالت ریست: {reset_mode}\n\n"
            PANEL_LINK = lambda panel_link: f"<a href='{panel_link}'>🔗 لینک پنل کاربری</a>\n"
            SUB_LINK = lambda sub_link: f"<a href='{sub_link}'>📋 لینک اشتراک</a>"
            BUTTON_EDIT_NAME = "✏️ نام"
            BUTTON_EDIT_COMMENT = "✏️ یادداشت"
            BUTTON_EDIT_USAGE_LIMIT = "✏️ حجم"
            BUTTON_EDIT_PACKAGE_DAYS = "✏️ روزها"
            BUTTON_EDIT_MODE = "✏️ حالت"
            BUTTON_EDIT_TELEGRAM_ID = "✏️ آیدی تلگرام"
            BUTTON_RESET_DAYS = "🔄 ریست روز"
            BUTTON_RESET_USAGE = "🔄 ریست حجم"
            BUTTON_ENABLE_USER = "✅ فعالسازی"
            BUTTON_DISABLE_USER = "❌ غیرفعالسازی"
            BUTTON_DELETE_USER = "🗑️ حذف کاربر"
            BUTTON_SHOW_SUB_QR = "🖼️ نمایش QR Code"
            BUTTON_DETACH_TELEGRAM = "🔗💥 قطع اکانت تلگرامی"
            BUTTON_BACK_TO_LIST = "🔙 بازگشت به لیست"
            BUTTON_BACK_TO_MANAGEMENT = "🔙 بازگشت به مدیریت"
            SUB_QR_LINK_NOT_FOUND = lambda user_uuid: f"❌ لینک اشتراک برای کاربر {user_uuid} جهت ساخت QR Code یافت نشد."
            SUB_QR_GENERATION_ERROR = lambda user_uuid: f"❌ خطایی در ساخت QR Code برای کاربر {user_uuid} رخ داد."
            SUB_QR_SEND_ERROR = lambda user_uuid: f"❌ خطایی در ارسال QR Code برای کاربر {user_uuid} رخ داد."
            SUB_QR_CAPTION = lambda user_uuid, sub_link, user_name, user_expiry, user_remaining_days, user_volume_limit, user_volume_used, user_volume_remaining: (
                f"🖼️ <b>اطلاعات QR Code اشتراک</b>\n\n"
                f"👤 <b>نام کاربری:</b> {user_name}\n"
                f"🔑 <b>UUID:</b> <code>{user_uuid}</code>\n\n"
                f"📅 <b>تاریخ انقضا:</b> {user_expiry} ({user_remaining_days} روز مانده)\n"
                f"📊 <b>حجم:</b> {user_volume_used:.2f} GB / {user_volume_limit:.2f} GB\n"
                f"📈 <b>باقیمانده:</b> {user_volume_remaining:.2f} GB\n\n"
                f"🔗 <b>لینک اشتراک (کپی کنید):</b>\n<code>{sub_link}</code>"
            )

        class DeleteUser:
            CONFIRMATION_QUERY = "آیا از حذف این کاربر مطمئن هستید؟"
            CONFIRMATION_WARNING = "⚠️ <b>هشدار: این عملیات غیرقابل بازگشت است!</b>"
            PROCESSING_DELETE = lambda uuid: f"⏳ در حال حذف کاربر با UUID:\n<code>{uuid}</code>"
        
        class DetachTelegram:
            CONFIRMATION_QUERY = "آیا از قطع ارتباط اکانت تلگرامی از این اشتراک مطمئن هستید؟"
            CONFIRMATION_WARNING = "⚠️ <b>توجه:</b> با این عمل:\n• اکانت تلگرامی از این اشتراک جدا می‌شود\n• شخص دیگری می‌تواند با UUID احراز هویت کند\n• اشتراک همچنان فعال باقی می‌ماند"
            PROCESSING_DETACH = lambda uuid, telegram_id: f"⏳ در حال قطع اکانت تلگرامی از اشتراک:\n🔑 UUID: <code>{uuid}</code>\n👤 تلگرام: <code>{telegram_id}</code>"
            SUCCESS_MESSAGE = lambda uuid: f"✅ اکانت تلگرامی با موفقیت از اشتراک قطع شد.\n\n🔑 UUID: <code>{uuid}</code>\n\n💡 این اشتراک حالا می‌تواند توسط شخص دیگری احراز هویت شود."
            ERROR_API = lambda error: f"❌ خطا در قطع اکانت از پنل:\n<pre>{error}</pre>"
            ERROR_DATABASE = "❌ خطا در قطع اکانت از دیتابیس محلی."
        
        class EditField:
            PROMPT_GENERIC_FIELD = lambda field_display, user_uuid: f"لطفا مقدار جدید برای **{field_display}** کاربر با UUID زیر وارد کنید:\n`{user_uuid}`"
            PROMPT_RESET_DAYS = lambda user_uuid: f"لطفا تعداد روزهای جدید را وارد کنید. تاریخ شروع از امروز محاسبه خواهد شد.\nUUID:\n`{user_uuid}`"
            PROMPT_RESET_USAGE = lambda user_uuid: f"لطفا حجم کل جدید را به گیگابایت وارد کنید. حجم مصرفی صفر خواهد شد.\nUUID:\n`{user_uuid}`"
            VALUE_INVALID_PROMPT_RETRY = "❌ مقدار وارد شده نامعتبر است. لطفا دوباره تلاش کنید."
            VALUE_UPDATE_PROCESSING = "⏳ در حال بروزرسانی مقدار..."
            VALUE_UPDATE_SUCCESS = lambda field_display, value, action_verb: f"✅ مقدار **{field_display}** با موفقیت به **{value}** {action_verb}."
            MODE_PROMPT = lambda user_uuid: f"لطفا حالت ریست جدید را برای کاربر با UUID زیر انتخاب کنید:\n`{user_uuid}`"

        class SendMessage:
            OPTIONS_TITLE = "<b>📤 ارسال پیام به کاربران</b>\n\nلطفا گروه مخاطبین خود را انتخاب کنید:"
            BUTTON_TO_ALL = "🗣️ ارسال به همه کاربران"
            BUTTON_TO_SINGLE_UUID = "👤 ارسال به یک UUID خاص"
            BUTTON_SELECT_FROM_LIST = "📋 انتخاب از لیست"
            PROMPT_TARGET_UUID = "لطفا UUID کاربر مورد نظر را برای ارسال پیام وارد کنید:"
            PROMPT_TEXT_FOR_BROADCAST = "لطفا پیام خود را برای ارسال به **همه کاربران** وارد کنید:"
            PROMPT_TEXT_FOR_SINGLE_USER = lambda user_name, uuid: f"لطفا پیام خود را برای ارسال به کاربر **{user_name}** (UUID: `{uuid}`) وارد کنید:"
            PREPARING_TO_SEND = "⏳ در حال آماده‌سازی برای ارسال پیام..."
            TARGET_UUID_NOT_IN_DB = lambda uuid: f"❌ کاربر با UUID زیر در دیتابیس ربات یافت نشد و امکان ارسال پیام وجود ندارد:\n`{uuid}`"
            SINGLE_SEND_SUCCESS = lambda user_name, user_id: f"✅ پیام با موفقیت به کاربر {user_name} (ID: `{user_id}`) ارسال شد."
            SINGLE_SEND_FAIL = lambda user_name, user_id, error_msg: f"❌ ارسال پیام به کاربر {user_name} (ID: `{user_id}`) ناموفق بود.\nخطا: {error_msg}"
            BROADCAST_NO_USERS_FOUND = "هیچ کاربری با UUID ثبت شده برای ارسال پیام یافت نشد."
            BROADCAST_STARTING = lambda user_count: f"⏳ شروع ارسال پیام همگانی به {user_count} کاربر..."
            BROADCAST_PROGRESS = lambda sent, total, last_user_id, status: f"⏳ در حال ارسال: {sent}/{total}\nآخرین کاربر: {last_user_id} ({status})"
            BROADCAST_COMPLETE_TITLE = "📊 نتایج ارسال پیام همگانی 📊\n"
            BROADCAST_STATS_SUCCESS = lambda count: f"✅ موفق: {count} نفر\n"
            BROADCAST_STATS_FAIL = lambda count: f"❌ ناموفق: {count} نفر\n"
            BROADCAST_STATS_BLOCKED = lambda count: f"🚫 بلاک کرده: {count} نفر\n"
            BROADCAST_STATS_TOTAL = lambda count: f"👥 کل: {count} نفر"

        class UpdateUsage:
            PROMPT_INITIATE = "درخواست بروزرسانی حجم مصرفی..."
            PROCESSING = "⏳ در حال ارسال درخواست به سرور برای بروزرسانی حجم مصرفی کاربران..."
            SUCCESS = lambda msg: f"✅ درخواست با موفقیت ارسال شد.\n\nپاسخ سرور:\n<pre>{msg}</pre>"
            API_ERROR = lambda response: f"❌ خطایی در ارسال درخواست رخ داد:\n<pre>{response}</pre>"
        
        class Approval:
            NEW_PURCHASE_REQUEST = lambda user_id, user_fullname, user_username, plan_desc, plan_price: (f"🆕 **درخواست خرید جدید**\n\n" f"👤 کاربر: {user_fullname} (@{user_username})\n" f"🆔 آیدی: <code>{user_id}</code>\n" f"📦 پلن: {plan_desc}\n" f"💰 مبلغ: {plan_price:,} تومان")
            RENEWAL_REQUEST = lambda user_id, user_fullname, user_username, user_uuid, plan_desc, plan_price: (f"🔄 **درخواست تمدید اشتراک**\n\n" f"👤 کاربر: {user_fullname} (@{user_username})\n" f"🆔 آیدی: <code>{user_id}</code>\n" f"🔑 UUID: <code>{user_uuid}</code>\n" f"📦 پلن: {plan_desc}\n" f"💰 مبلغ: {plan_price:,} تومان")
            BUTTON_CONFIRM = "✅ تایید"
            BUTTON_REJECT = "❌ رد"
            APPROVAL_PROCESSED_SUCCESS = "✅ درخواست تایید و پردازش شد."
            APPROVAL_PROCESSED_REJECT = "❌ درخواست رد شد."
            
        class IncomeStatus:
            BUTTON_TEXT = "💰 وضعیت درآمدی"
            FETCHING_STATS = "⏳ در حال محاسبه آمار درآمدی..."
            ERROR_CALCULATING = "❌ خطایی در محاسبه آمار رخ داد."
            TITLE = "📊 <b>وضعیت جامع درآمدی ربات</b>\n\n"
            DAILY_INCOME = "☀️ درآمد امروز: <b>{amount} تومان</b>\n"
            MONTHLY_INCOME_MTD = "📅 درآمد این ماه (MTD): <b>{amount} تومان</b>\n"
            SIX_MONTHLY_INCOME_ROLLING = "📈 درآمد ۶ ماه اخیر: <b>{amount} تومان</b>\n"
            YEARLY_INCOME_YTD = "🗓️ درآمد امسال (YTD): <b>{amount} تومان</b>\n\n"
            PROJECTED_MONTHLY_INCOME = "🔮 درآمد پیش‌بینی شده این ماه: <b>{amount} تومان</b>\n"
            RENEWALS_THIS_MONTH_COUNT = "🔄 تعداد تمدیدها این ماه: <b>{count} نفر</b>\n"
            NEW_PURCHASES_THIS_MONTH_COUNT = "🛒 تعداد خریدهای جدید این ماه: <b>{count} نفر</b>\n"
            DUE_FOR_RENEWAL_THIS_MONTH_COUNT = "⏳ تعداد موعد تمدید این ماه: <b>{count} نفر</b>\n"
            EXPIRING_SOON_NEXT_7_DAYS_COUNT = "🚨 تعداد در حال انقضا (۷ روز): <b>{count} نفر</b>\n"
            TOTAL_ACTIVE_USERS_PANEL_COUNT = "👥 تعداد کل کاربران فعال در پنل: <b>{count} نفر</b>\n"
            BUTTON_SHOW_RENEWED_THIS_MONTH = "مشاهده لیست تمدید شده‌ها"
            BUTTON_SHOW_NEW_PURCHASES_THIS_MONTH = "مشاهده لیست خریدهای جدید"
            BUTTON_SHOW_DUE_RENEWAL_THIS_MONTH = "مشاهده لیست موعد تمدید"
            BUTTON_SHOW_EXPIRING_SOON_7_DAYS = "مشاهده لیست در حال انقضا"
            USER_LIST_TITLE = "👥 <b>لیست کاربران - {category_name}</b>\n"
            CATEGORY_RENEWED = "تمدید شده این ماه"
            CATEGORY_NEW_PURCHASES = "خریدهای جدید این ماه"
            CATEGORY_DUE_RENEWAL = "موعد تمدید این ماه"
            CATEGORY_EXPIRING_SOON = "انقضای نزدیک (۷ روز)"
            RENEWED_TITLE = "\n🔄 <b>کاربران تمدید شده:</b>\n\n"
            NEW_PURCHASES_TITLE = "\n🛒 <b>خریدهای جدید:</b>\n\n"
            DUE_RENEWAL_TITLE = "\n⏳ <b>کاربران موعد تمدید:</b>\n\n"
            EXPIRING_SOON_TITLE = "\n🚨 <b>کاربران با انقضای نزدیک:</b>\n\n"
            TOTAL_COUNT = "📊 تعداد کل: {} نفر\n"
            PROJECTED_INCOME = "💰 درآمد پیش‌بینی شده: {:,} تومان\n"
            DETAILS_NOTE = "\n💡 <i>برای مشاهده جزئیات بیشتر، از منوی مدیریت کاربران استفاده کنید.</i>"
            DUE_RENEWAL_NOTE = "\n💡 <i>این کاربران در این ماه منقضی خواهند شد.</i>"
            EXPIRING_SOON_NOTE = "\n⚠️ <i>این کاربران ظرف ۷ روز آینده منقضی خواهند شد.</i>"
            INVALID_ACTION = "عملیات نامعتبر"
            INFO_ERROR = "خطا در نمایش اطلاعات"
            BUTTON_BACK_TO_INCOME = "🔙 بازگشت به وضعیت درآمدی"
            
        # NEW: Button Management
        class ButtonManagement:
            BUTTON_TEXT = "⚙️ مدیریت دکمه‌ها"
            TITLE = "<b>⚙️ مدیریت دکمه‌های منوی کاربران</b>\n\nوضعیت فعلی دکمه‌ها:"
            BUTTON_TOGGLE_BUY = "دکمه 'خرید اشتراک'"
            BUTTON_TOGGLE_TRIAL = "دکمه 'اشتراک تست'"
            BUTTON_STATUS = lambda enabled: "✅ فعال" if enabled else "❌ غیرفعال"
            BUTTON_NAME_BUY = "'خرید اشتراک'"
            BUTTON_NAME_TRIAL = "'اشتراک تست'"
            TOGGLE_SUCCESS = lambda button_name, status: f"دکمه {button_name} با موفقیت {'فعال' if status else 'غیرفعال'} شد."
            TOGGLE_ERROR = "خطا در تغییر وضعیت دکمه."
            
            # Bank Account Settings
            BUTTON_BANK_SETTINGS = "💳 تنظیمات شماره کارت"
            BANK_SETTINGS_TITLE = "<b>💳 تنظیمات اطلاعات بانکی</b>\n\n"
            CURRENT_BANK_INFO = lambda name, card: f"صاحب حساب فعلی: <b>{name}</b>\nشماره کارت فعلی: <code>{card}</code>"
            BUTTON_EDIT_NAME = "✏️ تغییر نام صاحب حساب"
            BUTTON_EDIT_CARD = "✏️ تغییر شماره کارت"
            PROMPT_BANK_NAME = "لطفا نام جدید صاحب حساب را وارد کنید:"
            PROMPT_BANK_CARD = "لطفا شماره کارت ۱۶ رقمی جدید را بدون فاصله وارد کنید:"
            INVALID_CARD_NUMBER = "❌ شماره کارت باید ۱۶ رقم و فقط عدد باشد."
            BANK_UPDATE_SUCCESS = "✅ اطلاعات با موفقیت بروزرسانی شد."
            BANK_UPDATE_ERROR = "❌ خطا در بروزرسانی اطلاعات."
            UNKNOWN_ACCOUNT_HOLDER = "نامشخص"
            UNKNOWN_CARD_NUMBER = "نامشخص"

        # NEW: Emergency Config
        class EmergencyConfig:
            BUTTON_TEXT = "🚨 مدیریت پیام اضطراری"
            ADMIN_TITLE = "<b>🚨 مدیریت پیام و کانفیگ اضطراری</b>\n\nبا استفاده از این بخش می‌توانید یک پیام (متن، عکس، فیلم، و...) به همراه کانفیگ‌های اضطراری برای کاربران ارسال کنید.\n\nکاربران با زدن دکمه '🚨 کانفیگ اضطراری' در منوی خود، این پیام را دریافت می‌کنند."
            PROMPT_NEW_MESSAGE = "لطفا پیام اضطراری جدید را ارسال کنید. می‌توانید متن، عکس، فیلم یا هر نوع فایل دیگری را ارسال کنید.\n\n(برای لغو /cancel را ارسال کنید یا از دکمه بازگشت استفاده کنید)"
            MESSAGE_SAVED = "✅ پیام اضطراری با موفقیت ذخیره شد."
            MESSAGE_DELETED = "✅ پیام اضطراری با موفقیت حذف شد."
            MESSAGE_DELETE_ERROR = "❌ خطا در حذف پیام اضطراری."
            NO_CONFIG_SET = "⚠️ در حال حاضر هیچ پیام اضطراری تنظیم نشده است."
            CURRENT_MESSAGE_TITLE = "🚨 <b>پیام اضطراری فعلی:</b>\n\n"
            CREATED_AT = lambda time: f"\n\n<i>(ایجاد شده در: {time})</i>"
            BUTTON_SET_NEW = "📝 تنظیم/تغییر پیام"
            BUTTON_VIEW_CURRENT = "👁️ مشاهده پیام فعلی"
            BUTTON_DELETE = "🗑️ حذف پیام فعلی"
            BUTTON_SEND_TEST = "🧪 ارسال تست به ادمین"

            MEDIA_SENT_SUCCESS = "✅ پیام اضطراری نمایش داده شد."
            DELETE_SUCCESS_TEXT = "✅ پیام اضطراری با موفقیت حذف شد.\n\n"
            TEST_MESSAGE_TITLE = "🧪 **تست پیام اضطراری:**\n\n"
            TEST_SENT_SUCCESS = "پیام تست ارسال شد"
            TEST_SENT_ERROR = "خطا در ارسال پیام تست"

        # NEW: Plan Management Messages  
        class PlanManagement:
            BUTTON_TEXT = "📋 مدیریت پلن‌ها"
            TITLE = "📋 <b>مدیریت پلن‌های اشتراک</b> 📋\n\nاز گزینه‌های زیر استفاده کنید:"
            
            BUTTON_VIEW_ALL = "👁️ مشاهده همه پلن‌ها"
            BUTTON_ADD_NEW = "➕ اضافه کردن پلن جدید"
            BUTTON_EDIT = "✏️ ویرایش پلن"
            BUTTON_DELETE = "🗑️ حذف پلن"
            BUTTON_TOGGLE_STATUS = "🔄 تغییر وضعیت"
            
            NO_PLANS_FOUND = "⚠️ هیچ پلنی یافت نشد."
            
            PLAN_LIST_TITLE = "📋 <b>لیست پلن‌های اشتراک</b>\n\n"
            PLAN_ITEM = lambda plan: (f"🔹 <b>{plan['plan_name']}</b>\n"
                                    f"   🆔 شناسه: \n <code>{plan['plan_id']}</code>\n"
                                    f"   📅 مدت: {plan['days']} روز\n"
                                    f"   📊 حجم: {plan['volume_gb']} گیگ\n"
                                    f"   💰 قیمت: {plan['price_toman']:,} تومان\n"
                                    f"   📊 وضعیت: {'✅ فعال' if plan['is_active'] else '❌ غیرفعال'}\n\n")
            
            PROMPT_PLAN_ID = "لطفا شناسه پلن جدید را وارد کنید (انگلیسی و بدون فاصله):\n\nمثال: 1month_50gb\n\n(برای لغو /cancel را ارسال کنید)"
            PROMPT_PLAN_NAME = "لطفا نام پلن را وارد کنید:\n\nمثال: یک ماهه ۵۰ گیگ\n\n(برای لغو /cancel را ارسال کنید)"
            PROMPT_PLAN_DESCRIPTION = "لطفا توضیحات پلن را وارد کنید:\n\n(برای لغو /cancel را ارسال کنید)"
            PROMPT_PLAN_DAYS = "لطفا تعداد روزهای پلن را وارد کنید:\n\nمثال: 30\n\n(برای لغو /cancel را ارسال کنید)"
            PROMPT_PLAN_VOLUME = "لطفا حجم پلن را به گیگابایت وارد کنید:\n\nمثال: 100\n\n(برای لغو /cancel را ارسال کنید)"
            PROMPT_PLAN_PRICE = "لطفا قیمت پلن را به تومان وارد کنید:\n\nمثال: 150000\n\n(برای لغو /cancel را ارسال کنید)"
            
            INVALID_PLAN_ID = "❌ شناسه پلن نامعتبر است. لطفا فقط از حروف انگلیسی، اعداد و خط تیره استفاده کنید."
            PLAN_ID_EXISTS = "❌ پلنی با این شناسه قبلا وجود دارد."
            INVALID_DAYS = "❌ تعداد روزها باید عدد صحیح مثبت باشد."
            INVALID_VOLUME = "❌ حجم باید عدد مثبت باشد."
            INVALID_PRICE = "❌ قیمت باید عدد صحیح مثبت باشد."
            
            CONFIRMATION_TITLE = "✅ <b>تایید اطلاعات پلن جدید</b>\n\n"
            CONFIRMATION_DETAILS = lambda plan: (f"🆔 شناسه: <code>{plan['id']}</code>\n"
                                                f"📝 نام: {plan['name']}\n"
                                                f"📋 توضیحات: {plan['description']}\n"
                                                f"📅 روزها: {plan['days']}\n"
                                                f"📊 حجم: {plan['volume']} گیگ\n"
                                                f"💰 قیمت: {plan['price']:,} تومان\n\n"
                                                f"آیا اطلاعات صحیح است؟")
            
            PLAN_SAVED = "✅ پلن با موفقیت ذخیره شد."
            PLAN_DELETED = "✅ پلن با موفقیت حذف شد."
            PLAN_STATUS_TOGGLED = lambda name, status: f"✅ وضعیت پلن '{name}' به {'فعال' if status else 'غیرفعال'} تغییر یافت."
            
            PLAN_SAVE_ERROR = "❌ خطا در ذخیره پلن."
            PLAN_DELETE_ERROR = "❌ خطا در حذف پلن."
            PLAN_NOT_FOUND = "❌ پلن مورد نظر یافت نشد."

            FIELD_EDIT_TITLE = lambda plan_name: f"✏️ <b>ویرایش فیلدهای پلن '{plan_name}'</b>\n\nکدام فیلد را می‌خواهید ویرایش کنید؟"
            EDIT_NAME_PROMPT = lambda current_name: f"📝 لطفا نام جدید را وارد کنید (نام فعلی: {current_name}):"
            EDIT_DESCRIPTION_PROMPT = lambda current_desc: f"📄 لطفا توضیحات جدید را وارد کنید (توضیحات فعلی: {current_desc}):"
            EDIT_DAYS_PROMPT = lambda current_days: f"📅 لطفا تعداد روزهای جدید را وارد کنید (مقدار فعلی: {current_days}):"
            EDIT_VOLUME_PROMPT = lambda current_volume: f"📊 لطفا حجم جدید (GB) را وارد کنید (مقدار فعلی: {current_volume}):"
            EDIT_PRICE_PROMPT = lambda current_price: f"💰 لطفا قیمت جدید (تومان) را وارد کنید (مقدار فعلی: {current_price:,}):"
            FIELD_UPDATED_SUCCESS = lambda field_name, new_value: f"✅ {field_name} با موفقیت به '{new_value}' تغییر یافت."
            FIELD_UPDATE_ERROR = lambda field_name: f"❌ خطا در بروزرسانی {field_name}."

        # NEW: Dynamic Messaging Enhancement
        class DynamicSendMessage:
            PROMPT_INLINE_BUTTONS = ("🔘 آیا می‌خواهید زیر این پیام دکمه‌های شیشه‌ای (Inline) اضافه کنید؟\n\n"
                                   "تعداد دکمه‌های مورد نظر را انتخاب کنید:")
            
            BUTTON_NO_BUTTONS = "❌ بدون دکمه"
            BUTTON_1_BUTTON = "1️⃣ یک دکمه"
            BUTTON_2_BUTTONS = "2️⃣ دو دکمه"
            BUTTON_3_BUTTONS = "3️⃣ سه دکمه"
            BUTTON_4_BUTTONS = "4️⃣ چهار دکمه"
            BUTTON_5_BUTTONS = "5️⃣ پنج دکمه"
            
            PROMPT_BUTTON_NAME = lambda num: f"📝 لطفا نام دکمه شماره {num} را وارد کنید:\n\n(حداکثر 30 کاراکتر)\n(برای لغو /cancel را ارسال کنید)"
            PROMPT_BUTTON_URL = lambda num: f"🔗 لطفا لینک دکمه شماره {num} را وارد کنید:\n\n(برای لغو /cancel را ارسال کنید)"
            
            INVALID_BUTTON_NAME = "❌ نام دکمه باید کمتر از 30 کاراکتر باشد."
            INVALID_URL = "❌ لینک وارد شده نامعتبر است."
            
            FINAL_PREVIEW_TITLE = "📋 <b>پیش‌نمایش پیام نهایی</b>\n\n"
            FINAL_CONFIRMATION = "آیا این پیام را برای ارسال تایید می‌کنید؟"
            
            BROADCAST_RESULT_TITLE = "📊 <b>نتیجه ارسال پیام</b>\n\n"
            BROADCAST_SUCCESS = lambda count: f"✅ <b>موفق:</b> {count} نفر\n"
            BROADCAST_FAILED = lambda count: f"❌ <b>ناموفق:</b> {count} نفر\n"
            BROADCAST_BLOCKED = lambda count: f"🚫 <b>بلاک کرده:</b> {count} نفر\n"
            BROADCAST_TOTAL = lambda count: f"👥 <b>کل:</b> {count} نفر\n"
            BROADCAST_TIME = lambda seconds: f"⏱️ <b>مدت زمان:</b> {seconds} ثانیه"

            SAVE_ERROR = "❌ خطا در ذخیره پیام اضطراری"
            SEND_CANCELLED = "❌ عملیات ارسال پیام لغو شد."
            TARGET_UUID_TITLE = "🎯 <b>مقصد:</b> {name} (<code>{uuid}</code>)\n\n"
            TARGET_BROADCAST_TITLE = "🎯 <b>مقصد:</b> همه کاربران\n\n"
            MESSAGE_TEXT_TITLE = "💬 <b>متن:</b>\n{text}\n\n"
            MEDIA_TYPE_TITLE = "📎 <b>رسانه:</b> {type}\n\n"
            INLINE_BUTTONS_TITLE = "🔘 <b>دکمه‌های شیشه‌ای:</b>\n"
            INLINE_BUTTON_ITEM = "  {index}. {name} → {url}\n"
            SINGLE_SEND_FAIL_GENERAL = "خطای ارسال"

        # NEW: Renewal Management
        class RenewalManagement:
            BUTTON_TEXT = "⚙️ مدیریت تمدید"
            TITLE = "<b>⚙️ مدیریت روش تمدید اشتراک کاربران</b>\n\nلطفا یکی از روش‌های زیر را به عنوان روش پیش‌فرض برای تمدید انتخاب کنید.\n\nروش فعلی: <b>{current_method_title}</b>"
            METHOD_1_TEXT = "روش ۱: ریست روز و حجم"
            METHOD_2_TEXT = "روش ۲: تجمیع روز و حجم"
            METHOD_3_TEXT = "روش ۳: ریست روز، تجمیع حجم"
            METHOD_4_TEXT = "روش ۴: تجمیع روز، ریست حجم"
            
            METHOD_1_DESC = "روزهای باقیمانده حذف و روزهای جدید اضافه می‌شود. حجم باقیمانده حذف و حجم جدید اضافه می‌شود."
            METHOD_2_DESC = "روزهای جدید به روزهای باقیمانده اضافه می‌شود. حجم جدید به حجم باقیمانده اضافه می‌شود."
            METHOD_3_DESC = "روزهای باقیمانده حذف و روزهای جدید اضافه می‌شود. حجم جدید به حجم باقیمانده اضافه می‌شود."
            METHOD_4_DESC = "روزهای جدید به روزهای باقیمانده اضافه می‌شود. حجم باقیمانده حذف و حجم جدید اضافه می‌شود."
            
            SUCCESS_MESSAGE = "✅ روش تمدید با موفقیت به <b>{selected_method_title}</b> تغییر یافت."
            ERROR_MESSAGE = "❌ خطا در بروزرسانی روش تمدید."
            
            METHOD_TITLES = {
                1: "ریست کامل (روز و حجم)",
                2: "تجمیع کامل (روز و حجم)",
                3: "ریست روز، تجمیع حجم",
                4: "تجمیع روز، ریست حجم"
            }
            NO_METHOD_SET = "تنظیم نشده"

        class PanelReport:
            FETCHING = "⏳ در حال تهیه گزارش جامع پنل..."
            ERROR = "❌ خطا در تهیه گزارش پنل."
            CONFIGS_FETCHING = "⏳ در حال دریافت کانفیگ‌ها..."
            CONFIGS_ERROR = lambda error: f"❌ خطا در دریافت کانفیگ‌ها:\n<pre>{error}</pre>"

    class Cancel:
        NO_OPERATION_ADMIN = "در حال حاضر عملیاتی برای لغو وجود ندارد. (Admin)"
        NO_OPERATION_USER_AUTHENTICATED = "عملیاتی برای لغو نبود. به منوی اصلی بازگشتید."
        NO_OPERATION_USER_UNAUTHENTICATED = "عملیاتی برای لغو نبود. می‌توانید از دکمه‌ها استفاده کنید."

    class Callbacks:
        PAGINATION_LOADING_PAGE = lambda page_num: f"بارگیری صفحه {page_num}..."
        PAGINATION_ERROR_DATA_FORMAT = "خطا در اطلاعات صفحه بندی."
        PAGINATION_ERROR_PROCESSING = "خطا در پردازش صفحه بندی."
        BACK_TO_USER_LIST_LOADING = "بازگشت به لیست کاربران..."
        COPY_SUB_LINK_PROMPT = "لینک در پیام بالا نمایش داده شده. روی آن کلیک کنید تا کپی شود."
        COPY_SUB_LINK_ERROR_RETRY = "❌ خطای دریافت مجدد لینک اشتراک."

    class Utils:
        # For format_ssh_server_info
        SSH_STATS_TITLE = "📊 <b>اطلاعات سرور (SSH)</b> 📊\n\n"
        SSH_STATS_OS = "🖥️ <b>OS:</b> {value}\n"
        SSH_STATS_KERNEL = "🐧 <b>Kernel:</b> {value}\n"
        SSH_STATS_UPTIME = "⏱️ <b>Uptime:</b> {value}\n"
        SSH_STATS_CPU = "💻 <b>CPU Usage:</b> {value}%\n"
        SSH_STATS_LOAD = "📈 <b>Load Average:</b> {value}\n"
        SSH_STATS_RAM = "🐑 <b>RAM Usage:</b> {used} GB / {total} GB\n"
        SSH_STATS_SWAP = "💾 <b>Swap Usage:</b> {used} GB / {total} GB\n"
        SSH_STATS_DISK = "💽 <b>Disk Usage (/):</b> {used} / {total} ({usage})\n"
        SSH_STATS_PROCS = "🚀 <b>Processes:</b> {value}\n"
        SSH_STATS_TCP = "🔗 <b>TCP Connections:</b> {value}\n"
        SSH_STATS_USERS = "👥 <b>Users Online (SSH):</b> {value}\n"
        
        # For format_server_status
        API_STATS_TITLE = "⚙️ <b>وضعیت سرور (Hiddify API)</b> ⚙️\n\n"
        API_STATS_CPU = "💻 <b>CPU:</b> <code>{percent}%</code> ({cores} Cores)\n"
        API_STATS_LOAD = "📈 <b>Load Avg:</b> <code>{one_min} (1m), {five_min} (5m), {fifteen_min} (15m)</code>\n"
        API_STATS_RAM = "🐑 <b>RAM:</b> <code>{used} GB / {total} GB</code>\n"
        API_STATS_DISK = "💽 <b>Disk:</b> <code>{used} GB / {total} GB</code> (Hiddify Usage: <code>{hiddify_used} GB</code>)\n\n"
        API_STATS_CONNECTIONS = "🔗 <b>Connections (Total):</b> <code>{value}</code>\n"
        API_STATS_UNIQUE_IPS = "🌐 <b>Unique IPs (Total):</b> <code>{value}</code>\n\n"
        API_STATS_TRAFFIC_TITLE = "📊 <b>Network Traffic (Cumulative)</b>\n"
        API_STATS_TRAFFIC_SENT = "  ⬆️ <b>Sent:</b> <code>{value} GB</code>\n"
        API_STATS_TRAFFIC_RECV = "  ⬇️ <b>Recv:</b> <code>{value} GB</code>\n"
        API_STATS_TRAFFIC_TOTAL = "  📶 <b>Total:</b> <code>{value} GB</code>\n\n"
        API_STATS_USAGE_TITLE = "📈 <b>Usage & Online Users</b>\n"
        API_STATS_USAGE_USERS = "  👥 <b>Total Users (Panel):</b> <code>{value}</code>\n"
        API_STATS_USAGE_ONLINE_5M = "  🟢 <b>Online (5m):</b> <code>{value}</code>\n"
        API_STATS_USAGE_TODAY = "  ☀️ <b>Today:</b> <code>{usage} GB</code> (Online: <code>{online}</code>)\n"
        API_STATS_USAGE_YESTERDAY = "  🌙 <b>Yesterday:</b> <code>{usage} GB</code> (Online: <code>{online}</code>)\n"
        API_STATS_USAGE_30_DAYS = "  🗓️ <b>Last 30 Days:</b> <code>{usage} GB</code> (Online: <code>{online}</code>)\n"
        API_STATS_USAGE_TOTAL = "  ♾️ <b>Total Usage (Panel):</b> <code>{usage} GB</code>\n"
        API_STATS_TOP5_CPU_TITLE = "\n📉 <b>Top 5 Processes by CPU</b>\n"
        API_STATS_TOP5_RAM_TITLE = "\n💾 <b>Top 5 Processes by RAM (GB)</b>\n"
        API_STATS_TOP5_ITEM = "  - <code>{name}</code>: {value}{unit}\n"
        API_STATS_INVALID_JSON = "پاسخ دریافتی معتبر نیست (JSON)."

        # For send_critical_error_to_admin
        CRITICAL_ERROR_TITLE = "🚨 <b>گزارش خطای بحرانی</b>\n\n"
        CRITICAL_ERROR_TYPE = "🔴 <b>نوع خطا:</b> <code>{value}</code>\n"
        CRITICAL_ERROR_TIME = "🕐 <b>زمان:</b> <code>{value}</code>\n"
        CRITICAL_ERROR_USER = "👤 <b>کاربر:</b> <code>{value}</code>\n"
        CRITICAL_ERROR_CONTEXT = "📍 <b>محل:</b> <code>{value}</code>\n"
        CRITICAL_ERROR_MESSAGE = "📋 <b>پیام خطا:</b>\n<pre>{value}</pre>"
        USER_ID_UNKNOWN = "نامشخص"

def get_plan_details(plan_id: str) -> dict:
    """Get plan details from database"""
    logger.debug(f"Fetching details for plan_id: '{plan_id}' from database.")
    
    # Import here to avoid circular imports
    from database import get_plan_by_id  # type: ignore
    
    plan = get_plan_by_id(plan_id)
    if plan and plan.get('is_active', False):
        # Convert to the expected format
        result = {
            "days": plan['days'],
            "limit_gb": plan['volume_gb'],
            "mode": "no_reset",  # Default mode
            "price": plan['price_toman'],
            "desc": plan['plan_name']
        }
        logger.debug(f"Found active plan details: {result}")
        return result
    else:
        logger.warning(f"Plan with id '{plan_id}' not found or inactive in database.")
        return {}