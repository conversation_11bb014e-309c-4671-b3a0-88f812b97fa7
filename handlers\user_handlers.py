# hiddy_bot/handlers/user_handlers.py

import logging  # type: ignore
import datetime  # type: ignore
import json  # type: ignore
import asyncio  # type: ignore
from html import escape as html_escape  # type: ignore
from io import BytesIO  # type: ignore
import os
import urllib.parse
from typing import Union

import jdatetime  # type: ignore
import qrcode  # type: ignore
from aiogram import Router, F, Bo<PERSON>, types  # type: ignore
from aiogram.fsm.context import FSMContext  # type: ignore
from aiogram.types import Message, CallbackQuery, ReplyKeyboardRemove, BufferedInputFile, ContentType  # type: ignore
from aiogram.utils.keyboard import InlineKeyboardBuilder  # type: ignore
from aiogram.filters import StateFilter  # type: ignore
from aiogram.utils.markdown import html_decoration  # type: ignore

from messages import Messages, get_plan_details  # type: ignore
from states import GetUserInfoState, BuySubscriptionState, RenewSubscriptionState  # type: ignore
from keyboards import (  # type: ignore
    create_user_initial_menu, 
    create_user_regular_menu, 
    create_support_keyboard, 
    create_plan_menu,
    create_cancel_button,
    create_user_guide_keyboard,
    create_guide_back_keyboard
)
from config import (  # type: ignore
    HIDDIFY_API_DOMAIN, 
    HIDDIFY_API_USER_PROXY_PATH, 
    HIDDIFY_API_PROXY_PATH,
    IP_SERVER,
    ADMIN_USER_ID,
    get_bank_account_info
)
from database import get_user_from_database, update_or_add_user, get_bot_setting, get_user_trial_status  # type: ignore
from api_hiddify import fetch_user_details_api, add_user_api, update_user_api, get_server_status, fetch_user_configs_api  # type: ignore
from utils import extract_uuid_from_input, save_receipt_photo, ping_server, format_server_status  # type: ignore


router = Router()
# فیلتر برای اینکه این مدیریت‌کننده‌ها برای ادمین اجرا نشوند
router.message.filter(F.from_user.id != ADMIN_USER_ID)
router.callback_query.filter(F.from_user.id != ADMIN_USER_ID)
logger = logging.getLogger(__name__)


# --- Core Logic Function: Send User Panel ---
async def send_user_panel_info(bot: Bot, user_data_db: tuple, message: Union[Message, None] = None, is_first_time_auth: bool = False, processing_msg_id: int = None) -> bool:
    """
    Sends user panel info. The 'message' parameter is now optional.
    If 'message' is None, it sends the panel directly to the user's chat_id from user_data_db.
    """
    target_chat_id = None
    user_id = None

    if user_data_db and len(user_data_db) >= 3 and user_data_db[2]:
        user_id = user_data_db[2]
        target_chat_id = user_id  # The user's ID is the target chat ID
    elif message:
        user_id = message.from_user.id
        target_chat_id = message.chat.id
    else:
        logger.error("send_user_panel_info called without sufficient context (no user_data_db or message).")
        return False

    logger.info(f"Preparing to send panel info for user {user_id}. Is first time auth: {is_first_time_auth}")

    if not user_data_db or len(user_data_db) < 5 or not user_data_db[4]:
        logger.error(f"Invalid or incomplete user_data_db provided for user {user_id}. Data: {user_data_db}")
        # Try to send a message to the user if we have a chat_id
        if target_chat_id:
            try:
                await bot.send_message(target_chat_id, "❌ خطای داخلی: اطلاعات کاربری شما ناقص است. لطفاً با پشتیبانی تماس بگیرید.")
            except Exception as e:
                logger.error(f"Failed to send error message to user {user_id}: {e}")
        return False

    user_uuid = user_data_db[4]
    logger.debug(f"User {user_id} has UUID: {user_uuid}. Fetching details from API.")

    if processing_msg_id and target_chat_id:
        try: 
            await bot.delete_message(chat_id=target_chat_id, message_id=processing_msg_id)
            logger.debug(f"Deleted processing message {processing_msg_id} for user {user_id}.")
        except Exception as e:
            logger.warning(f"Could not delete processing message {processing_msg_id} for user {user_id}: {e}")

    is_fetch_success, details_resp, status_code = await fetch_user_details_api(user_uuid)

    if not is_fetch_success:
        logger.error(f"Failed to fetch user details for UUID {user_uuid}. Status: {status_code}, Response: {details_resp}")
        if "user not found" in str(details_resp).lower():
            logger.warning(f"User {user_uuid} (TG ID: {user_id}) not found on panel. Clearing their UUID from local DB.")
            from database import clear_user_uuid
            clear_user_uuid(user_id)
            try:
                # FIX: Send user not found message with the initial menu
                from keyboards import create_user_initial_menu
                await bot.send_message(target_chat_id, Messages.User.Panel.USER_NOT_FOUND_ON_SERVER, reply_markup=create_user_initial_menu())
            except Exception as e:
                logger.error(f"Failed to send USER_NOT_FOUND_ON_SERVER message to user {user_id}: {e}")
            return False
        else:
            try:
                await bot.send_message(target_chat_id, Messages.General.API_ERROR_FETCH_DETAILS(error=html_escape(str(details_resp))))
            except Exception as e:
                logger.error(f"Failed to send API error message to user {user_id}: {e}")
            return False

    try:
        user_dict = json.loads(details_resp)
        panel_link_url = f"https://{HIDDIFY_API_DOMAIN}/{HIDDIFY_API_USER_PROXY_PATH}/{user_uuid}/?home=true"
        
        logger.debug(f"Generating QR code for panel link: {panel_link_url}")
        qr = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_L, box_size=10, border=4)
        qr.add_data(panel_link_url)
        qr.make(fit=True)
        img = qr.make_image(fill_color="black", back_color="white")
        
        bio = BytesIO()
        img.save(bio, format='PNG')
        qr_code_bytes = bio.getvalue()

        # Date & Usage calculations
        start_date_str = user_dict.get('start_date')
        start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d').date() if start_date_str else datetime.date.today()
        package_days = user_dict.get('package_days', 0)
        expiry_date = start_date + datetime.timedelta(days=package_days)
        remaining_days = max(0, (expiry_date - datetime.date.today()).days)
        jalali_expiry = jdatetime.date.fromgregorian(date=expiry_date).strftime('%Y/%m/%d')
        
        usage_limit_gb = user_dict.get('usage_limit_GB', 0.0)
        current_usage_gb = user_dict.get('current_usage_GB', 0.0)
        remaining_usage_gb = max(0.0, usage_limit_gb - current_usage_gb)
        
        logger.debug(f"User {user_id} stats: RemainingDays={remaining_days}, RemainingGB={remaining_usage_gb:.2f}")

        # Build message caption
        caption = Messages.User.Panel.INFO_TITLE + \
                  Messages.User.Panel.USERNAME(username=html_escape(user_dict.get('name', ''))) + \
                  (Messages.User.Panel.STATUS_ACTIVE if user_dict.get('enable') else Messages.User.Panel.STATUS_INACTIVE) + \
                  Messages.User.Panel.PACKAGE_DAYS(days=package_days) + \
                  Messages.User.Panel.EXPIRY_DATE(date_str=jalali_expiry) + \
                  Messages.User.Panel.REMAINING_TIME(time_str=f"{remaining_days} روز") + \
                  Messages.User.Panel.TOTAL_USAGE_LIMIT(limit_gb=f"{usage_limit_gb:.2f}") + \
                  Messages.User.Panel.CURRENT_USAGE(current_gb=f"{current_usage_gb:.2f}") + \
                  Messages.User.Panel.REMAINING_USAGE(remaining_gb=f"{remaining_usage_gb:.2f}") + \
                  Messages.User.Panel.SUB_LINK_TITLE + \
                  Messages.User.Panel.SUB_LINK_INSTRUCTIONS + \
                  Messages.User.Panel.SUB_LINK_CODE(panel_link_url=html_escape(panel_link_url)) + \
                  Messages.User.Panel.SUB_LINK_NOTE

        inline_buttons = InlineKeyboardBuilder()
        inline_buttons.button(text=Messages.User.Panel.BUTTON_PANEL_LINK, url=panel_link_url)
        inline_buttons.button(text=Messages.User.Panel.BUTTON_GET_CONFIGS, callback_data="user:get_configs")
        inline_buttons.button(text=Messages.User.Panel.BUTTON_APPS_GUIDE, callback_data="user:show_apps")
        inline_buttons.adjust(1)
        
        await bot.send_photo(
            chat_id=target_chat_id,
            photo=BufferedInputFile(qr_code_bytes, filename="panel.png"),
            caption=caption,
            reply_markup=inline_buttons.as_markup()
        )
        logger.info(f"Successfully sent panel info photo to user {user_id}.")
        return True

    except Exception as e:
        logger.error(f"Error preparing or sending user panel for {user_id}: {e}", exc_info=True)
        try:
            await bot.send_message(target_chat_id, Messages.General.INTERNAL_ERROR_MESSAGE)
        except Exception as e2:
            logger.error(f"Failed to send error message to user {user_id}: {e2}")
        return False


# --- State Handlers ---
@router.message(StateFilter(GetUserInfoState.waiting_for_user_info), F.text)
async def process_user_info_input(message: Message, state: FSMContext, bot: Bot):
    user = message.from_user
    text = message.text
    logger.info(f"User {user.id} is in GetUserInfoState and sent text: '{text[:50]}...'")
    
    # Check membership before processing any input
    from .common_handlers import check_membership
    if not await check_membership(bot, user.id):
        logger.warning(f"User {user.id} tried to authenticate but is not a member.")
        from config import MEMBERSHIP_CHANNEL_USERNAME
        from keyboards import create_membership_keyboard
        await message.reply(
            Messages.User.Membership.REQUIRED_MESSAGE(channel_username=MEMBERSHIP_CHANNEL_USERNAME),
            reply_markup=create_membership_keyboard(MEMBERSHIP_CHANNEL_USERNAME),
            parse_mode="HTML"
        )
        await state.clear()
        return
    
    # Check if the text is a valid button click first
    valid_buttons = [
        Messages.User.Initial.BUTTON_BUY_SUB,
        Messages.User.Initial.BUTTON_GET_TRIAL,
        Messages.User.Initial.BUTTON_SUPPORT,
        Messages.User.Menu.BUTTON_MY_ACCOUNT,
        Messages.User.Menu.BUTTON_RENEW_SUB,
        Messages.User.Menu.BUTTON_GET_APPS,
        Messages.User.Menu.BUTTON_SERVER_STATUS,
        Messages.User.EmergencyConfig.USER_BUTTON_TEXT,
        Messages.User.Guide.BUTTON_TEXT  # Add guide button for unauthenticated users
    ]
    
    if text in valid_buttons:
        logger.info(f"User {user.id} clicked a valid button '{text}' while in GetUserInfoState. Clearing state and handling button.")
        await state.clear()
        
        # Handle the button click by redirecting to the main button handler
        if text == Messages.User.Initial.BUTTON_BUY_SUB:
            buy_enabled = get_bot_setting('buy_subscription_enabled', '1') == '1'
            if not buy_enabled:
                await message.reply(
                    "⚠️ فعلاً فروش بسته‌ها بسته شده است.\n\n"
                    "لطفاً بعداً به ربات مراجعه کنید و دوباره دستور /start را ارسال کنید تا منوی به‌روز شده را دریافت نمایید.",
                    reply_markup=create_user_initial_menu()
                )
                return
            logger.debug(f"User {user.id} starting 'Buy Subscription' flow from GetUserInfoState.")
            await message.answer(Messages.User.Purchase.CHOOSE_PLAN_PROMPT, reply_markup=create_plan_menu("buy_plan"))
            await state.set_state(BuySubscriptionState.waiting_for_plan_confirmation)
            
        elif text == Messages.User.Initial.BUTTON_GET_TRIAL:
            trial_enabled = get_bot_setting('trial_subscription_enabled', '1') == '1'
            if not trial_enabled:
                await message.reply(
                    "⚠️ فعلاً ارائه اشتراک تست بسته شده است.\n\n"
                    "لطفاً بعداً به ربات مراجعه کنید و دوباره دستور /start را ارسال کنید تا منوی به‌روز شده را دریافت نمایید.",
                    reply_markup=create_user_initial_menu()
                )
                return
            await get_trial_subscription(message, bot)
            
        elif text == Messages.User.Initial.BUTTON_SUPPORT:
            await message.answer(Messages.User.Support.MESSAGE, reply_markup=create_support_keyboard())
            
        elif text == Messages.User.Guide.BUTTON_TEXT:
            await handle_guide_request(message)
            
        else:
            # For other buttons that require authentication, show the authentication prompt
            await message.reply(Messages.User.Initial.UNAUTHENTICATED_PROMPT_START, reply_markup=create_user_initial_menu())
        
        return
    
    # If not a button, try to extract UUID from the input
    uuid = await extract_uuid_from_input(text)
    if not uuid:
        logger.warning(f"Could not extract UUID from input for user {user.id}.")
        await message.reply(Messages.User.Initial.PROMPT_FOR_INFO_INPUT, reply_markup=create_user_initial_menu())
        return

    logger.debug(f"Extracted UUID {uuid} for user {user.id}. Verifying with API.")
    processing_msg = await message.answer(Messages.General.PROCESSING_MESSAGE)
    success, _, _ = await fetch_user_details_api(uuid)
    
    if not success:
        logger.warning(f"UUID {uuid} provided by user {user.id} was not found on panel.")
        await processing_msg.delete()
        await message.reply(Messages.User.Panel.USER_NOT_FOUND_ON_SERVER, reply_markup=create_user_initial_menu())
        return

    logger.info(f"UUID {uuid} is valid. Authenticating user {user.id}.")
    join_time = jdatetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
    username_str = f"@{user.username}" if user.username else "NO_ID"
    auth_result = update_or_add_user(user.full_name, username_str, user.id, join_time, uuid, is_trial=0)
    
    if auth_result is None:
        # UUID is already assigned to another user
        logger.error(f"Authentication failed for user {user.id} with UUID {uuid}. UUID is already assigned to another user.")
        await processing_msg.delete()
        await message.reply(
            "⚠️ این UUID قبلاً به کاربر دیگری اختصاص داده شده است.\n\n"
            "اگر این UUID متعلق به شما است، لطفاً با پشتیبانی تماس بگیرید.\n\n"
            "اگر UUID اشتباه ارسال شده، لطفاً دوباره تلاش کنید.",
            reply_markup=create_user_initial_menu()
        )
        return
    
    logger.debug(f"Attempting to set telegram_id={user.id} on panel for UUID {uuid}.")
    await update_user_api(uuid, {"telegram_id": user.id})
    
    await state.clear()
    logger.debug(f"State cleared for user {user.id} after successful authentication.")
    await processing_msg.edit_text(Messages.User.Initial.AUTH_SUCCESS_FETCHING_INFO)
    user_data_db = get_user_from_database(user.id)
    panel_sent = await send_user_panel_info(bot, user_data_db, message=message, is_first_time_auth=True)
    
    if panel_sent:
        if message: # Only reply if we have a message context
            await message.answer(Messages.User.Panel.MENU_AFTER_INFO, reply_markup=create_user_regular_menu(user.id))
    else:
        logger.error(f"Panel info sending failed for user {user.id} even after successful auth.")
        if message: # Only reply if we have a message context
            await message.answer(Messages.User.Initial.AUTH_SUCCESS_PANEL_ERROR, reply_markup=create_user_regular_menu(user.id))


# --- Universal Text Button Router ---
@router.message(F.text, StateFilter(None))
async def handle_user_text_buttons(message: Message, state: FSMContext, bot: Bot):
    user_id = message.from_user.id
    text = message.text
    logger.info(f"User {user_id} clicked main menu button: '{text}' while in no state.")

    # Check membership before processing any text buttons
    from .common_handlers import check_membership
    if not await check_membership(bot, user_id):
        logger.warning(f"User {user_id} tried to use text button '{text}' but is not a member.")
        from config import MEMBERSHIP_CHANNEL_USERNAME
        from keyboards import create_membership_keyboard
        await message.reply(
            Messages.User.Membership.REQUIRED_MESSAGE(channel_username=MEMBERSHIP_CHANNEL_USERNAME),
            reply_markup=create_membership_keyboard(MEMBERSHIP_CHANNEL_USERNAME),
            parse_mode="HTML"
        )
        return

    user_data = get_user_from_database(user_id)
    is_authenticated = user_data and user_data[4]
    
    logger.debug(f"User {user_id} authentication status: {is_authenticated}")

    authenticated_actions = [
        Messages.User.Menu.BUTTON_MY_ACCOUNT,
        Messages.User.Menu.BUTTON_RENEW_SUB,
        Messages.User.Menu.BUTTON_GET_APPS,
        Messages.User.Menu.BUTTON_SERVER_STATUS,
    ]

    if text in authenticated_actions and not is_authenticated:
        logger.warning(f"Unauthenticated user {user_id} tried to access action '{text}'.")
        await message.reply(Messages.User.Initial.UNAUTHENTICATED_PROMPT_START, reply_markup=create_user_initial_menu())
        return

    if text == Messages.User.Initial.BUTTON_BUY_SUB:
        # Check if buy subscription is enabled
        buy_enabled = get_bot_setting('buy_subscription_enabled', '1') == '1'
        if not buy_enabled:
            logger.warning(f"User {user_id} tried to buy subscription but it's disabled.")
            await message.reply(
                "⚠️ فعلاً فروش بسته‌ها بسته شده است.\n\n"
                "لطفاً بعداً به ربات مراجعه کنید و دوباره دستور /start را ارسال کنید تا منوی به‌روز شده را دریافت نمایید.",
                reply_markup=create_user_initial_menu()
            )
            return
            
        await state.clear()
        logger.debug(f"User {user_id} starting 'Buy Subscription' flow.")

        # FIX: If user is a trial user, pass their UUID to the state for upgrade process
        if is_authenticated and get_user_trial_status(user_id):
            logger.info(f"Trial user {user_id} is buying a subscription. Storing their UUID {user_data[4]} for upgrade.")
            await state.update_data(user_uuid=user_data[4])

        await message.answer(Messages.User.Purchase.CHOOSE_PLAN_PROMPT, reply_markup=create_plan_menu("buy_plan"))
        await state.set_state(BuySubscriptionState.waiting_for_plan_confirmation)
        logger.debug(f"Set state to BuySubscriptionState.waiting_for_plan_confirmation for user {user_id}.")

    elif text == Messages.User.Menu.BUTTON_RENEW_SUB:
        await state.clear()
        logger.debug(f"User {user_id} (UUID: {user_data[4]}) starting 'Renew Subscription' flow.")
        await state.update_data(user_uuid=user_data[4])
        await message.answer(Messages.User.Renewal.CHOOSE_PLAN_PROMPT, reply_markup=create_plan_menu("renew_plan"))
        await state.set_state(RenewSubscriptionState.waiting_for_plan_confirmation)
        logger.debug(f"Set state to RenewSubscriptionState.waiting_for_plan_confirmation for user {user_id}.")

    elif text == Messages.User.Initial.BUTTON_GET_TRIAL:
        # Check if trial subscription is enabled
        trial_enabled = get_bot_setting('trial_subscription_enabled', '1') == '1'
        if not trial_enabled:
            logger.warning(f"User {user_id} tried to get trial subscription but it's disabled.")
            await message.reply(
                "⚠️ فعلاً ارائه اشتراک تست بسته شده است.\n\n"
                "لطفاً بعداً به ربات مراجعه کنید و دوباره دستور /start را ارسال کنید تا منوی به‌روز شده را دریافت نمایید.",
                reply_markup=create_user_initial_menu()
            )
            return
            
        await get_trial_subscription(message, bot)

    elif text == Messages.User.Menu.BUTTON_MY_ACCOUNT:
        await send_user_panel_info(bot, user_data, message=message)

    elif text == Messages.User.Menu.BUTTON_GET_APPS:
        await send_app_download_links(message)

    elif text == Messages.User.Menu.BUTTON_SERVER_STATUS:
        await handle_server_status_request(message)

    elif text == Messages.User.Initial.BUTTON_SUPPORT:
        await message.answer(Messages.User.Support.MESSAGE, reply_markup=create_support_keyboard())

    # NEW: Emergency Config Button
    elif text == Messages.User.EmergencyConfig.USER_BUTTON_TEXT:
        await handle_emergency_config_request(message)

    # NEW: Guide Button
    elif text == Messages.User.Guide.BUTTON_TEXT:
        await handle_guide_request(message)

    else:
        # If no button matched, try to authenticate if unauthenticated
        if not is_authenticated:
            await state.set_state(GetUserInfoState.waiting_for_user_info)
            logger.debug(f"Set state to GetUserInfoState.waiting_for_user_info for user {user_id}.")
            # Re-process this text input in the authentication handler
            await process_user_info_input(message, state, bot)
        else:
            logger.debug(f"Authenticated user {user_id} sent unrecognized text: '{text}'.")
            await message.reply("🤔 متن ارسالی شناخته نشد. لطفا از دکمه‌ها استفاده کنید.", reply_markup=create_user_regular_menu(user_id))

# ... The rest of the `user_handlers.py` file with full logging
# NOTE: To keep the response manageable, I'm pasting the complete, fully-logged `user_handlers.py` now.
# Please replace your entire file with this.

async def get_trial_subscription(message: Message, bot: Bot):
    user = message.from_user
    logger.info(f"User {user.id} requested a trial subscription.")
    
    # Check membership first
    from .common_handlers import check_membership
    if not await check_membership(bot, user.id):
        logger.warning(f"User {user.id} tried to get trial subscription but is not a member.")
        from config import MEMBERSHIP_CHANNEL_USERNAME
        from keyboards import create_membership_keyboard
        await message.reply(
            Messages.User.Membership.REQUIRED_MESSAGE(channel_username=MEMBERSHIP_CHANNEL_USERNAME),
            reply_markup=create_membership_keyboard(MEMBERSHIP_CHANNEL_USERNAME),
            parse_mode="HTML"
        )
        return
    
    # Check if trial subscription is enabled
    trial_enabled = get_bot_setting('trial_subscription_enabled', '1') == '1'
    if not trial_enabled:
        logger.warning(f"User {user.id} tried to get trial subscription but it's disabled.")
        await message.reply(
            "⚠️ فعلاً ارائه اشتراک تست بسته شده است.\n\n"
            "لطفاً بعداً به ربات مراجعه کنید و دوباره دستور /start را ارسال کنید تا منوی به‌روز شده را دریافت نمایید.",
            reply_markup=create_user_initial_menu()
        )
        return
    
    processing_msg = await message.answer(Messages.User.Trial.CHECKING_ELIGIBILITY)

    existing_sub = get_user_from_database(user.id)
    if existing_sub and existing_sub[4]:
        msg = Messages.User.Trial.ALREADY_ACTIVE if existing_sub[5] else Messages.User.Trial.ALREADY_HAS_PAID_SUB
        logger.warning(f"User {user.id} trial request rejected: {msg}")
        await processing_msg.edit_text(msg)
        return

    trial_payload = { 
        "name": f"Trial_{user.id}", 
        "comment": f"Trial account for TG:{user.id} requested on {datetime.date.today()}", 
        "package_days": 1, 
        "usage_limit_GB": 1, 
        "telegram_id": user.id, 
        "enable": True, 
        "mode": "no_reset" 
    }
    logger.debug(f"Sending trial creation payload for user {user.id}: {trial_payload}")
    success, data, _ = await add_user_api(trial_payload)
    if not success:
        logger.error(f"API failed to create trial for user {user.id}. Response: {data}")
        await processing_msg.edit_text(Messages.User.Trial.API_ERROR_CREATE(error_data=html_escape(data)))
        return

    try:
        trial_uuid = json.loads(data).get('uuid')
        if not trial_uuid: raise KeyError("UUID not in response")
        logger.info(f"Trial created for user {user.id} with UUID {trial_uuid}.")
        
        join_time = jdatetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
        username_str = f"@{user.username}" if user.username else "NO_ID"
        trial_result = update_or_add_user(user.full_name, username_str, user.id, join_time, trial_uuid, is_trial=1)
        
        if trial_result is None:
            # This should be very rare since trial UUIDs are newly generated, but handle it just in case
            logger.error(f"Failed to store trial user {user.id} with UUID {trial_uuid} in database. Possible UUID conflict.")
            await processing_msg.edit_text("خطا در ذخیره اطلاعات کاربر. لطفاً دوباره تلاش کنید.")
            return
        
        await processing_msg.edit_text(Messages.User.Trial.ACTIVATED_SUCCESS)
        user_data_db = get_user_from_database(user.id)
        if await send_user_panel_info(bot, user_data_db, message=message, is_first_time_auth=True):
            await message.answer(Messages.User.Panel.MENU_AFTER_INFO, reply_markup=create_user_regular_menu(user.id))
        else:
            await message.answer(Messages.User.Trial.PANEL_INFO_ERROR_POST_CREATE, reply_markup=create_user_initial_menu())
    except (json.JSONDecodeError, KeyError) as e:
        logger.error(f"Error processing trial API response for user {user.id}: {e}", exc_info=True)
        await processing_msg.edit_text(Messages.User.Trial.API_INVALID_RESPONSE)

async def send_app_download_links(message: Message):
    logger.info(f"Sending app download links to user {message.from_user.id}.")
    builder = InlineKeyboardBuilder()
    builder.button(text=Messages.User.Apps.BUTTON_HIDDIFY, url="https://t.me/mbnvpnhelp/7")
    builder.button(text=Messages.User.Apps.BUTTON_V2RAYNG_ANDROID, url="https://t.me/mbnvpnhelp/5")
    builder.button(text=Messages.User.Apps.BUTTON_V2RAYN_WINDOWS, url="https://t.me/mbnvpnhelp/9")
    builder.button(text=Messages.User.Apps.BUTTON_STREISAND_IOS, url="https://t.me/mbnvpnhelp/11")
    builder.button(text=Messages.User.Apps.BUTTON_HAPP_IOS, url="https://apps.apple.com/us/app/happ-proxy-utility/id6504287215")
    builder.button(text=Messages.User.Apps.BUTTON_GUIDE_CHANNEL, url="https://t.me/mbnvpnhelp")
    builder.adjust(1)
    await message.answer(Messages.User.Apps.GUIDE_TITLE, reply_markup=builder.as_markup(), disable_web_page_preview=True)

async def handle_server_status_request(message: Message):
    logger.info(f"User {message.from_user.id} requested server status.")
    processing_msg = await message.answer(Messages.User.ServerStatus.CHECKING)
    
    # Check API status
    logger.debug("Checking API server status...")
    success, api_response, status_code = await get_server_status()
    logger.debug(f"API status check result: success={success}, status_code={status_code}")
    
    # DISABLED: Ping functionality temporarily disabled
    # Reason: Bot runs on the same server as the panel, so ping always shows 0ms or localhost ping
    # This doesn't provide meaningful information to users about external connectivity
    """
    # Check ping status with more detailed logging
    logger.debug(f"Checking ping to server: {IP_SERVER}")
    if not IP_SERVER:
        logger.error("IP_SERVER is not configured!")
        ping_ms = None
    else:
        ping_ms = await ping_server(IP_SERVER)
        logger.info(f"Ping result for {IP_SERVER}: {ping_ms} ms")
    """
    
    # Determine status display
    status_emoji, status_text = ("✅", "آنلاین") if success else ("❌", "آفلاین")
    
    # DISABLED: Ping display temporarily disabled
    """
    # Handle ping display
    if ping_ms is None:
        ping_text = "ناموفق"
        logger.warning(f"Ping failed for {IP_SERVER}")
    elif ping_ms == 0:
        ping_text = "<1ms"  # Show as very fast instead of 0
        logger.warning(f"Ping returned 0ms for {IP_SERVER} - showing as <1ms")
    else:
        ping_text = f"{ping_ms}ms"
    """
    
    logger.debug(f"Final server status for user: API Online={success}")
    
    # Create status message without ping information
    status_message = f"""وضعیت سرور:

{status_emoji} وضعیت پنل: {status_text}"""
    
    await processing_msg.edit_text(status_message, parse_mode="Markdown")

# NEW: Emergency Config Handler
async def handle_emergency_config_request(message: Message):
    """Handle user request for emergency config"""
    user_id = message.from_user.id
    logger.info(f"User {user_id} requested emergency config.")
    
    # Check if user is authenticated and not a trial user
    user_data = get_user_from_database(user_id)
    is_authenticated = user_data and user_data[4]  # Has UUID
    
    if not is_authenticated:
        logger.warning(f"Unauthenticated user {user_id} tried to access emergency config.")
        await message.reply(Messages.User.Initial.UNAUTHENTICATED_PROMPT_START, reply_markup=create_user_initial_menu())
        return
    
    # Check if user is trial user
    is_trial = get_user_trial_status(user_id)
    if is_trial:
        logger.warning(f"Trial user {user_id} tried to access emergency config.")
        await message.reply("⚠️ دکمه کانفیگ اضطراری فقط برای کاربرانی که اشتراک پرداختی دارند قابل استفاده است.")
        return
    
    # Import here to avoid circular imports
    from database import get_emergency_config  # type: ignore
    
    config = get_emergency_config()
    if not config:
        await message.answer(Messages.User.EmergencyConfig.USER_NO_CONFIG)
        return
    
    try:
        # Send the emergency message based on its type
        media_type = config.get('media_type')
        media_file_id = config.get('media_file_id')
        message_text = config.get('message_text', '')
        media_caption = config.get('media_caption', '')
        
        if media_type and media_file_id:
            # Send media with caption
            if media_type == 'photo':
                await message.answer_photo(
                    photo=media_file_id,
                    caption=media_caption or message_text
                )
            elif media_type == 'video':
                await message.answer_video(
                    video=media_file_id,
                    caption=media_caption or message_text
                )
            elif media_type == 'document':
                await message.answer_document(
                    document=media_file_id,
                    caption=media_caption or message_text
                )
            elif media_type == 'voice':
                await message.answer_voice(voice=media_file_id)
                if message_text:
                    await message.answer(message_text)
            elif media_type == 'audio':
                await message.answer_audio(
                    audio=media_file_id,
                    caption=media_caption or message_text
                )
            elif media_type == 'animation':
                await message.answer_animation(
                    animation=media_file_id,
                    caption=media_caption or message_text
                )
            elif media_type == 'sticker':
                await message.answer_sticker(sticker=media_file_id)
                if message_text:
                    await message.answer(message_text)
        else:
            # Send text only
            await message.answer(message_text)
            
        logger.info(f"Emergency config sent successfully to user {user_id}")
        
    except Exception as e:
        logger.error(f"Error sending emergency config to user {user_id}: {e}", exc_info=True)
        await message.answer("خطایی در دریافت پیام اضطراری رخ داد.")

# --- Payment Flow Callbacks & Photo Handlers ---
@router.callback_query(F.data.startswith("buy_plan:") | F.data.startswith("renew_plan:"))
async def handle_plan_selection(callback_query: CallbackQuery, state: FSMContext):
    user_id = callback_query.from_user.id
    logger.info(f"User {user_id} selected a plan with callback: {callback_query.data}")
    
    # Check membership first
    from .common_handlers import check_membership
    if not await check_membership(callback_query.bot, user_id):
        logger.warning(f"User {user_id} tried to select plan but is not a member.")
        from config import MEMBERSHIP_CHANNEL_USERNAME
        from keyboards import create_membership_keyboard
        await callback_query.answer("برای استفاده از ربات باید در کانال عضو شوید", show_alert=True)
        await callback_query.message.edit_text(
            Messages.User.Membership.REQUIRED_MESSAGE(channel_username=MEMBERSHIP_CHANNEL_USERNAME),
            reply_markup=create_membership_keyboard(MEMBERSHIP_CHANNEL_USERNAME),
            parse_mode="HTML"
        )
        return
    
    action_type, plan_id = callback_query.data.split(":", 1)
    plan = get_plan_details(plan_id)
    if not plan or not plan.get('price'):
        logger.error(f"User {user_id} selected an invalid plan ID: {plan_id}")
        await callback_query.answer(Messages.User.Purchase.PLAN_SELECTION_INVALID, show_alert=True)
        return

    # Check if user already has a UUID (should be renewal instead of new purchase)
    user_db_data = get_user_from_database(user_id)
    user_uuid = None
    if user_db_data and user_db_data[4]:  # User has UUID
        user_uuid = user_db_data[4]
        if user_db_data[5] == 1:  # is_trial flag
            logger.info(f"User {user_id} has trial subscription, treating as upgrade to paid")
            action_type = "buy_plan"  # Trial upgrade is still treated as buy
        else:
            logger.info(f"User {user_id} has existing subscription (UUID: {user_uuid}), treating as renewal")
            action_type = "renew_plan"  # Force renewal for existing paid users

    # Store user_uuid in state data for renewal processing
    state_update = {
        "plan_id": plan_id,
        "action_type": action_type,
        **plan
    }
    if user_uuid:
        state_update["user_uuid"] = user_uuid

    await state.update_data(**state_update)
    
    # Delete the plan selection message entirely to remove both message and buttons
    await callback_query.message.delete()
    
    # Remove the reply keyboard when plan is selected
    await callback_query.message.answer(
        "✅ " + Messages.User.Purchase.PLAN_SELECTED_CONFIRMATION(description=plan['desc']),
        reply_markup=ReplyKeyboardRemove()
    )
    
    next_state = BuySubscriptionState.waiting_for_receipt_photo if action_type == "buy_plan" else RenewSubscriptionState.waiting_for_receipt_photo
    await state.set_state(next_state)
    logger.debug(f"Set state to {await state.get_state()} for user {user_id}.")
    
    # Get dynamic bank account info
    bank_name = get_bot_setting('bank_account_name', get_bank_account_info()['name'])
    bank_card = get_bot_setting('bank_card_number', get_bank_account_info()['card_number'])
    
    price_formatted = f"{plan['price']:,} تومان"
    bank_info_msg = (
        Messages.User.Purchase.BANK_INFO_TITLE +
        Messages.User.Purchase.BANK_INFO_AMOUNT_PAYABLE(price_formatted) +
        Messages.User.Purchase.BANK_INFO_ACCOUNT_NAME(bank_name) +
        Messages.User.Purchase.BANK_INFO_CARD_NUMBER(bank_card) +
        Messages.User.Purchase.BANK_INFO_COPY_NOTE +
        Messages.User.Purchase.BANK_INFO_PAY_EXACT_AMOUNT +
        Messages.User.Purchase.BANK_INFO_SEND_RECEIPT_PHOTO
    )
    cancel_cb = "user:cancel_buy" if action_type == "buy_plan" else "user:cancel_renewal"
    await callback_query.message.answer(bank_info_msg, parse_mode="Markdown", reply_markup=create_cancel_button(cancel_cb))

@router.message(StateFilter(BuySubscriptionState.waiting_for_receipt_photo, RenewSubscriptionState.waiting_for_receipt_photo), F.photo)
async def handle_receipt_photo(message: Message, state: FSMContext, bot: Bot):
    user_id = message.from_user.id
    logger.info(f"User {user_id} sent a receipt photo.")
    
    # 1. Send processing message
    processing_msg = await message.answer(Messages.User.Purchase.RECEIPT_PROCESSING_ADMIN)
    
    # Check membership first
    from .common_handlers import check_membership
    if not await check_membership(bot, user_id):
        logger.warning(f"User {user_id} tried to send receipt photo but is not a member.")
        from config import MEMBERSHIP_CHANNEL_USERNAME
        from keyboards import create_membership_keyboard
        await message.reply(
            Messages.User.Membership.REQUIRED_MESSAGE(channel_username=MEMBERSHIP_CHANNEL_USERNAME),
            reply_markup=create_membership_keyboard(MEMBERSHIP_CHANNEL_USERNAME),
            parse_mode="HTML"
        )
        await state.clear()
        return
    
    state_data = await state.get_data()
    # Get action_type from state data, with proper fallback logic
    action_type = state_data.get("action_type", "buy_plan")

    # Convert action_type to the format expected by send_receipt_to_admin_for_approval
    if action_type == "buy_plan":
        action_type = "buy"
    elif action_type == "renew_plan":
        action_type = "renew"
    
    identifier = state_data.get('user_uuid') if action_type in ["renew", "buy"] and state_data.get('user_uuid') else f"new_user_{user_id}"
    if not identifier:
        logger.critical(f"Identifier is missing for receipt handling for user {user_id}. State: {state_data}")
        await message.reply(Messages.General.INTERNAL_ERROR_MESSAGE)
        await state.clear()
        return

    try:
        # FIX: Download the photo into a BytesIO object before saving
        photo_bytes_io = await bot.download(message.photo[-1], destination=BytesIO())
        
        receipt_filename = await save_receipt_photo(photo_bytes_io, identifier)
        if not receipt_filename:
            raise IOError("Failed to save receipt file.")
            
        await state.update_data(
            receipt_filename=receipt_filename,
            action_type=action_type,
            user_id=user_id,
            telegram_username=message.from_user.username,
            telegram_fullname=message.from_user.full_name
        )
        
        # 2. Delete the processing message
        await processing_msg.delete()
        
        # 3. Send success message to user
        await message.answer(Messages.User.Purchase.RECEIPT_SENT_SUCCESS_USER)
        
        # 4. Send receipt to admin
        from .admin_handlers import send_receipt_to_admin_for_approval
        current_data = await state.get_data()
        await send_receipt_to_admin_for_approval(bot, current_data)
        
    except Exception as e:
        logger.error(f"Error processing receipt for user {user_id}: {e}", exc_info=True)
        # Also delete processing message on error
        await processing_msg.delete()
        await message.answer(Messages.User.Purchase.RECEIPT_USER_NOTIFY_ERROR)
        
    finally:
        # Clear state for both buy and renew flows
        await state.clear()
        # The message "If you want to renew another plan..." is now removed.

# NEW: Handle menu button clicks during plan selection
@router.message(StateFilter(BuySubscriptionState.waiting_for_plan_confirmation, RenewSubscriptionState.waiting_for_plan_confirmation), F.text)
async def handle_menu_buttons_during_plan_selection(message: Message, state: FSMContext, bot: Bot):
    """Handle menu button clicks while user is in plan selection state"""
    user_id = message.from_user.id
    text = message.text
    current_state_str = await state.get_state()
    logger.info(f"User {user_id} clicked button '{text}' while in plan selection state: {current_state_str}")
    
    # Check membership before processing buttons
    from .common_handlers import check_membership
    if not await check_membership(bot, user_id):
        logger.warning(f"User {user_id} tried to use plan selection buttons but is not a member.")
        from config import MEMBERSHIP_CHANNEL_USERNAME
        from keyboards import create_membership_keyboard
        await message.reply(
            Messages.User.Membership.REQUIRED_MESSAGE(channel_username=MEMBERSHIP_CHANNEL_USERNAME),
            reply_markup=create_membership_keyboard(MEMBERSHIP_CHANNEL_USERNAME),
            parse_mode="HTML"
        )
        await state.clear()
        return
    
    user_data = get_user_from_database(user_id)
    is_authenticated = user_data and user_data[4]
    
    # Handle specific menu button actions
    if text == Messages.User.Menu.BUTTON_MY_ACCOUNT and is_authenticated:
        # Don't clear state, just show account info
        await send_user_panel_info(bot, user_data, message=message)
        return
        
    elif text == Messages.User.Menu.BUTTON_GET_APPS:
        await send_app_download_links(message)
        return
        
    elif text == Messages.User.Menu.BUTTON_SERVER_STATUS:
        await handle_server_status_request(message)
        return
        
    elif text == Messages.User.Initial.BUTTON_SUPPORT:
        await message.answer(Messages.User.Support.MESSAGE, reply_markup=create_support_keyboard())
        return
        
    elif text == Messages.User.EmergencyConfig.USER_BUTTON_TEXT and is_authenticated:
        await handle_emergency_config_request(message)
        return
        
    elif text == Messages.User.Guide.BUTTON_TEXT:
        await handle_guide_request(message)
        return
        
    # Handle subscription-related buttons by clearing state and redirecting
    elif text == Messages.User.Initial.BUTTON_BUY_SUB:
        # Clear current state and start new buy flow
        await state.clear()
        buy_enabled = get_bot_setting('buy_subscription_enabled', '1') == '1'
        if not buy_enabled:
            await message.reply(
                "⚠️ فعلاً فروش بسته‌ها بسته شده است.",
                reply_markup=create_user_initial_menu() if not is_authenticated else create_user_regular_menu(user_id)
            )
            return
        await message.answer(Messages.User.Purchase.CHOOSE_PLAN_PROMPT, reply_markup=create_plan_menu("buy_plan"))
        await state.set_state(BuySubscriptionState.waiting_for_plan_confirmation)
        return
        
    elif text == Messages.User.Menu.BUTTON_RENEW_SUB and is_authenticated:
        # Clear current state and start new renewal flow
        await state.clear()
        await state.update_data(user_uuid=user_data[4])
        await message.answer(Messages.User.Renewal.CHOOSE_PLAN_PROMPT, reply_markup=create_plan_menu("renew_plan"))
        await state.set_state(RenewSubscriptionState.waiting_for_plan_confirmation)
        return
        
    elif text == Messages.User.Initial.BUTTON_GET_TRIAL:
        # Clear current state and start trial flow
        await state.clear()
        trial_enabled = get_bot_setting('trial_subscription_enabled', '1') == '1'
        if not trial_enabled:
            await message.reply(
                "⚠️ فعلاً ارائه اشتراک تست بسته شده است.",
                reply_markup=create_user_initial_menu() if not is_authenticated else create_user_regular_menu(user_id)
            )
            return
        await get_trial_subscription(message, bot)
        return
    
    # If button wasn't recognized, guide user
    await message.reply(
        "💡 شما در حال انتخاب پلن هستید. لطفاً از دکمه‌های شیشه‌ای بالا برای انتخاب پلن استفاده کنید.\n\n"
        "اگر می‌خواهید انصراف دهید، می‌توانید از دکمه‌های منو استفاده کنید."
    )

@router.message(StateFilter(BuySubscriptionState.waiting_for_receipt_photo, RenewSubscriptionState.waiting_for_receipt_photo))
async def handle_wrong_content_for_receipt(message: Message):
    logger.warning(f"User {message.from_user.id} sent wrong content type ({message.content_type}) instead of photo.")
    await message.reply(Messages.User.Purchase.WRONG_CONTENT_FOR_RECEIPT)

@router.callback_query(F.data.in_({"user:cancel_buy", "user:cancel_renewal"}))
async def handle_cancel_payment(callback_query: CallbackQuery, state: FSMContext):
    user_id = callback_query.from_user.id
    logger.info(f"User {user_id} cancelled payment flow with callback: {callback_query.data}")
    is_buy = callback_query.data == "user:cancel_buy"
    user_data = get_user_from_database(user_id)
    is_authenticated = user_data and user_data[4]
    
    # Determine the correct menu based on user status
    if is_authenticated:
        # For authenticated users (both trial and paid), always use regular menu
        markup = create_user_regular_menu(user_id)
    else:
        # For unauthenticated users, use initial menu
        markup = create_user_initial_menu()
    
    msg = Messages.User.Purchase.PROCESS_CANCELLED if is_buy else Messages.User.Renewal.PROCESS_CANCELLED
    
    await state.clear()
    logger.debug(f"State cleared for user {user_id} after cancelling payment.")
    
    # Delete the bank info message with cancel button
    await callback_query.message.delete()
    
    # For renewal cancellation, show the plan selection menu again
    if not is_buy:
        await callback_query.message.answer(
            f"{msg}\n\n" + Messages.User.Renewal.CHOOSE_PLAN_PROMPT,
            reply_markup=create_plan_menu("renew_plan")
        )
    else:
        # For buy cancellation, just show cancellation message
        await callback_query.message.answer(msg)
    
    await callback_query.message.answer(Messages.General.BACK_TO_MAIN_MENU_MESSAGE, reply_markup=markup)

@router.callback_query(F.data == "user:show_apps")
async def handle_show_apps_guide_callback(callback_query: CallbackQuery):
    await callback_query.answer()
    await send_app_download_links(callback_query.message)

@router.callback_query(F.data.startswith("user:clear_state_and_menu:"))
async def handle_clear_state_and_menu(callback_query: CallbackQuery, state: FSMContext):
    """Clear user's FSM state after admin rejection and show appropriate menu"""
    user_id = callback_query.from_user.id
    action_type = callback_query.data.split(":")[-1]
    
    logger.info(f"Clearing FSM state for user {user_id} after admin rejection (action: {action_type})")
    
    # Clear the user's state
    await state.clear()
    
    # Determine appropriate menu
    user_data = get_user_from_database(user_id)
    is_authenticated = user_data and user_data[4]
    
    if action_type == "buy":
        # For buy rejections, user might not be authenticated yet
        markup = create_user_initial_menu()
        menu_text = "منو اصلی"
    else:
        # For renewal rejections, user should be authenticated
        markup = create_user_regular_menu(user_id) if is_authenticated else create_user_initial_menu()
        menu_text = "منو کاربری"
    
    # Delete the rejection message and send new one with proper reply keyboard
    await callback_query.message.delete()
    await callback_query.message.answer(
        f"✅ بازگشت به {menu_text}\n\n"
        "حالا می‌توانید از دکمه‌های زیر استفاده کنید:",
        reply_markup=markup
    )
    
    # For renewal rejections, also show the plan selection menu again
    if action_type == "renew":
        await callback_query.message.answer(
            "💡 می‌توانید دوباره پلن مورد نظر خود را انتخاب کنید:",
            reply_markup=create_plan_menu("renew_plan")
        )
    
    await callback_query.answer("منو بازیابی شد")
    logger.info(f"State cleared and menu restored for user {user_id}")

# Removed smart button handlers - now using URL buttons with deep links


# NOTE: Smart button handlers removed as we now use URL buttons with deep links


# --- NEW: User Guide Handlers ---

async def handle_guide_request(message: Message):
    """Handle main guide request from text button"""
    user_id = message.from_user.id
    logger.info(f"User {user_id} requested user guide.")
    
    # Check authentication status
    user_data = get_user_from_database(user_id)
    is_authenticated = user_data and user_data[4]  # Has UUID
    
    if is_authenticated:
        # Show authenticated user guide
        guide_text = (
            Messages.User.Guide.AUTHENTICATED_TITLE +
            Messages.User.Guide.AUTHENTICATED_INTRO
        )
    else:
        # Show unauthenticated user guide
        guide_text = (
            Messages.User.Guide.UNAUTHENTICATED_TITLE +
            Messages.User.Guide.UNAUTHENTICATED_INTRO
        )
    
    # Create appropriate guide keyboard
    guide_keyboard = create_user_guide_keyboard(is_authenticated)
    
    await message.answer(guide_text, reply_markup=guide_keyboard, parse_mode="HTML")
    logger.debug(f"Sent guide main menu to user {user_id} (authenticated: {is_authenticated})")


@router.callback_query(F.data.startswith("guide:"))
async def handle_guide_callbacks(callback_query: CallbackQuery):
    """Handle guide section callbacks"""
    user_id = callback_query.from_user.id
    action = callback_query.data.split(":", 1)[1]
    logger.info(f"User {user_id} clicked guide action: {action}")
    
    # Check authentication status for content customization
    user_data = get_user_from_database(user_id)
    is_authenticated = user_data and user_data[4]  # Has UUID
    
    if action == "back_to_guide":
        # Return to main guide menu
        await callback_query.answer()
        
        if is_authenticated:
            guide_text = (
                Messages.User.Guide.AUTHENTICATED_TITLE +
                Messages.User.Guide.AUTHENTICATED_INTRO
            )
        else:
            guide_text = (
                Messages.User.Guide.UNAUTHENTICATED_TITLE +
                Messages.User.Guide.UNAUTHENTICATED_INTRO
            )
        
        guide_keyboard = create_user_guide_keyboard(is_authenticated)
        await callback_query.message.edit_text(guide_text, reply_markup=guide_keyboard, parse_mode="HTML")
        
    elif action == "back_to_menu":
        # Return to user's main menu
        await callback_query.answer("بازگشت به منوی اصلی")
        
        # Delete the guide message
        await callback_query.message.delete()
        
        if is_authenticated:
            menu_text = "🏠 بازگشت به منوی کاربری\n\nاز دکمه‌های زیر استفاده کنید:"
            markup = create_user_regular_menu(user_id)
        else:
            menu_text = "🏠 بازگشت به منوی اصلی\n\nاز دکمه‌های زیر استفاده کنید:"
            markup = create_user_initial_menu()
        
        # Send new message with reply keyboard instead of editing
        await callback_query.message.answer(menu_text, reply_markup=markup)
        
    # Guide section handlers
    elif action == "authentication":
        await callback_query.answer()
        await callback_query.message.edit_text(
            Messages.User.Guide.AUTH_TITLE + Messages.User.Guide.AUTH_CONTENT,
            reply_markup=create_guide_back_keyboard(),
            parse_mode="HTML"
        )
        
    elif action == "buy_subscription":
        await callback_query.answer()
        await callback_query.message.edit_text(
            Messages.User.Guide.BUY_TITLE + Messages.User.Guide.BUY_CONTENT,
            reply_markup=create_guide_back_keyboard(),
            parse_mode="HTML"
        )
        
    elif action == "trial_subscription":
        await callback_query.answer()
        await callback_query.message.edit_text(
            Messages.User.Guide.TRIAL_TITLE + Messages.User.Guide.TRIAL_CONTENT,
            reply_markup=create_guide_back_keyboard(),
            parse_mode="HTML"
        )
        
    elif action == "support_contact":
        await callback_query.answer()
        await callback_query.message.edit_text(
            Messages.User.Guide.SUPPORT_TITLE + Messages.User.Guide.SUPPORT_CONTENT,
            reply_markup=create_guide_back_keyboard(),
            parse_mode="HTML"
        )
        
    # Authenticated user sections
    elif action == "account_info":
        if not is_authenticated:
            await callback_query.answer("⚠️ این بخش فقط برای کاربران احراز هویت شده است", show_alert=True)
            return
        await callback_query.answer()
        await callback_query.message.edit_text(
            Messages.User.Guide.ACCOUNT_TITLE + Messages.User.Guide.ACCOUNT_CONTENT,
            reply_markup=create_guide_back_keyboard(),
            parse_mode="HTML"
        )
        
    elif action == "renewal":
        if not is_authenticated:
            await callback_query.answer("⚠️ این بخش فقط برای کاربران احراز هویت شده است", show_alert=True)
            return
        await callback_query.answer()
        await callback_query.message.edit_text(
            Messages.User.Guide.RENEWAL_TITLE + Messages.User.Guide.RENEWAL_CONTENT,
            reply_markup=create_guide_back_keyboard(),
            parse_mode="HTML"
        )
        
    elif action == "apps_download":
        if not is_authenticated:
            await callback_query.answer("⚠️ این بخش فقط برای کاربران احراز هویت شده است", show_alert=True)
            return
        await callback_query.answer()
        await callback_query.message.edit_text(
            Messages.User.Guide.APPS_TITLE + Messages.User.Guide.APPS_CONTENT,
            reply_markup=create_guide_back_keyboard(),
            parse_mode="HTML"
        )
        
    elif action == "server_status":
        if not is_authenticated:
            await callback_query.answer("⚠️ این بخش فقط برای کاربران احراز هویت شده است", show_alert=True)
            return
        await callback_query.answer()
        await callback_query.message.edit_text(
            Messages.User.Guide.SERVER_TITLE + Messages.User.Guide.SERVER_CONTENT,
            reply_markup=create_guide_back_keyboard(),
            parse_mode="HTML"
        )
        
    elif action == "emergency_config":
        if not is_authenticated:
            await callback_query.answer("⚠️ این بخش فقط برای کاربران احراز هویت شده است", show_alert=True)
            return
        await callback_query.answer()
        await callback_query.message.edit_text(
            Messages.User.Guide.EMERGENCY_TITLE + Messages.User.Guide.EMERGENCY_CONTENT,
            reply_markup=create_guide_back_keyboard(),
            parse_mode="HTML"
        )
        
    elif action == "troubleshooting":
        if not is_authenticated:
            await callback_query.answer("⚠️ این بخش فقط برای کاربران احراز هویت شده است", show_alert=True)
            return
        await callback_query.answer()
        await callback_query.message.edit_text(
            Messages.User.Guide.TROUBLESHOOTING_TITLE + Messages.User.Guide.TROUBLESHOOTING_CONTENT,
            reply_markup=create_guide_back_keyboard(),
            parse_mode="HTML"
        )
        
    else:
        await callback_query.answer("عملیات نامشخص", show_alert=True)
        logger.warning(f"Unknown guide action: {action} from user {user_id}")

    logger.debug(f"Handled guide callback {action} for user {user_id}")

# --- NEW: Notification Callback Handlers ---

@router.callback_query(F.data.in_({
    "buy_plan_trial_warning", "view_plans_trial_warning",
    "renew_plan_reminder", "view_plans_renewal"
}))
async def handle_notification_callbacks(callback_query: CallbackQuery, state: FSMContext):
    """Handle callbacks from notification buttons (trial warnings and renewal reminders)"""
    user_id = callback_query.from_user.id
    action = callback_query.data
    logger.info(f"User {user_id} clicked notification callback: {action}")
    
    await callback_query.answer()
    await state.clear()  # Clear any existing state
    
    if action in ["buy_plan_trial_warning", "view_plans_trial_warning"]:
        # Handle trial warning callbacks - redirect to buy subscription
        buy_enabled = get_bot_setting('buy_subscription_enabled', '1') == '1'
        if not buy_enabled:
            await callback_query.message.answer(
                "⚠️ فعلاً فروش بسته‌ها بسته شده است.\n\n"
                "لطفاً بعداً به ربات مراجعه کنید.",
                reply_markup=create_user_initial_menu()
            )
            return
            
        logger.debug(f"User {user_id} starting 'Buy Subscription' flow from trial notification.")
        await callback_query.message.answer(
            "🛒 <b>خرید اشتراک از طریق نوتیفیکیشن</b>\n\n" + Messages.User.Purchase.CHOOSE_PLAN_PROMPT, 
            reply_markup=create_plan_menu("buy_plan"),
            parse_mode="HTML"
        )
        await state.set_state(BuySubscriptionState.waiting_for_plan_confirmation)
        
    elif action in ["renew_plan_reminder", "view_plans_renewal"]:
        # Handle renewal reminder callbacks - redirect to renewal
        user_data = get_user_from_database(user_id)
        if not user_data or not user_data[4]:  # No UUID
            await callback_query.message.answer(
                "⚠️ برای تمدید اشتراک ابتدا باید احراز هویت شوید.",
                reply_markup=create_user_initial_menu()
            )
            return
            
        logger.debug(f"User {user_id} starting 'Renew Subscription' flow from notification.")
        await state.update_data(user_uuid=user_data[4])
        await callback_query.message.answer(
            "🔄 <b>تمدید اشتراک از طریق نوتیفیکیشن</b>\n\n" + Messages.User.Renewal.CHOOSE_PLAN_PROMPT,
            reply_markup=create_plan_menu("renew_plan"),
            parse_mode="HTML"
        )
        await state.set_state(RenewSubscriptionState.waiting_for_plan_confirmation)

@router.callback_query(F.data == "user:get_configs")
async def handle_get_configs(callback_query: CallbackQuery, state: FSMContext):
    user_id = callback_query.from_user.id
    logger.info(f"User {user_id} requested configs via inline button")
    from database import get_user_from_database
    user_data = get_user_from_database(user_id)
    if not user_data or not user_data[4]:
        await callback_query.answer("❌ ابتدا باید احراز هویت کنید.", show_alert=True)
        return
    uuid = user_data[4]
    await callback_query.answer()
    await callback_query.message.answer(Messages.User.Panel.CONFIGS_FETCHING)
    success, data, status = await fetch_user_configs_api(uuid)
    if not success:
        await callback_query.message.answer(Messages.User.Panel.CONFIGS_ERROR(error=str(data)), parse_mode="HTML")
        return
    try:
        # data is expected list of dicts with 'name' and 'link'
        
        # Filter configs: keep only direct config links (vless, ss, etc.) by excluding http links
        filtered_configs = [cfg for cfg in data if not cfg.get('link', '').startswith('http')]

        lines = []
        for idx, cfg in enumerate(filtered_configs, start=1):
            name = cfg.get('name', f'Config {idx}')
            link = cfg.get('link', '')
            lines.append(f"🔹 <b>{idx}. {name}</b>\n<code>{link}</code>")
        
        if not lines:
            msg_text = "کانفیگ مستقیمی یافت نشد. می‌توانید از لینک اشتراک کلی در پیام اطلاعات اکانت استفاده کنید."
        else:
            msg_text = "\n\n".join(lines)
            
        await callback_query.message.answer(msg_text, parse_mode="HTML")
    except Exception as e:
        logger.error(f"Error formatting configs for user {user_id}: {e}")
        await callback_query.message.answer(Messages.User.Panel.CONFIGS_ERROR(error=str(e)), parse_mode="HTML")

# --- FALLBACK HANDLERS (Must be at the end to catch unhandled events) ---

@router.message()
async def handle_unhandled_messages(message: Message, state: FSMContext):
    """Fallback handler for all unhandled messages"""
    user_id = message.from_user.id
    text = message.text or f"[{message.content_type}]"
    logger.warning(f"Unhandled message from user {user_id}: '{text[:50]}...' (content_type: {message.content_type})")
    
    # Clear any stuck states
    current_state = await state.get_state()
    if current_state:
        await state.clear()
        logger.info(f"Cleared stuck state '{current_state}' for user {user_id}")
    
    # Check authentication and provide appropriate menu
    user_data = get_user_from_database(user_id)
    is_authenticated = user_data and user_data[4]
    
    if is_authenticated:
        response_text = "🤔 پیام شناخته نشد. لطفا از دکمه‌های منو استفاده کنید:"
        markup = create_user_regular_menu(user_id)
    else:
        response_text = "🤔 پیام شناخته نشد. لطفا از دکمه‌های منو استفاده کنید یا برای احراز هویت UUID خود را ارسال کنید:"
        markup = create_user_initial_menu()
    
    await message.answer(response_text, reply_markup=markup)


@router.callback_query()
async def handle_unhandled_callbacks(callback_query: CallbackQuery, state: FSMContext):
    """Fallback handler for all unhandled callback queries"""
    user_id = callback_query.from_user.id
    data = callback_query.data or "unknown"
    logger.warning(f"Unhandled callback from user {user_id}: '{data}'")
    
    # Clear any stuck states
    current_state = await state.get_state()
    if current_state:
        await state.clear()
        logger.info(f"Cleared stuck state '{current_state}' for user {user_id}")
    
    # Answer the callback to prevent loading
    await callback_query.answer("🤔 عملیات شناخته نشد", show_alert=True)
    
    # Check authentication and provide appropriate menu
    user_data = get_user_from_database(user_id)
    is_authenticated = user_data and user_data[4]
    
    if is_authenticated:
        response_text = "🔄 لطفا از دکمه‌های منوی زیر استفاده کنید:"
        markup = create_user_regular_menu(user_id)
    else:
        response_text = "🔄 لطفا از دکمه‌های منوی زیر استفاده کنید:"
        markup = create_user_initial_menu()
    
    try:
        await callback_query.message.edit_text(response_text)
        await callback_query.message.answer("📱 منوی شما:", reply_markup=markup)
    except Exception as e:
        logger.warning(f"Could not edit callback message for user {user_id}: {e}")
        await callback_query.message.answer(response_text, reply_markup=markup)