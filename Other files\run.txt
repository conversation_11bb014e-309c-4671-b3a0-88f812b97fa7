sudo apt update
sudo apt install python3-venv
cd Hiddy2
python3 -m venv .venv
source .venv/bin/activate
pip install --upgrade -r requirements.txt
python3 bot.py

sudo apt update
sudo apt install python3-venv
cd Hiddy2
source .venv/bin/activate
pip install --upgrade -r requirements.txt
python3 bot.py

cd Hiddy2
source .venv/bin/activate
python3 bot.py

deactivate

nohup python hiddybot.py > output.log 2>&1 &

pkill -f hiddybot.py

   ps aux | grep python
   kill PID

sudo nano /etc/systemd/system/hiddy-bot.service

V1.

[Unit]
Description=Hiddybot Service
After=network.target

[Service]
ExecStart=/root/hb/venv/bin/python /root/hb/hiddybot.py
WorkingDirectory=/root/hb
StandardOutput=append:/root/hb/output.log
StandardError=append:/root/hb/output.log
Restart=always
User=root
Group=root

[Install]
WantedBy=multi-user.target

V2.

[Unit]
Description=Hiddy Telegram Bot
After=network-online.target
Wants=network-online.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root/Hiddy2
Environment=PATH=/root/Hiddy2/.venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
Environment=PYTHONPATH=/root/Hiddy2
ExecStart=/root/Hiddy2/.venv/bin/python bot.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target

sudo systemctl daemon-reload
sudo systemctl enable hiddy-bot.service
sudo systemctl start hiddy-bot.service
sudo journalctl -u hiddy-bot.service -f

sudo systemctl restart hiddy-bot.service
sudo journalctl -u hiddy-bot.service -f

sudo systemctl disable hiddy-bot.service
sudo systemctl stop hiddy-bot.service
sudo systemctl daemon-reload

# مشاهده لاگ‌ها
sudo journalctl -u hiddy-bot.service -f

# مشاهده لاگ‌های اخیر
sudo journalctl -u hiddy-bot.service --since today

# بررسی وضعیت کامل
sudo systemctl status hiddy-bot.service -l

# مشاهده ارورها
sudo journalctl -u hiddy-bot.service --no-pager

# مشاهده لاگ‌های real-time
sudo journalctl -u hiddy-bot.service -f

# اگر فایل‌ها مشکل permission دارند
sudo chown -R root:root /root/Hiddy2
sudo chmod +x /root/Hiddy2/venv/bin/python

# مشاهده همه سرویس‌های فعال
sudo systemctl list-units --type=service --state=active | grep hiddy

# بررسی اینکه سرویس در حال اجراست یا نه
sudo systemctl is-active hiddy-bot.service

# بررسی اینکه سرویس enable است یا نه
sudo systemctl is-enabled hiddy-bot.service