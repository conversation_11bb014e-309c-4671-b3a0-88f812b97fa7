# hiddy_bot/handlers/admin_handlers.py

import logging
import asyncio
import json
import datetime
import re
import os
import shutil
from html import escape as html_escape
import urllib.parse
from typing import Union, Tuple
from io import BytesIO

from aiogram import Router, F, Bot, types # type: ignore
from aiogram.fsm.context import FSMContext # type: ignore
from aiogram.types import Message, CallbackQuery, ReplyKeyboardRemove, BufferedInputFile, InlineKeyboardButton, ContentType # type: ignore
from aiogram.filters import StateFilter, Command # type: ignore
from aiogram.exceptions import TelegramBadRequest, TelegramRetryAfter, TelegramForbiddenError # type: ignore
from aiogram.utils.keyboard import InlineKeyboardBuilder # type: ignore
import jdatetime # type: ignore
import qrcode # type: ignore

from messages import Messages, get_plan_details
from states import *
from keyboards import (  # type: ignore
    create_user_management_menu,
    create_plans_list_keyboard,
    create_plan_edit_keyboard,
    create_cancel_button,
    create_confirmation_keyboard,
    create_main_menu,  # Added for fallback handlers
    create_user_regular_menu,
    create_button_management_menu,
    create_emergency_config_menu,
    create_plan_management_menu,
    create_user_initial_menu,
    create_inline_buttons_count_keyboard,
    create_plan_field_edit_keyboard
)
from config import ADMIN_USER_ID, TELEGRAM_REPORT_CHAT, PROJECT_DIR, PANEL_LINK_PREFIX, HIDDIFY_API_DOMAIN, HIDDIFY_API_USER_PROXY_PATH, HIDDIFY_API_PROXY_PATH, TELEGRAM_INFO_CHAT
from database import *
from api_hiddify import *
from ssh_utils import fetch_server_stats_ssh, fetch_latest_backup_ssh
from utils import format_ssh_server_info, format_server_status, _generate_qr_code_bytes, send_subscription_qrcode, save_receipt_photo

router = Router()
# فیلتر برای اینکه این مدیریت‌کننده‌ها فقط برای ادمین اجرا شوند
router.message.filter(F.from_user.id == ADMIN_USER_ID)
router.callback_query.filter(F.from_user.id == ADMIN_USER_ID)

logger = logging.getLogger(__name__)


# --- Helper function for creating detailed comments ---
def create_comment_with_history(action_type: str, plan_description: str, admin_id: int, user_id: int = None, old_comment: str = None) -> str:
    """
    Create a detailed comment for the latest action, ignoring previous history to prevent database errors.
    """
    current_time = jdatetime.datetime.now().strftime("%Y/%m/%d %H:%M")
    
    new_comment_line = ""
    if action_type == "trial":
        new_comment_line = f"Trial account for TG:{user_id} requested on {datetime.date.today()}"
    elif action_type == "purchase":
        new_comment_line = f"Purchased '{plan_description}' via bot. Admin: {admin_id} on {current_time}"
    elif action_type == "upgrade_from_trial":
        new_comment_line = f"Upgraded from trial to '{plan_description}'. Admin: {admin_id} on {current_time}"
    elif action_type == "renewal":
        new_comment_line = f"Renewed to '{plan_description}'. Admin: {admin_id} on {current_time}"
    elif action_type == "manual_add":
        new_comment_line = f"Manual user creation. Admin: {admin_id} on {current_time}"
    
    # Return ONLY the latest action to prevent data length errors in the database.
    return new_comment_line


# --- Receipt Approval Function ---
async def send_receipt_to_admin_for_approval(bot: Bot, state_data: dict):
    """Sends receipt photo to admin for approval with buy/renew action buttons"""
    admin_telegram_id = ADMIN_USER_ID
    receipt_filename = state_data.get("receipt_filename")
    user_id = state_data.get("user_id")
    user_uuid = state_data.get("user_uuid")
    action_type = state_data.get("action_type", "buy")
    plan_id = state_data.get("plan_id", "unknown")
    plan_desc = state_data.get("desc", "نامشخص")
    plan_price = state_data.get("price", 0)
    tg_username = state_data.get("telegram_username")
    tg_fullname = state_data.get("telegram_fullname", "NO_NAME")

    logger.info(
        f"Sending receipt for {action_type} approval to admin {admin_telegram_id}. User: {user_id}, Plan: {plan_desc}"
    )

    if not receipt_filename or not user_id or not plan_id or plan_id == "unknown":
        logger.error(
            f"Receipt filename, user_id or plan_id missing in state_data for admin approval. User ID: {user_id}, Filename: {receipt_filename}, Plan ID: {plan_id}"
        )
        if admin_telegram_id:
            try:
                await bot.send_message(admin_telegram_id, f"⚠️ اطلاعات ناقص برای تایید فیش کاربر {user_id}")
            except Exception as e:
                logger.error(f"Failed to send missing info error to admin: {e}")
        return

    # FIX: Use the exact same logic as in handle_receipt_photo to determine the identifier
    identifier = state_data.get('user_uuid') if action_type in ["renew", "buy"] and state_data.get('user_uuid') else f"new_user_{user_id}"

    if not identifier:
        logger.error(f"Identifier missing for {action_type} approval. User ID: {user_id}, UUID: {user_uuid}")
        if admin_telegram_id:
            try:
                await bot.send_message(admin_telegram_id, f"⚠️ شناسه ناقص برای تایید {action_type} کاربر {user_id}")
            except Exception as e:
                logger.error(f"Failed to send missing identifier error to admin: {e}")
        return

    receipt_filepath = os.path.join(PROJECT_DIR, "bank", str(identifier), receipt_filename)
    logger.info(f"Attempting to read receipt file: {receipt_filepath}")

    try:
        with open(receipt_filepath, "rb") as f:
            photo_data = f.read()

        time_now = jdatetime.datetime.now().strftime("%Y/%m/%d %H:%M")
        price_formatted = f"{plan_price:,} تومان"

        safe_fullname = html_escape(tg_fullname)
        safe_username = html_escape(tg_username if tg_username else "NO_ID")
        safe_plan_desc = html_escape(plan_desc)
        safe_plan_id = html_escape(plan_id)

        # Correctly label the request as "Buy" even for a trial user upgrading
        caption_title = "🆕 درخواست خرید جدید" if action_type == "buy" else "🔄 درخواست تمدید"
        caption = f"{caption_title}\n\n"
        caption += f"👤 نام: {safe_fullname}\n"
        caption += f"🆔 یوزرنیم: {safe_username}\n"
        caption += f"📱 آیدی عددی: <code>{user_id}</code>\n"
        
        if user_uuid: # Show UUID if it exists (for renewals or trial upgrades)
            safe_uuid = html_escape(user_uuid)
            caption += f"🔑 UUID فعلی: <code>{safe_uuid}</code>\n"
            
        caption += f"📦 پلن: {safe_plan_desc} (ID: {safe_plan_id})\n"
        caption += f"💰 مبلغ: {price_formatted}\n"
        caption += f"⏰ زمان ارسال: {time_now}\n\n"
        caption += "⚡️ لطفاً تصمیم خود را اتخاذ کنید:"

        markup = InlineKeyboardBuilder()
        confirm_callback_data = f"admin_confirm_buy:{user_id}:{plan_id}" if action_type == "buy" else f"admin_confirm_renew:{user_id}:{plan_id}"
        reject_callback_data = f"admin_reject:{action_type}:{user_id}"

        markup.button(text="✅ تایید", callback_data=confirm_callback_data)
        markup.button(text="❌ رد", callback_data=reject_callback_data)
        markup.adjust(2)

        await bot.send_photo(
            chat_id=admin_telegram_id,
            photo=types.BufferedInputFile(photo_data, filename=receipt_filename),
            caption=caption,
            parse_mode="HTML",
            reply_markup=markup.as_markup(),
        )
        logger.info(f"Receipt and approval request sent to admin for user {user_id}.")

    except FileNotFoundError:
        logger.error(f"Receipt file not found at path: {receipt_filepath}")
        if admin_telegram_id:
            try:
                await bot.send_message(admin_telegram_id, f"⚠️ فایل فیش کاربر {user_id} یافت نشد:\n<code>{html_escape(receipt_filepath)}</code>", parse_mode="HTML")
            except Exception as e:
                logger.error(f"Failed to send file not found error to admin: {e}")
    except Exception as e:
        logger.error(f"Error sending receipt to admin for user {user_id}: {e}", exc_info=True)
        if admin_telegram_id:
            try:
                await bot.send_message(admin_telegram_id, f"⚠️ خطای نامشخص در ارسال فیش کاربر {user_id}: {e}")
            except Exception as e_notify:
                logger.error(f"Failed to send generic error notification to admin: {e_notify}")

# --- Admin Menu Text Button Router ---
@router.message(F.text.in_([
    Messages.Admin.Menu.BUTTON_USER_MANAGEMENT,
    Messages.Admin.Menu.BUTTON_SERVER_STATS,
    Messages.Admin.Menu.BUTTON_GET_BACKUP,
    Messages.Admin.Menu.BUTTON_SEND_MESSAGE_USERS,
    Messages.Admin.IncomeStatus.BUTTON_TEXT,
    Messages.Admin.ButtonManagement.BUTTON_TEXT,
    Messages.Admin.EmergencyConfig.BUTTON_TEXT,
    Messages.Admin.PlanManagement.BUTTON_TEXT,
    "🗄️ مدیریت JSON Database",
    Messages.Admin.Menu.BUTTON_PANEL_REPORT
]), StateFilter(None))
async def handle_admin_text_buttons(message: Message, state: FSMContext, bot: Bot):
    admin_id = message.from_user.id
    text = message.text
    logger.info(f"Admin {admin_id} clicked main menu button: '{text}' while in no state.")
    await state.clear()
    
    if text == Messages.Admin.Menu.BUTTON_USER_MANAGEMENT:
        logger.debug(f"Admin {admin_id} navigating to User Management.")
        await message.answer(Messages.Admin.UserManagement.TITLE, reply_markup=create_user_management_menu(), parse_mode="HTML")
    
    elif text == Messages.Admin.Menu.BUTTON_SERVER_STATS:
        logger.debug(f"Admin {admin_id} requesting server stats.")
        processing_msg = await message.answer(Messages.Admin.ServerStats.FETCHING)
        ssh_task = fetch_server_stats_ssh()
        api_task = get_server_status()
        
        logger.debug("Gathering SSH and API stat tasks.")
        results = await asyncio.gather(ssh_task, api_task, return_exceptions=True)
        logger.debug("Stat tasks finished.")
        
        ssh_success, ssh_stats = results[0] if not isinstance(results[0], Exception) else (False, str(results[0]))
        api_success, api_stats_str, api_status = results[1] if not isinstance(results[1], Exception) else (False, str(results[1]), None)
        
        ssh_msg = format_ssh_server_info(ssh_stats) if ssh_success else Messages.Admin.ServerStats.SSH_ERROR(error=str(ssh_stats))
        api_msg = format_server_status(api_stats_str) if api_success else Messages.Admin.ServerStats.API_ERROR(error=api_stats_str)
        
        await processing_msg.delete()
        await message.answer(f"{ssh_msg}\n---\n{api_msg}")
    
    elif text == Messages.Admin.Menu.BUTTON_GET_BACKUP:
        logger.debug(f"Admin {admin_id} triggered backup menu.")
        builder = InlineKeyboardBuilder()
        builder.button(text="⚡️ بکاپ لحظه‌ای", callback_data="admin:backup:instant")
        builder.button(text="🕓 آخرین بکاپ اتوماتیک پنل", callback_data="admin:backup:latest")
        builder.button(text="⏰ زمان‌بندی بکاپ لحظه‌ای", callback_data="admin:backup:schedule")
        builder.adjust(1)
        await message.answer("لطفا نوع بکاپ مورد نظر را انتخاب کنید:", reply_markup=builder.as_markup())
    
    elif text == Messages.Admin.Menu.BUTTON_SEND_MESSAGE_USERS:
        logger.debug(f"Admin {admin_id} navigating to Send Message options.")
        builder = InlineKeyboardBuilder()
        builder.button(text=Messages.Admin.SendMessage.BUTTON_TO_ALL, callback_data="admin:send_msg:all")
        builder.button(text=Messages.Admin.SendMessage.BUTTON_TO_SINGLE_UUID, callback_data="admin:send_msg:uuid")
        builder.button(text=Messages.Admin.SendMessage.BUTTON_SELECT_FROM_LIST, callback_data="admin:list_users:regular:send_msg:1")
        builder.adjust(1)
        await message.answer(Messages.Admin.SendMessage.OPTIONS_TITLE, reply_markup=builder.as_markup())
    
    elif text == Messages.Admin.IncomeStatus.BUTTON_TEXT:
        logger.debug(f"Admin {admin_id} requesting comprehensive income status.")
        processing_msg = await message.answer(Messages.Admin.IncomeStatus.FETCHING_STATS)
        
        try:
            # Get comprehensive income and user statistics
            stats = await get_comprehensive_income_stats()
            
            # Build the income status message
            income_message = Messages.Admin.IncomeStatus.TITLE
            
            # Income Statistics
            income_message += Messages.Admin.IncomeStatus.DAILY_INCOME.format(amount=f"{stats['daily_income']:,}")
            income_message += Messages.Admin.IncomeStatus.MONTHLY_INCOME_MTD.format(amount=f"{stats['monthly_income']:,}")
            income_message += Messages.Admin.IncomeStatus.SIX_MONTHLY_INCOME_ROLLING.format(amount=f"{stats['six_month_income']:,}")
            income_message += Messages.Admin.IncomeStatus.YEARLY_INCOME_YTD.format(amount=f"{stats['yearly_income']:,}")
            
            # User Statistics
            income_message += Messages.Admin.IncomeStatus.RENEWALS_THIS_MONTH_COUNT.format(count=stats['renewals_this_month'])
            income_message += Messages.Admin.IncomeStatus.NEW_PURCHASES_THIS_MONTH_COUNT.format(count=stats['new_purchases_this_month'])
            income_message += Messages.Admin.IncomeStatus.DUE_FOR_RENEWAL_THIS_MONTH_COUNT.format(count=stats['due_for_renewal'])
            income_message += Messages.Admin.IncomeStatus.EXPIRING_SOON_NEXT_7_DAYS_COUNT.format(count=stats['expiring_soon'])
            income_message += Messages.Admin.IncomeStatus.TOTAL_ACTIVE_USERS_PANEL_COUNT.format(count=stats['total_active_users'])
            
            if stats['projected_monthly_income'] > 0:
                income_message += Messages.Admin.IncomeStatus.PROJECTED_MONTHLY_INCOME.format(amount=f"{stats['projected_monthly_income']:,}")
            
            # Create interactive buttons for detailed views
            builder = InlineKeyboardBuilder()
            if stats['renewals_this_month'] > 0:
                builder.button(text=Messages.Admin.IncomeStatus.BUTTON_SHOW_RENEWED_THIS_MONTH, 
                             callback_data="admin:income:show_renewed")
            if stats['new_purchases_this_month'] > 0:
                builder.button(text=Messages.Admin.IncomeStatus.BUTTON_SHOW_NEW_PURCHASES_THIS_MONTH, 
                             callback_data="admin:income:show_new_purchases")
            if stats['due_for_renewal'] > 0:
                builder.button(text=Messages.Admin.IncomeStatus.BUTTON_SHOW_DUE_RENEWAL_THIS_MONTH, 
                             callback_data="admin:income:show_due_renewal")
            if stats['expiring_soon'] > 0:
                builder.button(text=Messages.Admin.IncomeStatus.BUTTON_SHOW_EXPIRING_SOON_7_DAYS, 
                             callback_data="admin:income:show_expiring_soon")
            
            builder.adjust(1)
            
            await processing_msg.delete()
            await message.answer(income_message, reply_markup=builder.as_markup(), parse_mode="HTML")
            
        except Exception as e:
            logger.error(f"Error generating income statistics: {e}", exc_info=True)
            await processing_msg.edit_text(Messages.Admin.IncomeStatus.ERROR_CALCULATING)

    # NEW: Button Management
    elif text == Messages.Admin.ButtonManagement.BUTTON_TEXT:
        logger.debug(f"Admin {admin_id} navigating to Button Management.")
        await message.answer(Messages.Admin.ButtonManagement.TITLE, reply_markup=create_button_management_menu(), parse_mode="HTML")
    
    # NEW: Emergency Config  
    elif text == Messages.Admin.EmergencyConfig.BUTTON_TEXT:
        logger.debug(f"Admin {admin_id} navigating to Emergency Config.")
        await message.answer(Messages.Admin.EmergencyConfig.ADMIN_TITLE, reply_markup=create_emergency_config_menu(), parse_mode="HTML")
    
    # NEW: Plan Management
    elif text == Messages.Admin.PlanManagement.BUTTON_TEXT:
        logger.debug(f"Admin {admin_id} navigating to Plan Management.")
        await message.answer(Messages.Admin.PlanManagement.TITLE, reply_markup=create_plan_management_menu(), parse_mode="HTML")
    
    # NEW: JSON Database Management
    elif text == "🗄️ مدیریت JSON Database":
        logger.debug(f"Admin {admin_id} navigating to JSON Database Management.")
        await handle_json_database_management(message)

    # DEBUG: Check renewal method
    elif text == "/check_renewal":
        current_method = get_renewal_method()
        method_names = {1: "ریست کامل", 2: "تجمیع کامل", 3: "ریست روز، تجمیع حجم", 4: "تجمیع روز، ریست حجم"}
        await message.reply(f"روش تمدید فعلی: {current_method} ({method_names.get(current_method, 'نامشخص')})")

    elif text == Messages.Admin.Menu.BUTTON_PANEL_REPORT:
        logger.debug(f"Admin {admin_id} requested full panel report.")
        processing_msg = await message.answer(Messages.Admin.PanelReport.FETCHING)
        # Gather data concurrently
        ssh_task = fetch_server_stats_ssh()
        api_task = get_server_status()
        income_task = get_comprehensive_income_stats()
        results = await asyncio.gather(ssh_task, api_task, income_task, return_exceptions=True)

        # Process SSH stats
        ssh_success, ssh_stats = results[0] if not isinstance(results[0], Exception) else (False, str(results[0]))
        ssh_msg = format_ssh_server_info(ssh_stats) if ssh_success else Messages.Admin.ServerStats.SSH_ERROR(error=str(ssh_stats))

        # Process API stats
        api_success, api_stats_str, api_status = results[1] if not isinstance(results[1], Exception) else (False, str(results[1]), None)
        api_msg = format_server_status(api_stats_str) if api_success else Messages.Admin.ServerStats.API_ERROR(error=api_stats_str)

        # Process income stats
        income_stats = results[2] if not isinstance(results[2], Exception) else None
        income_msg = ""
        try:
            if income_stats:
                M = Messages.Admin.IncomeStatus
                income_msg += M.TITLE
                income_msg += M.DAILY_INCOME.format(amount=f"{income_stats['daily_income']:,}")
                income_msg += M.MONTHLY_INCOME_MTD.format(amount=f"{income_stats['monthly_income']:,}")
                income_msg += M.SIX_MONTHLY_INCOME_ROLLING.format(amount=f"{income_stats['six_month_income']:,}")
                income_msg += M.YEARLY_INCOME_YTD.format(amount=f"{income_stats['yearly_income']:,}")
                income_msg += M.PROJECTED_MONTHLY_INCOME.format(amount=f"{income_stats['projected_monthly_income']:,}")
                income_msg += M.RENEWALS_THIS_MONTH_COUNT.format(count=income_stats['renewals_this_month'])
                income_msg += M.NEW_PURCHASES_THIS_MONTH_COUNT.format(count=income_stats['new_purchases_this_month'])
                income_msg += M.DUE_FOR_RENEWAL_THIS_MONTH_COUNT.format(count=income_stats['due_for_renewal'])
                income_msg += M.EXPIRING_SOON_NEXT_7_DAYS_COUNT.format(count=income_stats['expiring_soon'])
                income_msg += M.TOTAL_ACTIVE_USERS_PANEL_COUNT.format(count=income_stats['total_active_users'])
            else:
                income_msg = "❌ خطا در محاسبه آمار درآمدی."
        except Exception as e:
            logger.error(f"Error building income section in report: {e}")
            income_msg = "❌ خطا در نمایش آمار درآمدی."

        full_report = (
            "📊 <b>گزارش جامع پنل</b>\n\n" +
            ssh_msg + "\n---\n" +
            api_msg + "\n---\n" +
            income_msg
        )
        await processing_msg.delete()
        await message.answer(full_report, parse_mode="HTML")


# --- User Management Actions ---

# [FIXED] Handler for "Update Usage" Button
@router.callback_query(F.data == "admin:update_usage")
async def handle_update_usage(callback_query: CallbackQuery):
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} clicked 'Update Usage' button.")
    
    await callback_query.answer(Messages.Admin.UpdateUsage.PROMPT_INITIATE)
    await callback_query.message.edit_text(Messages.Admin.UpdateUsage.PROCESSING, reply_markup=None)
    
    success, response_data = await update_user_usage_api()
    
    if success:
        try:
            # Try to parse the message from the API response
            msg_from_api = json.loads(response_data).get("message", "Success")
            final_message = Messages.Admin.UpdateUsage.SUCCESS(msg=msg_from_api)
        except (json.JSONDecodeError, AttributeError):
            final_message = Messages.Admin.UpdateUsage.SUCCESS(msg=str(response_data))
        logger.info(f"User usage update successful. API response: {response_data}")
    else:
        final_message = Messages.Admin.UpdateUsage.API_ERROR(response=html_escape(str(response_data)))
        logger.error(f"User usage update failed. API response: {response_data}")

    await callback_query.message.edit_text(final_message, parse_mode="HTML")
    await callback_query.message.answer(Messages.Admin.UserList.BACK_TO_MGMT, reply_markup=create_user_management_menu())


# --- User Management List & Pagination ---
@router.callback_query(F.data.startswith("admin:list_users:"))
async def list_users_callback(callback_query: CallbackQuery):
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} triggered list_users_callback with data: {callback_query.data}")
    try:
        _, _, user_type, menu_type, page_str = callback_query.data.split(":")
        page = int(page_str)
        logger.debug(f"Parsed callback: user_type={user_type}, menu_type={menu_type}, page={page}")
    except (ValueError, IndexError) as e:
        logger.error(f"Invalid callback data in list_users_callback: {callback_query.data}", exc_info=True)
        return
    
    await callback_query.answer("در حال بروزرسانی لیست...")
    success, users_or_error = await fetch_panel_users()
    
    if success:
        logger.debug("Users fetched from panel successfully, storing in DB.")
        await store_panel_users_in_db(users_or_error)
        await show_panel_users_paged(callback_query.message, page, menu_type, user_type)
    else:
        logger.error(f"Failed to fetch users from panel: {users_or_error}")
        await callback_query.message.edit_text(Messages.Admin.UserList.API_FETCH_ERROR(error=str(users_or_error)))

async def show_panel_users_paged(message: Message, page: int, menu_type: str, user_type: str):
    logger.debug(f"Showing paged user list. Page: {page}, Menu: {menu_type}, Type: {user_type}")
    users_per_page = 9
    users = get_panel_users_page(page, users_per_page, user_type)
    total_users = get_total_panel_users_count(user_type)
    total_pages = (total_users + users_per_page - 1) // users_per_page or 1
    
    if not users and page > 1:
        logger.warning(f"Page {page} is empty, redirecting to last page {total_pages}.")
        page = total_pages
        users = get_panel_users_page(page, users_per_page, user_type)

    list_name = "کاربران دائمی" if user_type == "regular" else "کاربران تستی"
    if not users:
        logger.warning(f"No users of type '{user_type}' found to display.")
        await message.edit_text(Messages.Admin.UserList.NO_USERS_FOUND(list_name=list_name), reply_markup=create_user_management_menu())
        return

    builder = InlineKeyboardBuilder()
    for uuid, name in users:
        display_name = name[:25] + "..." if len(name) > 25 else name
        
        if menu_type == "send_msg":
            callback_data = f"admin:send_msg:select_user:{uuid}"
            button_text = f"✉️ {display_name}"
        else: # Default is "edit"
            callback_data = f"u_v:{uuid}:{user_type}"
            button_text = f"⚙️ {display_name}"
            
        builder.button(text=button_text, callback_data=callback_data)

    builder.adjust(2)
    
    nav_row = [
        InlineKeyboardButton(text="⬅️", callback_data=f"admin:list_users:{user_type}:{menu_type}:{page-1}") if page > 1 else InlineKeyboardButton(text=" ", callback_data="noop"),
        InlineKeyboardButton(text=f"صفحه {page}/{total_pages}", callback_data="noop"),
        InlineKeyboardButton(text="➡️", callback_data=f"admin:list_users:{user_type}:{menu_type}:{page+1}") if page < total_pages else InlineKeyboardButton(text=" ", callback_data="noop")
    ]
    builder.row(*nav_row)
    
    # Add first/last page buttons if there are more than 3 pages
    if total_pages > 3:
        extra_nav_row = []
        if page > 2:
            extra_nav_row.append(InlineKeyboardButton(text=Messages.Admin.UserList.PAGINATION_FIRST, callback_data=f"admin:list_users:{user_type}:{menu_type}:1"))
        if page < total_pages - 1:
            extra_nav_row.append(InlineKeyboardButton(text=Messages.Admin.UserList.PAGINATION_LAST, callback_data=f"admin:list_users:{user_type}:{menu_type}:{total_pages}"))
        if extra_nav_row:
            builder.row(*extra_nav_row)
    
    builder.row(InlineKeyboardButton(text="🔙 بازگشت به مدیریت", callback_data="admin:back_to_usermgmt"))
    
    await message.edit_text(f"👤 **لیست {list_name}**", reply_markup=builder.as_markup(), parse_mode="Markdown")

@router.callback_query(F.data == "admin:back_to_usermgmt")
async def back_to_user_mgmt_callback(callback_query: CallbackQuery):
    logger.info(f"Admin {callback_query.from_user.id} clicked 'Back to User Management'.")
    await callback_query.answer()
    await callback_query.message.edit_text(Messages.Admin.UserManagement.TITLE, reply_markup=create_user_management_menu(), parse_mode="HTML")


# --- User Info Display & Edit ---
@router.callback_query(F.data.startswith("u_v:"))
async def view_user_info_callback(callback_query: CallbackQuery, bot: Bot):
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} triggered view_user_info_callback with data: {callback_query.data}")
    try:
        parts = callback_query.data.split(":")
        uuid = parts[1]
        user_list_type = parts[2] if len(parts) > 2 else 'regular'
        logger.debug(f"Parsed view request for UUID: {uuid}, from list type: {user_list_type}")
        await callback_query.answer("در حال دریافت اطلاعات کاربر...")
        await display_user_info_for_edit(callback_query, uuid, user_list_type, bot)
    except Exception as e:
        logger.error(f"Error in view_user_info_callback: {e}", exc_info=True)
        await callback_query.answer("خطای داخلی رخ داد.", show_alert=True)

async def display_user_info_for_edit(callback_or_message: Union[CallbackQuery, Message], uuid: str, user_list_type: str, bot: Bot, is_from_message: bool = False):
    message_to_handle = callback_or_message if is_from_message else callback_or_message.message
    logger.debug(f"Displaying info for UUID {uuid}. Is from message: {is_from_message}")
    
    if is_from_message:
        sent_message = await message_to_handle.answer(Messages.Admin.UserInfoEdit.FETCHING_DETAILS)
    else:
        await message_to_handle.edit_text(Messages.Admin.UserInfoEdit.FETCHING_DETAILS)
        sent_message = message_to_handle

    success, details_str, _ = await fetch_user_details_api(uuid)

    if not success:
        logger.error(f"Failed to fetch details for {uuid} in display_user_info_for_edit. Error: {details_str}")
        await sent_message.edit_text(Messages.Admin.UserInfoEdit.API_FETCH_ERROR(user_identifier=uuid, error_msg=details_str))
        return
        
    try:
        user_dict = json.loads(details_str)
        start_date_str = user_dict.get('start_date')
        start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d').date() if start_date_str else datetime.date.today()
        package_days = user_dict.get('package_days', 0)
        expiry_date = start_date + datetime.timedelta(days=package_days)
        remaining_days = max(0, (expiry_date - datetime.date.today()).days)
        jalali_expiry = jdatetime.date.fromgregorian(date=expiry_date).strftime('%Y/%m/%d')
        
        usage_limit_gb = user_dict.get('usage_limit_GB', 0.0)
        current_usage_gb = user_dict.get('current_usage_GB', 0.0)
        remaining_usage_gb = max(0.0, usage_limit_gb - current_usage_gb)

        tg_id_str = user_dict.get('telegram_id')
        tg_id = int(tg_id_str) if tg_id_str and str(tg_id_str).isdigit() else None
        
        id_source = ""
        tg_username = "N/A"
        if tg_id:
            id_source = "(Panel)"
            tg_info_local = get_user_from_database(tg_id)
            if tg_info_local and tg_info_local[1] and tg_info_local[1] != "NO_ID":
                tg_username = tg_info_local[1]
        else:
            tg_info_local = get_user_from_database_by_uuid(uuid)
            if tg_info_local:
                tg_id = tg_info_local[2]
                id_source = "(Local DB)"
                if tg_info_local[1] and tg_info_local[1] != "NO_ID":
                    tg_username = tg_info_local[1]

        base_user_url = f"https://{HIDDIFY_API_DOMAIN}/{HIDDIFY_API_USER_PROXY_PATH}/{uuid}"
        panel_link_url = f"{base_user_url}/?home=true"
        sub_link_name_encoded = urllib.parse.quote(user_dict.get('name', ''))
        sub_link_url = f"{base_user_url}/auto/?asn=unknown#{sub_link_name_encoded}"

        info_message = Messages.Admin.UserInfoEdit.TITLE_DISPLAY(name=html_escape(user_dict.get('name', ''))) + \
                       Messages.Admin.UserInfoEdit.COMMENT(comment=html_escape(user_dict.get('comment', '-'))) + \
                       Messages.Admin.UserInfoEdit.UUID(uuid=uuid) + \
                       Messages.Admin.UserInfoEdit.STATUS(status_text="فعال ✅" if user_dict.get('enable') else "غیرفعال ❌") + \
                       Messages.Admin.UserInfoEdit.TELEGRAM_NUM_ID(telegram_id=tg_id or "N/A", id_source=id_source) + \
                       Messages.Admin.UserInfoEdit.TELEGRAM_USERNAME(telegram_username=tg_username.replace("@", "")) + \
                       Messages.Admin.UserInfoEdit.USAGE_LIMIT(usage_limit_gb=usage_limit_gb) + \
                       Messages.Admin.UserInfoEdit.USAGE_CURRENT(current_usage_gb=current_usage_gb) + \
                       Messages.Admin.UserInfoEdit.REMAINING_USAGE(remaining_usage_gb=remaining_usage_gb) + \
                       Messages.Admin.UserInfoEdit.PACKAGE_DAYS(package_days=package_days) + \
                       Messages.Admin.UserInfoEdit.EXPIRY_DATE(expiry_date_str=jalali_expiry) + \
                       Messages.Admin.UserInfoEdit.REMAINING_DAYS(remaining_days_str=f"{remaining_days} روز") + \
                       Messages.Admin.UserInfoEdit.RESET_MODE(reset_mode=user_dict.get('mode', '-')) + \
                       Messages.Admin.UserInfoEdit.PANEL_LINK(panel_link=panel_link_url) + \
                       Messages.Admin.UserInfoEdit.SUB_LINK(sub_link=sub_link_url)
        
        builder = InlineKeyboardBuilder()
        builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_EDIT_NAME, callback_data=f"u_e:n:{uuid}:{user_list_type}")
        builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_EDIT_COMMENT, callback_data=f"u_e:c:{uuid}:{user_list_type}")
        builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_EDIT_USAGE_LIMIT, callback_data=f"u_e:ugb:{uuid}:{user_list_type}")
        builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_EDIT_PACKAGE_DAYS, callback_data=f"u_e:pd:{uuid}:{user_list_type}")
        builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_EDIT_MODE, callback_data=f"u_e:m:{uuid}:{user_list_type}")
        builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_EDIT_TELEGRAM_ID, callback_data=f"u_e:tid:{uuid}:{user_list_type}")
        builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_RESET_DAYS, callback_data=f"u_r:d:{uuid}:{user_list_type}")
        builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_RESET_USAGE, callback_data=f"u_r:u:{uuid}:{user_list_type}")
        builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_SHOW_SUB_QR, callback_data=f"u_qr:{uuid}:{user_list_type}")
        
        # Only show detach telegram button if user has telegram_id
        if tg_id:
            builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_DETACH_TELEGRAM, callback_data=f"u_detach_tg:{uuid}:{user_list_type}")
        
        if user_dict.get('enable'):
            builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_DISABLE_USER, callback_data=f"u_tgl:{uuid}:{user_list_type}")
        else:
            builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_ENABLE_USER, callback_data=f"u_tgl:{uuid}:{user_list_type}")
        builder.button(text=Messages.Admin.UserInfoEdit.BUTTON_DELETE_USER, callback_data=f"u_del_p:{uuid}:{user_list_type}")
        
        back_button_cb = f"admin:list_users:{user_list_type}:edit:1" if not is_from_message else "admin:back_to_usermgmt"
        back_button_text = Messages.Admin.UserInfoEdit.BUTTON_BACK_TO_LIST if not is_from_message else Messages.Admin.UserInfoEdit.BUTTON_BACK_TO_MANAGEMENT
        builder.button(text=back_button_text, callback_data=back_button_cb)
        
        builder.adjust(2)
        
        await sent_message.edit_text(info_message, reply_markup=builder.as_markup(), disable_web_page_preview=True, parse_mode="HTML")
        logger.debug(f"Successfully displayed info for UUID {uuid}.")

    except Exception as e:
        logger.error(f"Error parsing or displaying user details for admin: {e}", exc_info=True)
        await sent_message.edit_text(Messages.Admin.UserInfoEdit.API_PARSE_ERROR)


# --- FSM Handlers for Editing User Fields ---
@router.callback_query(F.data.startswith("u_e:") | F.data.startswith("u_r:"))
async def prompt_for_edit_value(callback_query: CallbackQuery, state: FSMContext):
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} triggered prompt_for_edit_value with data: {callback_query.data}")
    try:
        action_abbr, field_abbr, uuid, user_list_type = callback_query.data.split(":")
        
        field_map = {
            'n': 'name', 'c': 'comment', 'ugb': 'usage_limit_GB', 'pd': 'package_days', 
            'm': 'mode', 'tid': 'telegram_id', 'd': 'days', 'u': 'usage'
        }
        field_name = field_map.get(field_abbr)
        if not field_name:
            logger.error(f"Unknown field abbreviation '{field_abbr}' in callback.")
            return

        if field_name == "mode":
            logger.debug(f"Showing mode selection keyboard for UUID {uuid}.")
            modes_builder = InlineKeyboardBuilder()
            for mode in ["no_reset", "monthly", "weekly", "daily"]:
                modes_builder.button(text=mode, callback_data=f"u_set_m:{mode}:{uuid}:{user_list_type}")
            modes_builder.adjust(2)
            modes_builder.row(InlineKeyboardButton(text=Messages.Buttons.CANCEL, callback_data=f"u_v:{uuid}:{user_list_type}"))
            await callback_query.message.edit_text(Messages.Admin.EditField.MODE_PROMPT(user_uuid=uuid), reply_markup=modes_builder.as_markup())
        else:
            await state.set_state(EditUserState.waiting_for_new_value)
            await state.update_data(
                action_type=action_abbr,
                field_name=field_name,
                uuid=uuid,
                user_list_type=user_list_type,
                original_message_id=callback_query.message.message_id
            )
            logger.debug(f"Set state to EditUserState.waiting_for_new_value for admin {admin_id}. Field: {field_name}")
            prompt_map = {
                "name": Messages.Admin.EditField.PROMPT_GENERIC_FIELD("نام", uuid),
                "comment": Messages.Admin.EditField.PROMPT_GENERIC_FIELD("یادداشت", uuid),
                "usage_limit_GB": Messages.Admin.EditField.PROMPT_GENERIC_FIELD("حجم (GB)", uuid),
                "package_days": Messages.Admin.EditField.PROMPT_GENERIC_FIELD("روزهای بسته", uuid),
                "telegram_id": Messages.Admin.EditField.PROMPT_GENERIC_FIELD("آیدی عددی تلگرام", uuid),
                "days": Messages.Admin.EditField.PROMPT_RESET_DAYS(uuid),
                "usage": Messages.Admin.EditField.PROMPT_RESET_USAGE(uuid),
            }
            prompt_text = prompt_map.get(field_name, "لطفا مقدار جدید را وارد کنید:")
            await callback_query.message.edit_text(prompt_text, reply_markup=None)
        
        await callback_query.answer()
    except Exception as e:
        logger.error(f"Error in prompt_for_edit_value: {e}", exc_info=True)

@router.message(StateFilter(EditUserState.waiting_for_new_value), F.text)
async def process_new_value(message: Message, state: FSMContext, bot: Bot):
    admin_id = message.from_user.id
    state_data = await state.get_data()
    logger.debug(f"Admin {admin_id} sent new value '{message.text}' for state data: {state_data}")

    action_type = state_data.get("action_type")
    field_name = state_data.get("field_name")
    uuid = state_data.get("uuid")
    user_list_type = state_data.get("user_list_type")
    original_message_id = state_data.get("original_message_id")
    new_value_str = message.text.strip()
    
    payload = {}
    is_valid = True
    
    try:
        if action_type == 'u_r': # Reset actions
            if field_name == 'days':
                payload = {"package_days": int(new_value_str), "start_date": datetime.date.today().strftime("%Y-%m-%d")}
            elif field_name == 'usage':
                payload = {"usage_limit_GB": float(new_value_str), "current_usage_GB": 0}
        elif action_type == 'u_e': # Edit actions
            if field_name in ['package_days']:
                payload = {field_name: int(new_value_str)}
            elif field_name == 'telegram_id':
                 payload = {field_name: int(new_value_str) if new_value_str.lower() not in ['none', '0', ''] else None}
            elif field_name == 'usage_limit_GB':
                payload = {field_name: float(new_value_str)}
            else: # name, comment
                payload = {field_name: new_value_str}
    except (ValueError, TypeError):
        logger.warning(f"Admin {admin_id} sent invalid value '{new_value_str}' for field {field_name}.")
        is_valid = False
        await message.reply(Messages.Admin.EditField.VALUE_INVALID_PROMPT_RETRY)

    if is_valid:
        await state.clear()
        logger.debug(f"State cleared for admin {admin_id} after processing new value.")
        processing_msg = await message.answer(Messages.Admin.EditField.VALUE_UPDATE_PROCESSING)
        
        logger.debug(f"Sending API update request for UUID {uuid} with payload: {payload}")
        success, _, status = await update_user_api(uuid, payload)
        
        try:
            await bot.delete_message(chat_id=message.chat.id, message_id=original_message_id)
            await bot.delete_message(chat_id=message.chat.id, message_id=message.message_id)
            await processing_msg.delete()
        except Exception as e:
            logger.warning(f"Could not delete messages during edit flow: {e}")

        mock_message = await message.answer("درحال بازخوانی اطلاعات...")
        
        if success:
            logger.info(f"Successfully updated field {field_name} for UUID {uuid}.")
            await bot.send_message(message.chat.id, Messages.Admin.EditField.VALUE_UPDATE_SUCCESS(field_display=field_name, value=new_value_str, action_verb="تغییر یافت"))
        else:
            logger.error(f"API update failed for field {field_name}, UUID {uuid}. Status: {status}")
            await bot.send_message(message.chat.id, f"خطا در آپدیت: {status}")
            
        await display_user_info_for_edit(mock_message, uuid, user_list_type, bot, is_from_message=True)
        await mock_message.delete()


# --- Direct User Action Callbacks ---
@router.callback_query(F.data.startswith("u_"))
async def handle_user_actions(callback_query: CallbackQuery, bot: Bot):
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} triggered direct user action with data: {callback_query.data}")
    try:
        parts = callback_query.data.split(":")
        action_abbr = parts[0]
    except (ValueError, IndexError): 
        logger.error(f"Invalid callback format for direct user action: {callback_query.data}")
        return

    if action_abbr == "u_tgl":
        _, uuid, user_list_type = parts
        await callback_query.answer("در حال تغییر وضعیت...")
        success, details, _ = await fetch_user_details_api(uuid)
        if success:
            new_status = not json.loads(details).get('enable', False)
            update_success, _, _ = await update_user_api(uuid, {"enable": new_status})
            if update_success:
                await callback_query.answer(f"کاربر {'فعال' if new_status else 'غیرفعال'} شد.", show_alert=True)
            else:
                await callback_query.answer("خطا در تغییر وضعیت.", show_alert=True)
            await display_user_info_for_edit(callback_query, uuid, user_list_type, bot)
    
    elif action_abbr == "u_qr":
        _, uuid, _ = parts
        await callback_query.answer()
        await send_subscription_qrcode(bot, callback_query.from_user.id, uuid)
    
    elif action_abbr == "u_set_m":
        _, mode, uuid, user_list_type = parts
        await callback_query.answer(f"در حال تنظیم حالت به {mode}...")
        success, _, _ = await update_user_api(uuid, {"mode": mode})
        if success:
            await callback_query.answer(f"حالت با موفقیت به {mode} تغییر یافت.", show_alert=True)
        else:
            await callback_query.answer("خطا در تغییر حالت.", show_alert=True)
        await display_user_info_for_edit(callback_query, uuid, user_list_type, bot)

    elif action_abbr == "u_detach_tg":
        _, uuid, user_list_type = parts
        logger.warning(f"Admin {admin_id} showing detach telegram prompt for UUID: {uuid}")
        success, details, _ = await fetch_user_details_api(uuid)
        
        if not success:
            await callback_query.answer("خطا در دریافت اطلاعات کاربر", show_alert=True)
            return
            
        user_data = json.loads(details)
        name = user_data.get('name', uuid)
        telegram_id = user_data.get('telegram_id', 'نامشخص')
        
        # Escape HTML entities to prevent parsing errors
        safe_name = html_escape(name)
        safe_uuid = html_escape(uuid)
        safe_telegram_id = html_escape(str(telegram_id))
        
        builder = InlineKeyboardBuilder().add(
            InlineKeyboardButton(text="✅ بله، قطع کن", callback_data=f"u_detach_tg_c:{uuid}:{user_list_type}"),
            InlineKeyboardButton(text="❌ خیر", callback_data=f"u_v:{uuid}:{user_list_type}")
        )
        
        confirmation_text = (
            Messages.Admin.DetachTelegram.CONFIRMATION_QUERY + 
            f"\n\n👤 نام: <b>{safe_name}</b>\n🔑 UUID: <code>{safe_uuid}</code>\n🆔 تلگرام: <code>{safe_telegram_id}</code>\n\n" + 
            Messages.Admin.DetachTelegram.CONFIRMATION_WARNING
        )
        
        await callback_query.message.edit_text(confirmation_text, reply_markup=builder.as_markup(), parse_mode="HTML")

    elif action_abbr == "u_del_p":
        _, uuid, user_list_type = parts
        logger.warning(f"Admin {admin_id} showing delete prompt for UUID: {uuid}")
        success, details, _ = await fetch_user_details_api(uuid)
        name = json.loads(details).get('name', uuid) if success else uuid
        
        # Escape HTML entities to prevent parsing errors
        safe_name = html_escape(name)
        safe_uuid = html_escape(uuid)
        
        builder = InlineKeyboardBuilder().add(
            InlineKeyboardButton(text="✅ بله، حذف کن", callback_data=f"u_del_c:{uuid}:{user_list_type}"),
            InlineKeyboardButton(text="❌ خیر", callback_data=f"u_v:{uuid}:{user_list_type}")
        )
        
        confirmation_text = (
            Messages.Admin.DeleteUser.CONFIRMATION_QUERY + 
            f"\n👤 نام: <b>{safe_name}</b>\n🔑 UUID: <code>{safe_uuid}</code>\n\n" + 
            Messages.Admin.DeleteUser.CONFIRMATION_WARNING
        )
        
        await callback_query.message.edit_text(confirmation_text, reply_markup=builder.as_markup(), parse_mode="HTML")

    elif action_abbr == "u_detach_tg_c":
        _, uuid, user_list_type = parts
        logger.warning(f"Admin {admin_id} CONFIRMED detach telegram for UUID: {uuid}")
        
        # Get current user data first
        success, details, _ = await fetch_user_details_api(uuid)
        if not success:
            await callback_query.message.edit_text("❌ خطا در دریافت اطلاعات کاربر.")
            return
            
        user_data = json.loads(details)
        telegram_id = user_data.get('telegram_id')
        
        await callback_query.message.edit_text(Messages.Admin.DetachTelegram.PROCESSING_DETACH(uuid=uuid, telegram_id=telegram_id or 'نامشخص'), parse_mode="HTML")
        
        # Clear from local database first
        try:
            clear_user_uuid(int(telegram_id)) if telegram_id else None
            logger.info(f"Cleared UUID from local database for telegram_id: {telegram_id}")
            db_success = True
        except Exception as e:
            logger.error(f"Error clearing UUID from local database: {e}")
            db_success = False
        
        # Clear telegram_id from panel
        api_success = True
        api_error = None
        try:
            success, _, status = await update_user_api(uuid, {"telegram_id": None})
            if not success:
                api_success = False
                api_error = f"Status: {status}"
                logger.error(f"API Error clearing telegram_id for UUID {uuid}: Status {status}")
        except Exception as e:
            api_success = False
            api_error = str(e)
            logger.error(f"Exception clearing telegram_id for UUID {uuid}: {e}")
        
        # Show result
        if db_success and api_success:
            await callback_query.message.edit_text(Messages.Admin.DetachTelegram.SUCCESS_MESSAGE(uuid=uuid), parse_mode="HTML")
        elif not api_success:
            await callback_query.message.edit_text(Messages.Admin.DetachTelegram.ERROR_API(error=api_error or "نامشخص"), parse_mode="HTML")
        else:
            await callback_query.message.edit_text(Messages.Admin.DetachTelegram.ERROR_DATABASE, parse_mode="HTML")
        
        await asyncio.sleep(3)
        await show_panel_users_paged(callback_query.message, 1, 'edit', user_list_type)

    elif action_abbr == "u_del_c":
        _, uuid, user_list_type = parts
        logger.warning(f"Admin {admin_id} CONFIRMED deletion for UUID: {uuid}")
        await callback_query.message.edit_text(Messages.Admin.DeleteUser.PROCESSING_DELETE(uuid=uuid), parse_mode="Markdown")
        if archive_deleted_user(uuid):
            success, _, status = await delete_user_api(uuid)
            if success:
                await callback_query.message.edit_text(f"✅ کاربر با موفقیت حذف و آرشیو شد.")
            else:
                await callback_query.message.edit_text(f"⚠️ کاربر از دیتابیس محلی حذف شد اما حذف از پنل با خطا مواجه شد. (کد: {status})")
        else:
            await callback_query.message.edit_text(f"❌ خطا در آرشیو کردن کاربر در دیتابیس.")
        await asyncio.sleep(3)
        await show_panel_users_paged(callback_query.message, 1, 'edit', user_list_type)


# --- Add User FSM ---
@router.callback_query(F.data == "admin:add_user")
async def add_user_prompt(callback_query: CallbackQuery, state: FSMContext):
    logger.info(f"Admin {callback_query.from_user.id} initiated Add User flow via callback.")
    await state.set_state(AddUserState.waiting_for_name)
    logger.debug(f"Set state to AddUserState.waiting_for_name for admin {callback_query.from_user.id}.")
    await callback_query.message.edit_text(Messages.Admin.AddUser.PROMPT_NAME)
    await callback_query.answer()

@router.message(AddUserState.waiting_for_name, F.text)
async def process_add_user_name(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    logger.debug(f"Admin {admin_id} is in AddUserState.waiting_for_name and sent text: '{message.text}'")
    
    if not message.text or not message.text.strip():
        logger.warning(f"Admin {admin_id} sent an empty name.")
        await message.reply(Messages.Admin.AddUser.NAME_EMPTY_ERROR)
        return
        
    user_name = message.text.strip()
    await state.update_data(name=user_name)
    logger.info(f"Admin {admin_id} provided name: '{user_name}'. Moving to next step.")
    await state.set_state(AddUserState.waiting_for_note)
    logger.debug(f"Set state to AddUserState.waiting_for_note for admin {admin_id}.")
    
    builder = InlineKeyboardBuilder().button(text=Messages.Buttons.SKIP_OPTIONAL("یادداشت"), callback_data="admin:add_user:skip_note")
    await message.answer(Messages.Admin.AddUser.PROMPT_NOTE, reply_markup=builder.as_markup())

@router.callback_query(AddUserState.waiting_for_note, F.data == "admin:add_user:skip_note")
async def process_add_user_skip_note(callback_query: CallbackQuery, state: FSMContext):
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} skipped providing a note.")
    await state.update_data(note=None)
    await state.set_state(AddUserState.waiting_for_usage_limit)
    logger.debug(f"Set state to AddUserState.waiting_for_usage_limit for admin {admin_id}.")
    await callback_query.message.edit_text(Messages.Admin.AddUser.PROMPT_USAGE_LIMIT)
    await callback_query.answer(Messages.Admin.AddUser.NOTE_SKIPPED)

@router.message(AddUserState.waiting_for_note, F.text)
async def process_add_user_note(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    logger.debug(f"Admin {admin_id} is in AddUserState.waiting_for_note and sent text: '{message.text}'")
    user_note = message.text.strip()
    await state.update_data(note=user_note)
    logger.info(f"Admin {admin_id} provided note: '{user_note}'. Moving to next step.")
    await state.set_state(AddUserState.waiting_for_usage_limit)
    logger.debug(f"Set state to AddUserState.waiting_for_usage_limit for admin {admin_id}.")
    await message.answer(Messages.Admin.AddUser.PROMPT_USAGE_LIMIT)

@router.message(AddUserState.waiting_for_usage_limit, F.text)
async def process_add_user_limit(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    logger.debug(f"Admin {admin_id} is in AddUserState.waiting_for_usage_limit and sent text: '{message.text}'")
    try:
        limit = float(message.text)
        if limit < 0: raise ValueError
        await state.update_data(limit_gb=limit)
        logger.info(f"Admin {admin_id} provided usage limit: {limit} GB. Moving to next step.")
        await state.set_state(AddUserState.waiting_for_package_days)
        logger.debug(f"Set state to AddUserState.waiting_for_package_days for admin {admin_id}.")
        await message.answer(Messages.Admin.AddUser.PROMPT_PACKAGE_DAYS)
    except (ValueError, TypeError):
        logger.warning(f"Admin {admin_id} sent invalid usage limit: '{message.text}'")
        await message.reply(Messages.Admin.AddUser.INVALID_USAGE_LIMIT)

@router.message(AddUserState.waiting_for_package_days, F.text)
async def process_add_user_days(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    logger.debug(f"Admin {admin_id} is in AddUserState.waiting_for_package_days and sent text: '{message.text}'")
    try:
        days = int(message.text)
        if days <= 0: raise ValueError
        await state.update_data(package_days=days)
        logger.info(f"Admin {admin_id} provided package days: {days}. Moving to next step.")
        await state.set_state(AddUserState.waiting_for_mode)
        logger.debug(f"Set state to AddUserState.waiting_for_mode for admin {admin_id}.")
        builder = InlineKeyboardBuilder()
        for mode in ["no_reset", "monthly", "weekly", "daily"]:
            builder.button(text=mode, callback_data=f"admin:add_user:mode:{mode}")
        builder.adjust(2)
        await message.answer(Messages.Admin.AddUser.PROMPT_MODE, reply_markup=builder.as_markup())
    except (ValueError, TypeError):
        logger.warning(f"Admin {admin_id} sent invalid package days: '{message.text}'")
        await message.reply(Messages.Admin.AddUser.INVALID_PACKAGE_DAYS)

@router.callback_query(AddUserState.waiting_for_mode, F.data.startswith("admin:add_user:mode:"))
async def process_add_user_mode(callback_query: CallbackQuery, state: FSMContext):
    admin_id = callback_query.from_user.id
    mode = callback_query.data.split(":")[-1]
    logger.info(f"Admin {admin_id} selected mode '{mode}'. Moving to confirmation.")
    await state.update_data(mode=mode)
    await state.set_state(AddUserState.waiting_for_confirmation)
    logger.debug(f"Set state to AddUserState.waiting_for_confirmation for admin {admin_id}.")
    
    user_data = await state.get_data()
    logger.debug(f"Showing confirmation for data: {user_data}")
    confirmation_text = Messages.Admin.AddUser.CONFIRMATION_TITLE + \
                        Messages.Admin.AddUser.CONFIRMATION_NAME(name=user_data.get('name', '')) + \
                        Messages.Admin.AddUser.CONFIRMATION_NOTE(note=user_data.get('note') or '-') + \
                        Messages.Admin.AddUser.CONFIRMATION_USAGE(usage_gb=user_data.get('limit_gb', 0)) + \
                        Messages.Admin.AddUser.CONFIRMATION_DAYS(days=user_data.get('package_days', 0)) + \
                        Messages.Admin.AddUser.CONFIRMATION_MODE(mode=user_data.get('mode', '')) + \
                        Messages.Admin.AddUser.CONFIRMATION_QUERY
                        
    builder = InlineKeyboardBuilder()
    builder.button(text=Messages.Admin.AddUser.BUTTON_CONFIRM_ADD, callback_data="admin:confirm_add")
    builder.button(text=Messages.Admin.AddUser.BUTTON_CANCEL_ADD, callback_data="admin:cancel_add")
    await callback_query.message.edit_text(confirmation_text, reply_markup=builder.as_markup())
    await callback_query.answer(Messages.Admin.AddUser.MODE_SELECTED(mode=mode))

@router.callback_query(StateFilter(AddUserState.waiting_for_confirmation))
async def confirm_add_user_callback(callback_query: CallbackQuery, state: FSMContext, bot: Bot):
    admin_id = callback_query.from_user.id
    if callback_query.data == "admin:cancel_add":
        logger.info(f"Admin {admin_id} cancelled the Add User flow at confirmation.")
        await state.clear()
        await callback_query.message.edit_text(Messages.General.OPERATION_CANCELLED, reply_markup=create_user_management_menu())
        return
        
    logger.info(f"Admin {admin_id} confirmed adding the user.")
    await callback_query.message.edit_text(Messages.Admin.AddUser.CREATING_USER_PROCESSING)
    user_data = await state.get_data()
    payload = {
        "name": user_data['name'],
        "comment": create_comment_with_history("manual_add", user_data.get('note') or "Manual user creation", admin_id),
        "usage_limit_GB": user_data['limit_gb'],
        "package_days": user_data['package_days'],
        "start_date": datetime.date.today().strftime("%Y-%m-%d"),
        "mode": user_data.get('mode', 'no_reset'),
        "enable": True
    }
    
    logger.debug(f"Sending final payload to add_user_api: {payload}")
    success, data, status = await add_user_api(payload)
    
    await state.clear()
    
    if success:
        try:
            new_user = json.loads(data)
            logger.info(f"Successfully created new user: {new_user}")
            
            await callback_query.message.edit_text(Messages.Admin.AddUser.SUCCESS_MESSAGE_DETAILS(
                name=new_user.get('name'), 
                uuid=new_user.get('uuid'), 
                limit_gb=new_user.get('usage_limit_GB'), 
                days=new_user.get('package_days'), 
                mode=new_user.get('mode')
            ))

            await send_forwardable_user_card(bot, admin_id, new_user)

        except json.JSONDecodeError:
            logger.error(f"Could not parse success response from API: {data}", exc_info=True)
            await callback_query.message.edit_text(Messages.Admin.AddUser.SUCCESS_INVALID_JSON(response=html_escape(data)))
    else:
        logger.error(f"API call to add user failed. Status: {status}, Data: {data}")
        await callback_query.message.edit_text(Messages.Admin.AddUser.API_ERROR_ADD_USER(status_code=status, response_data=html_escape(data)))
    
    logger.debug(f"State cleared for admin {admin_id} after Add User flow.")

async def send_forwardable_user_card(bot: Bot, chat_id: int, new_user: dict):
    """Creates and sends a beautiful, forwardable card for the new user with enhanced info and smart buttons."""
    user_name = new_user.get('name', 'کاربر')
    user_uuid = new_user.get('uuid')

    if not user_uuid:
        logger.error(f"Cannot create forwardable card because UUID is missing from new user data: {new_user}")
        return

    logger.info(f"Creating enhanced forwardable info card for new user '{user_name}' (UUID: {user_uuid})")
    
    # Get bot username for creating deep links
    try:
        bot_info = await bot.get_me()
        bot_username = bot_info.username
    except Exception as e:
        logger.error(f"Failed to get bot info: {e}")
        bot_username = "YourBotUsername"  # Fallback

    success, details_str, _ = await fetch_user_details_api(user_uuid)
    if not success:
        logger.error(f"Failed to fetch user details for forwardable card. UUID: {user_uuid}")
        await bot.send_message(chat_id, "خطا در دریافت اطلاعات کاربر برای ساخت کارت.")
        return

    try:
        user_dict = json.loads(details_str)
        
        start_date_str = user_dict.get('start_date')
        start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d').date() if start_date_str else datetime.date.today()
        package_days = user_dict.get('package_days', 0)
        expiry_date = start_date + datetime.timedelta(days=package_days)
        remaining_days = max(0, (expiry_date - datetime.date.today()).days)
        jalali_expiry = jdatetime.date.fromgregorian(date=expiry_date).strftime('%Y/%m/%d')
        
        usage_limit_gb = user_dict.get('usage_limit_GB', 0.0)
        current_usage_gb = user_dict.get('current_usage_GB', 0.0)
        remaining_usage_gb = max(0.0, usage_limit_gb - current_usage_gb)
        
    except (json.JSONDecodeError, Exception) as e:
        logger.error(f"Error parsing user details for forwardable card: {e}")
        await bot.send_message(chat_id, "خطا در پردازش اطلاعات کاربر.")
        return

    is_success, configs_or_error, _ = await fetch_user_configs_api(user_uuid)
    sub_link_url = None
    if is_success and isinstance(configs_or_error, list):
        auto_config = next((c for c in configs_or_error if c.get('name') == 'Auto' and c.get('link')), None)
        sub_link_url = auto_config['link'] if auto_config else next((c.get('link') for c in configs_or_error if c.get('is_subscription')), None)

    if not sub_link_url:
        sub_link_url = f"https://{HIDDIFY_API_DOMAIN}/{HIDDIFY_API_USER_PROXY_PATH}/{user_uuid}/"

    qr_code_bytes = _generate_qr_code_bytes(sub_link_url)
    if not qr_code_bytes:
        logger.error(f"Failed to generate QR code for forwardable card (UUID: {user_uuid})")
        await bot.send_message(chat_id, "خطا در ساخت QR Code. لطفاً لینک را دستی ارسال کنید.")
        return

    M = Messages.ForwardableUserInfo
    caption = (
        M.ENHANCED_TITLE +
        M.ENHANCED_GREETING(name=html_escape(user_name)) +
        M.ENHANCED_INFO(
            username=html_escape(user_name),
            expiry_date=jalali_expiry,
            remaining_days=remaining_days,
            volume_limit=usage_limit_gb,
            volume_used=current_usage_gb,
            volume_remaining=remaining_usage_gb
        ) +
        M.ENHANCED_QR_SECTION +
        f"<code>{html_escape(sub_link_url)}</code>" +
        M.ENHANCED_FOOTER
    )

    # Create URL buttons instead of callback buttons for forwardability
    builder = InlineKeyboardBuilder()
    
    # Panel access URL
    panel_link_url = f"https://{HIDDIFY_API_DOMAIN}/{HIDDIFY_API_USER_PROXY_PATH}/{user_uuid}/?home=true"
    
    # Deep link for entering bot with UUID
    bot_deep_link = f"https://t.me/{bot_username}?start=uuid_{user_uuid}"
    
    # Deep link for showing apps guide
    apps_deep_link = f"https://t.me/{bot_username}?start=show_apps"
    
    # Create URL buttons
    builder.button(text=M.BUTTON_PANEL_ACCESS, url=panel_link_url)  # Panel access button
    builder.button(text=M.BUTTON_DOWNLOAD_APPS_USER, url=apps_deep_link)  # Link to apps guide in bot
    builder.button(text=M.BUTTON_GOTO_BOT, url=bot_deep_link)
    builder.adjust(1)
    
    try:
        # Send to admin
        await bot.send_photo(
            chat_id=chat_id,
            photo=BufferedInputFile(qr_code_bytes, "subscription.png"),
            caption=caption,
            reply_markup=builder.as_markup(),
            parse_mode="HTML"
        )
        logger.info(f"Successfully sent enhanced forwardable card for user {user_uuid} to admin {chat_id}.")
        
        # <<< START OF MODIFICATION >>>
        # Also send to TELEGRAM_INFO_CHAT if configured
        if TELEGRAM_INFO_CHAT:
            try:
                # Send a new message with the same components, not a forward
                await bot.send_photo(
                    chat_id=TELEGRAM_INFO_CHAT,
                    photo=BufferedInputFile(qr_code_bytes, "subscription.png"),
                    caption=caption,
                    reply_markup=builder.as_markup(),
                    parse_mode="HTML"
                )
                logger.info(f"Successfully sent enhanced forwardable card for user {user_uuid} to info channel {TELEGRAM_INFO_CHAT}.")
            except Exception as e:
                logger.error(f"Failed to send forwardable user card to info channel {TELEGRAM_INFO_CHAT}: {e}")
        # <<< END OF MODIFICATION >>>
        
    except Exception as e:
        logger.error(f"Failed to send enhanced forwardable user card for {user_uuid}: {e}", exc_info=True)
        await bot.send_message(chat_id, "خطا در ارسال کارت اطلاعات کاربر.")

# --- Search User FSM ---
@router.callback_query(F.data == "admin:search_user")
async def search_user_prompt(callback_query: CallbackQuery, state: FSMContext):
    logger.info(f"Admin {callback_query.from_user.id} initiated Search User flow.")
    await state.set_state(SearchUserState.waiting_for_uuid)
    logger.debug(f"Set state to SearchUserState.waiting_for_uuid for admin {callback_query.from_user.id}.")
    await callback_query.message.edit_text(Messages.Admin.SearchUser.PROMPT_UUID)
    await callback_query.answer()

@router.message(SearchUserState.waiting_for_uuid, F.text)
async def process_search_uuid(message: Message, state: FSMContext, bot: Bot):
    admin_id = message.from_user.id
    logger.debug(f"Admin {admin_id} is in SearchUserState.waiting_for_uuid and sent text.")
    
    # Use the enhanced extract_uuid_from_input that handles panel links
    from utils import extract_uuid_from_input
    uuid = await extract_uuid_from_input(message.text.strip())
    
    if not uuid:
        logger.warning(f"Admin {admin_id} sent invalid UUID format for search: '{message.text}'")
        await message.reply(Messages.Admin.SearchUser.INVALID_UUID_FORMAT)
        return
    
    await state.clear()
    logger.debug(f"State cleared for admin {admin_id} after getting UUID for search.")
    await display_user_info_for_edit(message, uuid, 'regular', bot, is_from_message=True)


# --- Send Message FSM Handlers ---
@router.callback_query(F.data == "admin:send_msg:all")
async def send_to_all_start(callback_query: CallbackQuery, state: FSMContext):
    await callback_query.answer()
    await state.set_state(SendMessageState.waiting_for_message_text)
    await state.update_data(target="all")
    logger.info(f"Admin {callback_query.from_user.id} initiated broadcast. Waiting for message text.")
    await callback_query.message.edit_text(Messages.Admin.SendMessage.PROMPT_TEXT_FOR_BROADCAST)

@router.callback_query(F.data == "admin:send_msg:uuid")
async def send_to_uuid_start(callback_query: CallbackQuery, state: FSMContext):
    await callback_query.answer()
    await state.set_state(SendMessageState.waiting_for_target_uuid)
    logger.info(f"Admin {callback_query.from_user.id} wants to send by UUID. Waiting for UUID.")
    await callback_query.message.edit_text(Messages.Admin.SendMessage.PROMPT_TARGET_UUID)

@router.callback_query(F.data == "admin:send_msg:list")
async def send_to_list_start(callback_query: CallbackQuery):
    await callback_query.answer("در حال بارگیری لیست کاربران...")
    logger.info(f"Admin {callback_query.from_user.id} wants to select a user from the list to send a message.")
    await show_panel_users_paged(callback_query.message, page=1, menu_type="send_msg", user_type="regular")

@router.callback_query(F.data.startswith("admin:send_msg:select_user:"))
async def user_selected_for_message(callback_query: CallbackQuery, state: FSMContext):
    uuid = callback_query.data.split(":")[-1]
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} selected user with UUID {uuid} from the list.")
    
    user_db_info = get_user_from_database_by_uuid(uuid)
    if not user_db_info or not user_db_info[2]:
        logger.error(f"Cannot send message. TG ID for UUID {uuid} not found in 'newusers' table.")
        await callback_query.answer(Messages.Admin.SendMessage.TARGET_UUID_NOT_IN_DB(uuid), show_alert=True)
        return

    target_user_id = user_db_info[2]
    target_user_name = user_db_info[0]
    
    await state.set_state(SendMessageState.waiting_for_message_text)
    await state.update_data(target_id=target_user_id, target_name=target_user_name)
    logger.debug(f"State set to 'waiting_for_message_text' for single target: {target_user_id}")
    
    await callback_query.message.edit_text(
        Messages.Admin.SendMessage.PROMPT_TEXT_FOR_SINGLE_USER(user_name=target_user_name, uuid=uuid)
    )

@router.message(StateFilter(SendMessageState.waiting_for_target_uuid), F.text)
async def process_send_message_uuid(message: Message, state: FSMContext):
    uuid = message.text.strip()
    logger.info(f"Admin provided UUID {uuid} to send a message.")
    
    user_db_info = get_user_from_database_by_uuid(uuid)
    if not user_db_info or not user_db_info[2]:
        logger.error(f"Cannot send message. TG ID for UUID {uuid} not found in 'newusers' table.")
        await message.reply(Messages.Admin.SendMessage.TARGET_UUID_NOT_IN_DB(uuid))
        return

    target_user_id = user_db_info[2]
    target_user_name = user_db_info[0]

    await state.set_state(SendMessageState.waiting_for_message_text)
    await state.update_data(target_id=target_user_id, target_name=target_user_name)
    logger.debug(f"State set to 'waiting_for_message_text' for single target: {target_user_id}")

    await message.answer(
        Messages.Admin.SendMessage.PROMPT_TEXT_FOR_SINGLE_USER(user_name=target_user_name, uuid=uuid)
    )

@router.message(StateFilter(SendMessageState.waiting_for_message_text))
async def process_dynamic_message_content(message: Message, state: FSMContext, bot: Bot):
    admin_id = message.from_user.id
    logger.info(f"Admin {admin_id} provided message content for dynamic send")
    
    # Store message content and details
    message_data = {
        'text': message.text or message.caption or "",
        'media_type': None,
        'media_file_id': None,
        'media_caption': message.caption
    }
    
    # Determine media type
    if message.photo:
        message_data['media_type'] = "photo"
        message_data['media_file_id'] = message.photo[-1].file_id
    elif message.video:
        message_data['media_type'] = "video"
        message_data['media_file_id'] = message.video.file_id
    elif message.document:
        message_data['media_type'] = "document"
        message_data['media_file_id'] = message.document.file_id
    elif message.voice:
        message_data['media_type'] = "voice"
        message_data['media_file_id'] = message.voice.file_id
    elif message.audio:
        message_data['media_type'] = "audio"
        message_data['media_file_id'] = message.audio.file_id
    elif message.animation:
        message_data['media_type'] = "animation"
        message_data['media_file_id'] = message.animation.file_id
    elif message.sticker:
        message_data['media_type'] = "sticker"
        message_data['media_file_id'] = message.sticker.file_id
    
    await state.update_data(message_data=message_data)
    
    # Ask about inline buttons
    await state.set_state(SendMessageState.waiting_for_inline_buttons_count)
    
    # Get target type to include in the message
    data = await state.get_data()
    target_type = "broadcast"
    if data.get('target_uuid'):
        target_type = "uuid"
        target_name = data.get('target_user_name', 'نامشخص')
        await message.answer(
            f"📤 **ارسال پیام به: {target_name}**\n\n" + 
            Messages.Admin.DynamicSendMessage.PROMPT_INLINE_BUTTONS,
            reply_markup=create_inline_buttons_count_keyboard(),
            parse_mode="Markdown"
        )
    else:
        await message.answer(
            "📤 **ارسال پیام به همه کاربران**\n\n" + 
            Messages.Admin.DynamicSendMessage.PROMPT_INLINE_BUTTONS,
            reply_markup=create_inline_buttons_count_keyboard(),
            parse_mode="Markdown"
        )
    
    await state.update_data(target_type=target_type)

@router.callback_query(F.data.startswith("admin:send_msg:buttons:"))
async def handle_inline_buttons_count_selection(callback_query: CallbackQuery, state: FSMContext):
    """Handle selection of inline buttons count"""
    await callback_query.answer()
    admin_id = callback_query.from_user.id
    buttons_count = int(callback_query.data.split(":")[-1])
    logger.info(f"Admin {admin_id} selected {buttons_count} inline buttons")

    await state.update_data(buttons_count=buttons_count)

    if buttons_count == 0:
        # No buttons, go directly to preview
        await show_final_message_preview(callback_query, state, callback_query.bot)
    else:
        # Start collecting button data
        await state.update_data(
            buttons_data=[],
            current_button_index=1
        )
        await state.set_state(SendMessageState.waiting_for_button_name)
        await callback_query.message.edit_text(
            Messages.Admin.DynamicSendMessage.PROMPT_BUTTON_NAME(1)
        )

@router.callback_query(F.data == "admin:send_msg:confirm")
async def handle_send_message_confirm(callback_query: CallbackQuery, state: FSMContext, bot: Bot):
    """Handle confirmation to send the message"""
    await callback_query.answer()
    await send_dynamic_message(callback_query, state, bot)

@router.callback_query(F.data == "admin:send_msg:cancel")
async def handle_send_message_cancel(callback_query: CallbackQuery, state: FSMContext):
    """Handle cancellation of message sending"""
    await callback_query.answer()
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} cancelled message sending")

    await state.clear()
    await callback_query.message.edit_text(
        "❌ ارسال پیام لغو شد.",
        reply_markup=None
    )

@router.message(StateFilter(SendMessageState.waiting_for_button_name), F.text)
async def process_button_name(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    button_name = message.text.strip()
    logger.info(f"Admin {admin_id} provided button name: {button_name}")
    
    if len(button_name) > 30:
        await message.reply(Messages.Admin.DynamicSendMessage.INVALID_BUTTON_NAME)
        return
    
    data = await state.get_data()
    current_index = data.get('current_button_index', 1)
    
    await state.update_data(current_button_name=button_name)
    await state.set_state(SendMessageState.waiting_for_button_url)
    await message.answer(Messages.Admin.DynamicSendMessage.PROMPT_BUTTON_URL(current_index))

@router.message(StateFilter(SendMessageState.waiting_for_button_url), F.text)
async def process_button_url(message: Message, state: FSMContext, bot: Bot):
    admin_id = message.from_user.id
    button_url = message.text.strip()
    logger.info(f"Admin {admin_id} provided button URL")
    
    # Simple URL validation
    if not (button_url.startswith('http://') or button_url.startswith('https://')):
        await message.reply(Messages.Admin.DynamicSendMessage.INVALID_URL)
        return
    
    # Store button data
    data = await state.get_data()
    current_index = data.get('current_button_index', 1)
    button_name = data.get('current_button_name', '')
    buttons_data = data.get('buttons_data', [])
    buttons_count = data.get('buttons_count', 0)
    
    buttons_data.append({'name': button_name, 'url': button_url})
    
    if current_index < buttons_count:
        # More buttons to collect
        await state.update_data(
            buttons_data=buttons_data,
            current_button_index=current_index + 1
        )
        await state.set_state(SendMessageState.waiting_for_button_name)
        await message.answer(Messages.Admin.DynamicSendMessage.PROMPT_BUTTON_NAME(current_index + 1))
    else:
        # All buttons collected, show preview
        await state.update_data(buttons_data=buttons_data)
        await show_final_message_preview(message, state, bot)

async def show_final_message_preview(message_or_callback, state: FSMContext, bot: Bot):
    """Show final preview of the message with buttons"""
    data = await state.get_data()
    message_data = data.get('message_data', {})
    buttons_data = data.get('buttons_data', [])
    target_type = data.get('target_type', 'broadcast')
    
    # Create preview message
    preview_text = Messages.Admin.DynamicSendMessage.FINAL_PREVIEW_TITLE
    
    # Add target info
    if target_type == "uuid":
        target_name = data.get('target_user_name', 'نامشخص')
        target_uuid = data.get('target_uuid', 'نامشخص')
        preview_text += f"🎯 <b>مقصد:</b> {target_name} (<code>{target_uuid}</code>)\n\n"
    else:
        preview_text += f"🎯 <b>مقصد:</b> همه کاربران\n\n"
    
    # Add message content preview
    original_text = message_data.get('text', '')
    if original_text:
        preview_text += f"💬 <b>متن:</b>\n{original_text[:200]}{'...' if len(original_text) > 200 else ''}\n\n"
    
    # Add media info
    media_type = message_data.get('media_type')
    if media_type:
        preview_text += f"📎 <b>رسانه:</b> {media_type}\n\n"
    
    # Add buttons info
    if buttons_data:
        preview_text += f"🔘 <b>دکمه‌های شیشه‌ای:</b>\n"
        for i, button in enumerate(buttons_data, 1):
            preview_text += f"  {i}. {button['name']} → {button['url'][:30]}{'...' if len(button['url']) > 30 else ''}\n"
        preview_text += "\n"
    
    preview_text += Messages.Admin.DynamicSendMessage.FINAL_CONFIRMATION
    
    # Create preview with actual buttons
    builder = InlineKeyboardBuilder()
    for button in buttons_data:
        builder.button(text=button['name'], url=button['url'])
    
    # Add confirmation buttons
    if buttons_data:
        builder.row(InlineKeyboardButton(text="✅ تایید و ارسال", callback_data="admin:send_msg:confirm"))
        builder.row(InlineKeyboardButton(text="❌ لغو", callback_data="admin:send_msg:cancel"))
    else:
        builder = InlineKeyboardBuilder()
        builder.button(text="✅ تایید و ارسال", callback_data="admin:send_msg:confirm")
        builder.button(text="❌ لغو", callback_data="admin:send_msg:cancel")
        builder.adjust(1)
    
    await state.set_state(SendMessageState.waiting_for_final_confirmation)
    
    if hasattr(message_or_callback, 'message'):  # It's a callback
        await message_or_callback.message.answer(preview_text, reply_markup=builder.as_markup(), parse_mode="HTML")
    else:  # It's a message
        await message_or_callback.answer(preview_text, reply_markup=builder.as_markup(), parse_mode="HTML")

async def send_dynamic_message(callback_query: CallbackQuery, state: FSMContext, bot: Bot):
    """Send the dynamic message to target(s)"""
    admin_id = callback_query.from_user.id
    data = await state.get_data()
    
    message_data = data.get('message_data', {})
    buttons_data = data.get('buttons_data', [])
    target_type = data.get('target_type', 'broadcast')
    
    # Create inline keyboard
    builder = InlineKeyboardBuilder()
    for button in buttons_data:
        builder.button(text=button['name'], url=button['url'])
    reply_markup = builder.as_markup() if buttons_data else None
    
    await callback_query.message.edit_text(Messages.Admin.SendMessage.PREPARING_TO_SEND)
    
    if target_type == "uuid":
        # Send to specific user
        target_uuid = data.get('target_uuid')
        user_data = get_user_from_database_by_uuid(target_uuid)
        if user_data:
            target_user_id = user_data[2]  # num_id
            success = await send_message_to_user(bot, target_user_id, message_data, reply_markup)
            if success:
                await callback_query.message.edit_text(
                    Messages.Admin.SendMessage.SINGLE_SEND_SUCCESS(user_data[0], target_user_id)
                )
            else:
                await callback_query.message.edit_text(
                    Messages.Admin.SendMessage.SINGLE_SEND_FAIL(user_data[0], target_user_id, "خطای ارسال")
                )
    else:
        # Broadcast to all users
        users = get_all_users_with_uuid()
        if not users:
            await callback_query.message.edit_text(Messages.Admin.SendMessage.BROADCAST_NO_USERS_FOUND)
            await state.clear()
            return

        # Perform the broadcast
        success_count = 0
        failed_count = 0
        blocked_count = 0

        for user in users:
            user_id = user[0]  # num_id (first column in get_all_users_with_uuid)
            if user_id:
                try:
                    success = await send_message_to_user(bot, user_id, message_data, reply_markup)
                    if success:
                        success_count += 1
                    else:
                        failed_count += 1
                except Exception as e:
                    if "blocked" in str(e).lower() or "forbidden" in str(e).lower():
                        blocked_count += 1
                    else:
                        failed_count += 1
                    logger.debug(f"Failed to send to user {user_id}: {e}")

        # Show results
        total_count = success_count + failed_count + blocked_count
        result_text = Messages.Admin.DynamicSendMessage.BROADCAST_RESULT_TITLE
        result_text += Messages.Admin.DynamicSendMessage.BROADCAST_SUCCESS(success_count)
        result_text += Messages.Admin.DynamicSendMessage.BROADCAST_FAILED(failed_count)
        result_text += Messages.Admin.DynamicSendMessage.BROADCAST_BLOCKED(blocked_count)
        result_text += Messages.Admin.DynamicSendMessage.BROADCAST_TOTAL(total_count)

        await callback_query.message.edit_text(result_text, parse_mode="HTML")
        await state.clear()

async def send_message_to_user(bot: Bot, user_id: int, message_data: dict, reply_markup=None) -> bool:
    """Helper function to send message to a specific user"""
    try:
        media_type = message_data.get('media_type')
        media_file_id = message_data.get('media_file_id')
        text = message_data.get('text', '')
        caption = message_data.get('media_caption', '')
        
        if media_type and media_file_id:
            if media_type == 'photo':
                await bot.send_photo(user_id, photo=media_file_id, caption=caption or text, reply_markup=reply_markup)
            elif media_type == 'video':
                await bot.send_video(user_id, video=media_file_id, caption=caption or text, reply_markup=reply_markup)
            elif media_type == 'document':
                await bot.send_document(user_id, document=media_file_id, caption=caption or text, reply_markup=reply_markup)
            elif media_type == 'voice':
                await bot.send_voice(user_id, voice=media_file_id, reply_markup=reply_markup)
                if text:
                    await bot.send_message(user_id, text, reply_markup=reply_markup)
            elif media_type == 'audio':
                await bot.send_audio(user_id, audio=media_file_id, caption=caption or text, reply_markup=reply_markup)
            elif media_type == 'animation':
                await bot.send_animation(user_id, animation=media_file_id, caption=caption or text, reply_markup=reply_markup)
            elif media_type == 'sticker':
                await bot.send_sticker(user_id, sticker=media_file_id, reply_markup=reply_markup)
                if text:
                    await bot.send_message(user_id, text, reply_markup=reply_markup)
        else:
            await bot.send_message(user_id, text, reply_markup=reply_markup)
        
        return True
    except Exception as e:
        logger.debug(f"Failed to send message to {user_id}: {e}")
        return False


# --- Complete Plan Management Handlers (fixing all issues)
@router.callback_query(F.data.startswith("admin:plans:"))
async def handle_plan_management(callback_query: CallbackQuery, state: FSMContext):
    admin_id = callback_query.from_user.id
    action_parts = callback_query.data.split(":")
    action = action_parts[2] if len(action_parts) > 2 else ""
    logger.info(f"Admin {admin_id} plan management action: {action}")
    
    try:
        if action == "view_all":
            await callback_query.answer()
            plans = get_all_plans()
            if not plans:
                from keyboards import create_plan_management_menu
                await callback_query.message.edit_text(
                    Messages.Admin.PlanManagement.NO_PLANS_FOUND,
                    reply_markup=create_plan_management_menu(),
                    parse_mode="HTML"
                )
            else:
                plan_list_text = Messages.Admin.PlanManagement.PLAN_LIST_TITLE
                for plan in plans:
                    plan_list_text += Messages.Admin.PlanManagement.PLAN_ITEM(plan)
                
                from keyboards import create_plans_list_keyboard
                await callback_query.message.edit_text(
                    plan_list_text,
                    reply_markup=create_plans_list_keyboard(plans),
                    parse_mode="HTML"
                )
        
        elif action == "add_new":
            await callback_query.answer()
            from states import PlanManagementState
            await state.set_state(PlanManagementState.waiting_for_plan_id)
            await callback_query.message.edit_text(Messages.Admin.PlanManagement.PROMPT_PLAN_ID, reply_markup=None)
        
        elif action == "edit":
            if len(action_parts) < 4:
                logger.error(f"Edit action missing plan_id in callback: {callback_query.data}")
                await callback_query.answer("خطا در شناسه پلن", show_alert=True)
                return
                
            plan_id = action_parts[3]
            plan = get_plan_by_id(plan_id)
            if not plan:
                await callback_query.answer(Messages.Admin.PlanManagement.PLAN_NOT_FOUND, show_alert=True)
                return

            await callback_query.answer()
            plan_info = Messages.Admin.PlanManagement.PLAN_ITEM(plan)
            from keyboards import create_plan_edit_keyboard
            await callback_query.message.edit_text(
                f"📋 <b>مشخصات پلن:</b>\n\n{plan_info}",
                reply_markup=create_plan_edit_keyboard(plan_id),
                parse_mode="HTML"
            )
        
        elif action == "modify":
            if len(action_parts) < 4:
                logger.error(f"Modify action missing plan_id in callback: {callback_query.data}")
                await callback_query.answer("خطا در شناسه پلن", show_alert=True)
                return
                
            plan_id = action_parts[3]
            plan = get_plan_by_id(plan_id)
            if not plan:
                await callback_query.answer(Messages.Admin.PlanManagement.PLAN_NOT_FOUND, show_alert=True)
                return

            await callback_query.answer()
            # Store plan_id for editing
            await state.update_data(edit_plan_id=plan_id)
            
            # Show field selection keyboard
            field_edit_text = Messages.Admin.PlanManagement.FIELD_EDIT_TITLE(plan['plan_name'])
            from keyboards import create_plan_field_edit_keyboard
            await callback_query.message.edit_text(
                field_edit_text,
                reply_markup=create_plan_field_edit_keyboard(plan_id),
                parse_mode="HTML"
            )
        
        elif action == "edit_field":
            if len(action_parts) < 5:
                logger.error(f"Edit field action missing parameters in callback: {callback_query.data}")
                await callback_query.answer("خطا در پارامترهای ویرایش", show_alert=True)
                return
                
            field_type = action_parts[3]
            plan_id = action_parts[4]
            plan = get_plan_by_id(plan_id)
            if not plan:
                await callback_query.answer(Messages.Admin.PlanManagement.PLAN_NOT_FOUND, show_alert=True)
                return

            await callback_query.answer()
            await state.update_data(edit_plan_id=plan_id, edit_field_type=field_type)
            
            from states import PlanManagementState
            # Get current value and set appropriate state
            current_value = None
            if field_type == "name":
                current_value = plan['plan_name']
                await state.set_state(PlanManagementState.waiting_for_edit_name)
                prompt_text = Messages.Admin.PlanManagement.EDIT_NAME_PROMPT(current_value)
            elif field_type == "description":
                current_value = plan['description']
                await state.set_state(PlanManagementState.waiting_for_edit_description)
                prompt_text = Messages.Admin.PlanManagement.EDIT_DESCRIPTION_PROMPT(current_value)
            elif field_type == "days":
                current_value = plan['days']
                await state.set_state(PlanManagementState.waiting_for_edit_days)
                prompt_text = Messages.Admin.PlanManagement.EDIT_DAYS_PROMPT(current_value)
            elif field_type == "volume":
                current_value = plan['volume_gb']
                await state.set_state(PlanManagementState.waiting_for_edit_volume)
                prompt_text = Messages.Admin.PlanManagement.EDIT_VOLUME_PROMPT(current_value)
            elif field_type == "price":
                current_value = plan['price_toman']
                await state.set_state(PlanManagementState.waiting_for_edit_price)
                prompt_text = Messages.Admin.PlanManagement.EDIT_PRICE_PROMPT(current_value)
            else:
                await callback_query.message.edit_text("❌ نوع فیلد نامعتبر است.")
                return
            
            # Show prompt for new value
            await callback_query.message.edit_text(prompt_text, parse_mode="HTML")
        
        elif action == "toggle":
            if len(action_parts) < 4:
                logger.error(f"Toggle action missing plan_id in callback: {callback_query.data}")
                await callback_query.answer("خطا در شناسه پلن", show_alert=True)
                return
                
            plan_id = action_parts[3]
            plan = get_plan_by_id(plan_id)
            if not plan:
                await callback_query.answer(Messages.Admin.PlanManagement.PLAN_NOT_FOUND, show_alert=True)
                return
                
            success = toggle_plan_status(plan_id)
            if success:
                new_status = not plan['is_active']
                await callback_query.answer(
                    Messages.Admin.PlanManagement.PLAN_STATUS_TOGGLED(plan['plan_name'], new_status),
                    show_alert=True
                )
                # Refresh plan info
                updated_plan = get_plan_by_id(plan_id)
                if updated_plan:
                    plan_info = Messages.Admin.PlanManagement.PLAN_ITEM(updated_plan)
                    from keyboards import create_plan_edit_keyboard
                    await callback_query.message.edit_text(
                        f"📋 <b>مشخصات پلن:</b>\n\n{plan_info}",
                        reply_markup=create_plan_edit_keyboard(plan_id),
                        parse_mode="HTML"
                    )
            else:
                await callback_query.answer("خطا در تغییر وضعیت پلن.", show_alert=True)
        
        elif action == "delete":
            if len(action_parts) < 4:
                logger.error(f"Delete action missing plan_id in callback: {callback_query.data}")
                await callback_query.answer("خطا در شناسه پلن", show_alert=True)
                return
                
            plan_id = action_parts[3]
            plan = get_plan_by_id(plan_id)
            if not plan:
                await callback_query.answer(Messages.Admin.PlanManagement.PLAN_NOT_FOUND, show_alert=True)
                return

            await callback_query.answer()
            # Show confirmation
            builder = InlineKeyboardBuilder()
            builder.button(text="✅ بله، حذف کن", callback_data=f"admin:plans:confirm_delete:{plan_id}")
            builder.button(text="❌ خیر", callback_data=f"admin:plans:edit:{plan_id}")
            builder.adjust(1)
            
            await callback_query.message.edit_text(
                f"⚠️ <b>تایید حذف پلن</b>\n\n"
                f"آیا از حذف پلن '{plan['plan_name']}' مطمئن هستید؟\n\n"
                f"🛑 <b>این عملیات غیرقابل بازگشت است!</b>",
                reply_markup=builder.as_markup(),
                parse_mode="HTML"
            )
        
        elif action == "confirm_delete":
            if len(action_parts) < 4:
                logger.error(f"Confirm delete action missing plan_id in callback: {callback_query.data}")
                await callback_query.answer("خطا در شناسه پلن", show_alert=True)
                return
                
            plan_id = action_parts[3]
            success = delete_plan(plan_id)
            if success:
                await callback_query.answer(Messages.Admin.PlanManagement.PLAN_DELETED, show_alert=True)
                # Go back to plans list
                plans = get_all_plans()
                if plans:
                    plan_list_text = Messages.Admin.PlanManagement.PLAN_LIST_TITLE
                    for plan in plans:
                        plan_list_text += Messages.Admin.PlanManagement.PLAN_ITEM(plan)
                    from keyboards import create_plans_list_keyboard
                    await callback_query.message.edit_text(
                        plan_list_text,
                        reply_markup=create_plans_list_keyboard(plans),
                        parse_mode="HTML"
                    )
                else:
                    from keyboards import create_plan_management_menu
                    await callback_query.message.edit_text(
                        Messages.Admin.PlanManagement.NO_PLANS_FOUND,
                        reply_markup=create_plan_management_menu(),
                        parse_mode="HTML"
                    )
            else:
                await callback_query.answer(Messages.Admin.PlanManagement.PLAN_DELETE_ERROR, show_alert=True)
        
        elif action == "save_confirmed":
            logger.info(f"Admin {admin_id} confirming plan save")
            await callback_query.answer("در حال ذخیره پلن...")
            
            # Handle save confirmation without state requirement
            try:
                data = await state.get_data()
                edit_plan_id = data.get('edit_plan_id')
                
                plan_id = edit_plan_id or data.get('plan_id')
                plan_name = data.get('plan_name')
                plan_description = data.get('plan_description')
                plan_days = data.get('plan_days')
                plan_volume = data.get('plan_volume', 0)
                plan_price = data.get('plan_price', 0)
                
                logger.debug(f"Plan data to save: ID={plan_id}, Name={plan_name}, Days={plan_days}, Volume={plan_volume}, Price={plan_price}")
                
                if not plan_id or not plan_name:
                    logger.error(f"Missing required plan data: plan_id={plan_id}, plan_name={plan_name}")
                    await callback_query.message.edit_text("❌ اطلاعات پلن ناقص است")
                    return
                
                success = save_plan(plan_id, plan_name, plan_description, plan_days, plan_volume, plan_price)
                logger.info(f"Save plan result: {success}")
                
                if success:
                    await callback_query.message.edit_text(Messages.Admin.PlanManagement.PLAN_SAVED)
                    await state.clear()
                    # Show updated plans list
                    await asyncio.sleep(1)
                    try:
                        plans = get_all_plans()
                        plan_list_text = Messages.Admin.PlanManagement.PLAN_LIST_TITLE
                        for plan in plans:
                            plan_list_text += Messages.Admin.PlanManagement.PLAN_ITEM(plan)
                        from keyboards import create_plans_list_keyboard
                        await callback_query.message.answer(
                            plan_list_text,
                            reply_markup=create_plans_list_keyboard(plans),
                            parse_mode="HTML"
                        )
                    except Exception as e:
                        logger.error(f"Error showing updated plans list: {e}")
                        from keyboards import create_plan_management_menu
                        await callback_query.message.answer(
                            "✅ پلن ذخیره شد",
                            reply_markup=create_plan_management_menu(),
                            parse_mode="HTML"
                        )
                else:
                    await callback_query.message.edit_text(Messages.Admin.PlanManagement.PLAN_SAVE_ERROR)
                    await state.clear()
            except Exception as e:
                logger.error(f"Critical error in save_confirmed: {e}", exc_info=True)
                await callback_query.message.edit_text("❌ خطای داخلی در ذخیره پلن")
                await state.clear()
        
        elif action == "cancel_add":
            await callback_query.answer()
            await state.clear()
            from keyboards import create_plan_management_menu
            await callback_query.message.edit_text(
                Messages.Admin.PlanManagement.TITLE,
                reply_markup=create_plan_management_menu(),
                parse_mode="HTML"
            )
        
        elif action == "menu":
            await callback_query.answer()
            await state.clear()  # Clear any existing state
            from keyboards import create_plan_management_menu
            await callback_query.message.edit_text(
                Messages.Admin.PlanManagement.TITLE,
                reply_markup=create_plan_management_menu(),
                parse_mode="HTML"
            )
        
        else:
            logger.warning(f"Unknown plan management action: {action}")
            await callback_query.answer("عملیات نامعتبر", show_alert=True)
        
    except Exception as e:
        logger.error(f"Critical error in handle_plan_management: {e}", exc_info=True)
        try:
            await callback_query.answer("خطای داخلی رخ داد", show_alert=True)
        except:
            pass
        try:
            await state.clear()
        except:
            pass
        try:
            from keyboards import create_plan_management_menu
            await callback_query.message.edit_text(
                "❌ خطای داخلی رخ داد. لطفاً دوباره تلاش کنید.",
                reply_markup=create_plan_management_menu(),
                parse_mode="HTML"
            )
        except Exception as fallback_error:
            logger.error(f"Even fallback failed: {fallback_error}")

# Plan Management FSM Handlers
@router.message(StateFilter(PlanManagementState.waiting_for_plan_id), F.text)
async def process_plan_id(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    plan_id = message.text.strip().lower()
    logger.info(f"Admin {admin_id} provided plan ID: {plan_id}")
    
    # Validate plan ID format
    import re  # type: ignore
    if not re.match(r'^[a-z0-9_-]+$', plan_id):
        await message.reply(Messages.Admin.PlanManagement.INVALID_PLAN_ID)
        return
    
    # Check if plan already exists
    existing_plan = get_plan_by_id(plan_id)
    if existing_plan:
        await message.reply(Messages.Admin.PlanManagement.PLAN_ID_EXISTS)
        return
    
    await state.update_data(plan_id=plan_id)
    await state.set_state(PlanManagementState.waiting_for_plan_name)
    await message.answer(Messages.Admin.PlanManagement.PROMPT_PLAN_NAME)

@router.message(StateFilter(PlanManagementState.waiting_for_plan_name), F.text)
async def process_plan_name(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    plan_name = message.text.strip()
    logger.info(f"Admin {admin_id} provided plan name: {plan_name}")
    
    await state.update_data(plan_name=plan_name)
    await state.set_state(PlanManagementState.waiting_for_plan_description)
    await message.answer(Messages.Admin.PlanManagement.PROMPT_PLAN_DESCRIPTION)

@router.message(StateFilter(PlanManagementState.waiting_for_plan_description), F.text)
async def process_plan_description(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    plan_description = message.text.strip()
    logger.info(f"Admin {admin_id} provided plan description")
    
    await state.update_data(plan_description=plan_description)
    await state.set_state(PlanManagementState.waiting_for_plan_days)
    await message.answer(Messages.Admin.PlanManagement.PROMPT_PLAN_DAYS)

@router.message(StateFilter(PlanManagementState.waiting_for_plan_days), F.text)
async def process_plan_days(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    logger.info(f"Admin {admin_id} provided plan days")
    
    try:
        plan_days = int(message.text.strip())
        if plan_days <= 0:
            raise ValueError
        await state.update_data(plan_days=plan_days)
        await state.set_state(PlanManagementState.waiting_for_plan_volume)
        await message.answer(Messages.Admin.PlanManagement.PROMPT_PLAN_VOLUME)
    except ValueError:
        await message.reply(Messages.Admin.PlanManagement.INVALID_DAYS)

@router.message(StateFilter(PlanManagementState.waiting_for_plan_volume), F.text)
async def process_plan_volume(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    logger.info(f"Admin {admin_id} provided plan volume")
    
    try:
        plan_volume = float(message.text.strip())
        if plan_volume <= 0:
            raise ValueError
        await state.update_data(plan_volume=plan_volume)
        await state.set_state(PlanManagementState.waiting_for_plan_price)
        await message.answer(Messages.Admin.PlanManagement.PROMPT_PLAN_PRICE)
    except ValueError:
        await message.reply(Messages.Admin.PlanManagement.INVALID_VOLUME)

@router.message(StateFilter(PlanManagementState.waiting_for_plan_price), F.text)
async def process_plan_price(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    logger.info(f"Admin {admin_id} provided plan price")
    
    try:
        plan_price = int(message.text.strip())
        if plan_price <= 0:
            raise ValueError
        await state.update_data(plan_price=plan_price)
        await state.set_state(PlanManagementState.waiting_for_plan_confirmation)
        
        # Show confirmation
        data = await state.get_data()
        
        plan_details = {
            'id': data.get('plan_id', ''),
            'name': data.get('plan_name', ''),
            'description': data.get('plan_description', ''),
            'days': data.get('plan_days', 0),
            'volume': data.get('plan_volume', 0),
            'price': data.get('plan_price', 0)
        }
        
        confirmation_text = Messages.Admin.PlanManagement.CONFIRMATION_TITLE + \
                          Messages.Admin.PlanManagement.CONFIRMATION_DETAILS(plan_details)
        
        builder = InlineKeyboardBuilder()
        builder.button(text="✅ تایید و ذخیره", callback_data="admin:plans:save_confirmed")
        builder.button(text="❌ لغو", callback_data="admin:plans:cancel_add")
        builder.adjust(1)
        
        await message.answer(confirmation_text, reply_markup=builder.as_markup(), parse_mode="HTML")
        
    except ValueError:
        await message.reply(Messages.Admin.PlanManagement.INVALID_PRICE)

# @router.callback_query(StateFilter(PlanManagementState.waiting_for_plan_confirmation))
async def handle_plan_confirmation_OLD_DISABLED(callback_query: CallbackQuery, state: FSMContext):
    admin_id = callback_query.from_user.id
    action = callback_query.data.split(":")[-1]
    
    if action == "save_confirmed":
        data = await state.get_data()
        edit_plan_id = data.get('edit_plan_id')
        
        plan_id = edit_plan_id or data.get('plan_id')
        plan_name = data.get('plan_name')
        plan_description = data.get('plan_description')
        plan_days = data.get('plan_days')
        plan_volume = data.get('plan_volume', 0)
        plan_price = data.get('plan_price', 0)
        
        if save_plan(plan_id, plan_name, plan_description, plan_days, plan_volume, plan_price):
            await callback_query.message.edit_text(Messages.Admin.PlanManagement.PLAN_SAVED)
            await state.clear()
            # Show updated plans list
            await asyncio.sleep(1)
            plans = get_all_plans()
            plan_list_text = Messages.Admin.PlanManagement.PLAN_LIST_TITLE
            for plan in plans:
                plan_list_text += Messages.Admin.PlanManagement.PLAN_ITEM(plan)
            from keyboards import create_plans_list_keyboard
            await callback_query.message.answer(
                plan_list_text,
                reply_markup=create_plans_list_keyboard(plans),
                parse_mode="HTML"
            )
        else:
            await callback_query.message.edit_text(Messages.Admin.PlanManagement.PLAN_SAVE_ERROR)
            await state.clear()
    
    elif action == "cancel_add":
        await state.clear()
        from keyboards import create_plan_management_menu
        await callback_query.message.edit_text(
            Messages.Admin.PlanManagement.TITLE,
            reply_markup=create_plan_management_menu(),
            parse_mode="HTML"
        )
    
    await callback_query.answer()


# --- Fallback Handler for Admin States ---
@router.message(StateFilter(AddUserState, SearchUserState, EditUserState, SendMessageState, ButtonManagementState, EmergencyConfigState, PlanManagementState), F.content_type.in_({ContentType.PHOTO, ContentType.STICKER, ContentType.VIDEO, ContentType.DOCUMENT, ContentType.AUDIO}))
async def handle_wrong_content_in_admin_state(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    current_state = await state.get_state()
    logger.warning(f"Admin {admin_id} sent wrong content type ({message.content_type}) while in state {current_state}.")
    await message.reply(Messages.General.UNEXPECTED_MESSAGE_IN_STATE)

# TEST: Error Handler Test Command (Only for Admin)
@router.message(Command(commands=["test_error"]), F.from_user.id == ADMIN_USER_ID)
async def test_error_handler(message: Message):
    """Test command to trigger an intentional error for testing error handling"""
    logger.info(f"Admin {message.from_user.id} triggered test error command")
    
    # Intentionally raise an exception to test error handling
    raise ValueError("این یک خطای تستی است برای آزمایش سیستم error handling")

# --- Admin Approval Handlers ---
@router.callback_query(F.data.startswith("admin_confirm_buy:"))
async def handle_admin_confirm_buy(callback_query: CallbackQuery, bot: Bot):
    admin_id = callback_query.from_user.id
    try:
        _, user_id_str, plan_id = callback_query.data.split(":")
        user_id = int(user_id_str)
        logger.info(
            f"Admin {admin_id} CONFIRMED BUY for user {user_id}, plan: {plan_id}"
        )
        await callback_query.answer("در حال پردازش تایید خرید...")
        original_caption = callback_query.message.caption
        try:
            safe_caption_edit = html_escape(original_caption or "")
            await callback_query.message.edit_caption(
                caption=safe_caption_edit + "\n\n✅ تایید شده توسط ادمین",
                reply_markup=None,
                parse_mode="HTML"
            )
        except Exception as edit_err:
            logger.warning(
                f"Could not edit caption for buy confirmation (User: {user_id}): {edit_err}"
            )

        old_trial_uuid = None
        existing_uuid = None
        user_db_check = get_user_from_database(user_id)

        if user_db_check and user_db_check[4]:  # User has a UUID
            if user_db_check[5] == 1:  # is_trial flag
                old_trial_uuid = user_db_check[4]
                logger.warning(f"User {user_id} has an existing trial subscription (UUID: {old_trial_uuid}). It will be deleted after successful purchase.")
            else:  # User has a regular subscription - this should be a renewal, not a new purchase
                existing_uuid = user_db_check[4]
                logger.warning(f"User {user_id} already has an active subscription (UUID: {existing_uuid}). This should be handled as a renewal, not a new purchase.")
                await callback_query.message.answer(
                    f"⚠️ خطا: کاربر {user_id} قبلاً اشتراک فعال دارد (UUID: {existing_uuid}).\n"
                    f"این درخواست باید به عنوان تمدید پردازش شود، نه خرید جدید.\n"
                    f"لطفاً از دکمه تمدید استفاده کنید.",
                    parse_mode="HTML"
                )
                return

        plan_full_details = get_plan_details(plan_id)
        package_days = plan_full_details.get("days", 30)
        usage_limit_gb = plan_full_details.get("limit_gb", 0)
        mode = plan_full_details.get("mode", "no_reset")
        plan_description = plan_full_details.get("desc", "پلن خریداری شده")
        plan_price_for_log = plan_full_details.get("price", 0)

        telegram_name = user_db_check[0] if user_db_check and user_db_check[0] else f"User_{user_id}"
        telegram_username = user_db_check[1] if user_db_check and user_db_check[1] else "NO_ID"
        if telegram_name == f"User_{user_id}":
            try:
                user_chat = await bot.get_chat(user_id)
                if user_chat.full_name: telegram_name = user_chat.full_name
                if user_chat.username: telegram_username = f"@{user_chat.username}"
            except Exception as e:
                logger.error(f"Could not get chat info for user {user_id}: {e}.")

        paid_user_name = f"{telegram_name}"
        comment_action = "upgrade_from_trial" if old_trial_uuid else "purchase"
        payload = {
            "comment": create_comment_with_history(comment_action, plan_description, admin_id, user_id),
            "current_usage_GB": 0, "enable": True, "lang": "fa", "mode": mode,
            "name": paid_user_name, "package_days": package_days,
            "start_date": datetime.date.today().strftime("%Y-%m-%d"),
            "telegram_id": user_id, "usage_limit_GB": usage_limit_gb,
        }
        payload_cleaned = {k: v for k, v in payload.items() if v is not None}
        is_success, response, status = await add_user_api(payload_cleaned)

        if is_success:
            new_paid_uuid = json.loads(response).get("uuid")
            if new_paid_uuid:
                if old_trial_uuid:
                    logger.warning(f"Attempting to delete old trial user {old_trial_uuid} from panel...")
                    del_success, _, _ = await delete_user_api(old_trial_uuid)
                    if not del_success:
                        logger.error(f"Failed to delete old trial user {old_trial_uuid} from panel.")
                        await callback_query.message.answer(f"⚠️ کاربر جدید ساخته شد، اما خطایی در حذف کاربر تست قدیمی {html_escape(old_trial_uuid)} رخ داد.", parse_mode="HTML")

                join_time_shamsi = jdatetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
                update_or_add_user(paid_user_name, telegram_username, user_id, join_time_shamsi, new_paid_uuid, is_trial=0)
                log_income_transaction(user_tg_id=user_id, user_uuid=new_paid_uuid, transaction_type='buy', plan_id=plan_id, plan_description=plan_description, amount_paid=plan_price_for_log, admin_approver_id=admin_id)

                if TELEGRAM_REPORT_CHAT:
                    try:
                        await bot.copy_message(chat_id=TELEGRAM_REPORT_CHAT, from_chat_id=callback_query.message.chat.id, message_id=callback_query.message.message_id)
                    except Exception as e:
                        logger.warning(f"Failed to copy approved receipt to report channel: {e}")
                
                # Send panel info first
                new_user_data_db = get_user_from_database(user_id)
                if new_user_data_db:
                    from .user_handlers import send_user_panel_info
                    await send_user_panel_info(bot=bot, user_data_db=new_user_data_db, is_first_time_auth=True)

                # Then send the success message
                try:
                    await bot.send_message(user_id, Messages.User.Purchase.SUCCESS_MESSAGE)
                except Exception as e:
                    logger.error(f"Failed to send purchase success message to user {user_id}: {e}")

                await callback_query.message.answer(f"✅ خرید کاربر {html_escape(paid_user_name)} (ID: {user_id}) تایید شد.\n\nUUID جدید: <code>{html_escape(new_paid_uuid)}</code>", parse_mode="HTML")
            else:
                await callback_query.message.answer(f"⚠️ خطا: UUID در پاسخ API یافت نشد برای کاربر {user_id}")
        else:
            await callback_query.message.answer(f"⚠️ خطا در API: وضعیت {status} برای کاربر {user_id}\nخطا: {html_escape(response)}", parse_mode="HTML")
            
    except Exception as e:
        logger.error(f"Error in handle_admin_confirm_buy: {e}", exc_info=True)
        await callback_query.answer("خطای داخلی رخ داد", show_alert=True)

@router.callback_query(F.data.startswith("admin_confirm_renew:"))
async def handle_admin_confirm_renew(callback_query: CallbackQuery, bot: Bot):
    admin_id = callback_query.from_user.id
    try:
        _, user_id_str, plan_id = callback_query.data.split(":")
        user_tg_id = int(user_id_str)
        logger.info(f"Admin {admin_id} CONFIRMED RENEW for user {user_tg_id}, plan: {plan_id}")

        user_db_data = get_user_from_database(user_tg_id)
        if not user_db_data or not user_db_data[4]:
            logger.error(f"Cannot renew: User or UUID not found in local DB for TG ID {user_tg_id}.")
            await callback_query.answer("خطا: کاربر یا UUID در دیتابیس محلی یافت نشد!", show_alert=True)
            return

        user_uuid = user_db_data[4]
        await callback_query.answer("⏳ در حال پردازش تمدید...")
        try:
            original_caption = callback_query.message.caption or ""
            await callback_query.message.edit_caption(caption=html_escape(original_caption) + "\n\n✅ تمدید تایید شد", reply_markup=None, parse_mode="HTML")
        except Exception as edit_err:
            logger.warning(f"Could not edit caption for renewal confirmation (User: {user_uuid}): {edit_err}")

        renew_success, renew_response, renew_status = await renew_user_subscription(uuid=user_uuid, plan_id=plan_id, admin_id=admin_id, user_tg_id=user_tg_id, bot=bot)

        if renew_success:
            logger.info(f"Successfully renewed subscription for user {user_tg_id} (UUID: {user_uuid})")
            plan_details = get_plan_details(plan_id)
            log_income_transaction(user_tg_id=user_tg_id, user_uuid=user_uuid, transaction_type='renewal', plan_id=plan_id, plan_description=plan_details.get("desc", "N/A"), amount_paid=plan_details.get("price", 0), admin_approver_id=admin_id)
            
            if TELEGRAM_REPORT_CHAT:
                try:
                    await bot.copy_message(chat_id=TELEGRAM_REPORT_CHAT, from_chat_id=callback_query.message.chat.id, message_id=callback_query.message.message_id)
                except Exception as e:
                    logger.warning(f"Failed to copy approved renewal receipt to report channel: {e}")

            await callback_query.message.answer(f"✅ تمدید کاربر با شناسه <code>{user_uuid}</code> با موفقیت انجام شد.", parse_mode="HTML")
            
            updated_user_data = get_user_from_database(user_tg_id)
            if updated_user_data:
                from .user_handlers import send_user_panel_info
                await send_user_panel_info(bot=bot, user_data_db=updated_user_data)
            
            await bot.send_message(user_tg_id, Messages.User.Renewal.SUCCESS_MESSAGE)
        else:
            error_message = f"API Error: {renew_response}" if renew_response else f"Status: {renew_status}"
            await callback_query.message.answer(f"❌ تمدید کاربر با شناسه <code>{user_uuid}</code> ناموفق بود.\nخطا: {html_escape(error_message)}", parse_mode="HTML")
            await bot.send_message(user_tg_id, Messages.User.Renewal.FAILURE_MESSAGE)
                
    except Exception as e:
        logger.error(f"Critical error in handle_admin_confirm_renew: {e}", exc_info=True)
        await callback_query.answer("خطای داخلی بسیار جدی رخ داد. لاگ‌ها را بررسی کنید.", show_alert=True)

@router.callback_query(F.data.startswith("admin_reject:"))
async def handle_admin_reject(callback_query: CallbackQuery, bot: Bot):
    admin_id = callback_query.from_user.id
    try:
        _, action_type, user_id_str = callback_query.data.split(":")
        user_id = int(user_id_str)
        action_text = "خرید" if action_type == "buy" else "تمدید"
        logger.warning(
            f"Admin {admin_id} REJECTED {action_text} for user {user_id}."
        )
        await callback_query.answer(f"درخواست {action_text} رد شد")

        original_caption = callback_query.message.caption
        original_photo_file_id = callback_query.message.photo[-1].file_id if callback_query.message.photo else None

        try:
            safe_caption_edit = html_escape(original_caption or "")
            await callback_query.message.edit_caption(
                caption=safe_caption_edit + "\n\n❌ رد شده توسط ادمین",
                reply_markup=None,
                parse_mode="HTML"
            )
        except Exception as edit_err:
             logger.warning(f"Could not edit caption for rejection (User: {user_id}): {edit_err}")

        # CRITICAL FIX: Clear the user's FSM state so they can use buttons again
        try:
            # We need access to the dispatcher's storage to clear user state
            # Since we can't access it directly, we'll trigger state clearing via a callback
            logger.info(f"Attempting to clear FSM state for user {user_id} after rejection")
            
            # Send a message with an inline button that will trigger state clearing
            builder = InlineKeyboardBuilder()
            builder.button(text="🔄 منو اصلی", callback_data=f"user:clear_state_and_menu:{action_type}")
            
            await bot.send_message(
                user_id, 
                "❌ درخواست شما رد شده است.\n\n"
                "برای بازگشت به منو اصلی و ادامه استفاده از ربات، روی دکمه زیر کلیک کنید:",
                reply_markup=builder.as_markup()
            )
            
        except Exception as state_clear_err:
            logger.error(f"Failed to clear state for user {user_id} after rejection: {state_clear_err}")
            # Fallback: send a simple message
            await bot.send_message(
                user_id, 
                "❌ درخواست شما رد شده است. لطفاً دستور /start را ارسال کنید تا منو اصلی نمایش داده شود.",
                reply_markup=create_user_initial_menu() if action_type == "buy" else create_user_regular_menu(user_id)
            )

        # Note: We removed the duplicate rejection message here since we already send one above
        logger.info(f"Rejection message sent to user {user_id}.")

        await callback_query.message.answer(
            f"❌ درخواست {action_text} کاربر {user_id} رد شد."
        )

        # Send a copy of the rejected receipt to the report channel (if configured)
        if TELEGRAM_REPORT_CHAT:
            try:
                await bot.copy_message(chat_id=TELEGRAM_REPORT_CHAT,
                                       from_chat_id=callback_query.message.chat.id,
                                       message_id=callback_query.message.message_id)
                logger.info(f"Copied rejected receipt to report channel for user {user_id}.")
            except Exception as e:
                logger.warning(f"Failed to copy rejected receipt to report channel: {e}")

    except ValueError:
        logger.error(
            f"Invalid callback data format for admin_reject: {callback_query.data}"
        )
        await callback_query.answer("خطا در فرمت داده", show_alert=True)
    except Exception as e:
        logger.error(f"Error in handle_admin_reject: {e}", exc_info=True)
        await callback_query.answer("خطای داخلی رخ داد", show_alert=True)


# NEW: JSON Database Management Functions
async def handle_json_database_management(message: Message):
    """Handle JSON database management for admin"""
    admin_id = message.from_user.id
    logger.info(f"Admin {admin_id} accessing JSON database management")
    
    # Import here to avoid circular imports
    from database import get_json_database_stats, force_sync_all_to_json, backup_json_files
    
    processing_msg = await message.answer("🔄 در حال دریافت آمار JSON Database...")
    
    try:
        # Get JSON stats
        stats = get_json_database_stats()
        
        # Build stats message
        stats_text = "🗄️ <b>آمار JSON Database</b>\n\n"
        
        total_size_kb = 0
        total_records = 0
        
        for name, data in stats.items():
            name_fa = {
                'users': 'کاربران',
                'panel_users': 'کاربران پنل',
                'deleted_users': 'کاربران حذف شده',
                'income_transactions': 'تراکنش‌های درآمد',
                'bot_settings': 'تنظیمات ربات',
                'emergency_config': 'کانفیگ اضطراری',
                'subscription_plans': 'پلن‌های اشتراک'
            }.get(name, name)
            
            if data.get('exists', False):
                stats_text += f"📁 <b>{name_fa}:</b>\n"
                stats_text += f"   📊 تعداد رکورد: {data['record_count']}\n"
                stats_text += f"   💾 حجم: {data['size_kb']} KB\n"
                stats_text += f"   🕐 آخرین بروزرسانی: {data['last_modified']}\n\n"
                
                total_size_kb += data['size_kb']
                total_records += data['record_count']
            else:
                stats_text += f"❌ <b>{name_fa}:</b> فایل وجود ندارد\n\n"
        
        stats_text += f"📈 <b>خلاصه کلی:</b>\n"
        stats_text += f"💽 کل حجم: {total_size_kb:.2f} KB\n"
        stats_text += f"📋 کل رکوردها: {total_records}\n"
        
        # Create management buttons
        builder = InlineKeyboardBuilder()
        builder.button(text="🔄 همگام‌سازی SQLite → JSON", callback_data="admin:json:sync")
        builder.button(text="💾 پشتیبان‌گیری JSON", callback_data="admin:json:backup")
        builder.button(text="📊 بروزرسانی آمار", callback_data="admin:json:refresh_stats")
        builder.adjust(1)
        
        await processing_msg.delete()
        await message.answer(stats_text, reply_markup=builder.as_markup(), parse_mode="HTML")
        
    except Exception as e:
        logger.error(f"Error generating JSON stats for admin {admin_id}: {e}", exc_info=True)
        await processing_msg.edit_text("❌ خطا در دریافت آمار JSON Database")


# NEW: JSON Database Callback Handlers
@router.callback_query(F.data.startswith("admin:json:"))
async def handle_json_database_callbacks(callback_query: CallbackQuery):
    admin_id = callback_query.from_user.id
    action = callback_query.data.split(":")[2]
    logger.info(f"Admin {admin_id} performing JSON database action: {action}")
    await callback_query.answer()

    if action == "sync":
        await callback_query.message.edit_text("⏳ در حال همگام‌سازی اجباری پایگاه داده با فایل‌های JSON...")
        try:
            force_sync_all_to_json()  # No await
            await callback_query.message.edit_text("✅ همگام‌سازی با موفقیت انجام شد.")
        except Exception as e:
            logger.error(f"Error in forced JSON sync: {e}", exc_info=True)
            await callback_query.message.edit_text(f"❌ خطا در همگام‌سازی: {e}")
        await asyncio.sleep(2)
        await handle_json_database_management(callback_query.message)

    elif action == "backup":
        try:
            await callback_query.message.edit_text("⏳ در حال ایجاد فایل پشتیبان از JSON...")
            success, backup_path, file_count = backup_json_files()  # No await
            if success and backup_path:
                zip_path = f"{backup_path}.zip"
                shutil.make_archive(backup_path, 'zip', backup_path)
                
                with open(zip_path, "rb") as backup_file:
                    await callback_query.message.answer_document(
                        BufferedInputFile(backup_file.read(), filename=os.path.basename(zip_path)),
                        caption=f"✅ پشتیبان‌گیری از {file_count} فایل با موفقیت در مسیر زیر ایجاد شد:\n<code>{os.path.basename(backup_path)}</code>\n\nفایل فشرده برای دانلود ارسال شد.",
                        parse_mode="HTML"
                    )
                
                os.remove(zip_path)
                shutil.rmtree(backup_path)
                await callback_query.message.edit_text("✅ عملیات پشتیبان‌گیری با موفقیت انجام شد.")
            else:
                await callback_query.message.edit_text(f"❌ خطایی در ایجاد پشتیبان رخ داد. مسیر بکاپ: {backup_path}")
        except Exception as e:
            logger.error(f"Error in JSON backup: {e}", exc_info=True)
            await callback_query.message.answer(f"❌ خطا در پشتیبان‌گیری: {e}")
        await asyncio.sleep(3)
        await handle_json_database_management(callback_query.message)
        
    elif action == "refresh_stats":
        await handle_json_database_management(callback_query.message)


# --- FINAL FALLBACK HANDLERS (Must be at the very end) ---

@router.message(StateFilter(None))
async def handle_unhandled_admin_messages(message: Message, state: FSMContext):
    """Final fallback handler for admin messages with no specific state"""
    admin_id = message.from_user.id
    text = message.text or f"[{message.content_type}]"
    logger.warning(f"Unhandled admin message from {admin_id}: '{text[:50]}...' (content_type: {message.content_type})")
    
    await message.answer(
        "🤔 پیام شناخته نشد. لطفا از دکمه‌های منوی ادمین استفاده کنید:",
        reply_markup=create_main_menu(admin_id)
    )


@router.callback_query(
    ~F.data.startswith("admin:send_msg:buttons:") &
    ~F.data.startswith("admin:send_msg:confirm") &
    ~F.data.startswith("admin:send_msg:cancel") &
    ~F.data.startswith("admin:income:") &
    ~F.data.startswith("admin:toggle_button:") &
    ~F.data.startswith("admin:bank") &
    ~F.data.startswith("admin:emergency:") &
    ~F.data.startswith("admin:button_management") &
    ~F.data.startswith("admin:emergency_config") &
    ~F.data.startswith("admin:income_status") &
    ~F.data.startswith("admin:backup:") &
    ~F.data.startswith("admin:json:")
)
async def handle_unhandled_admin_callbacks(callback_query: CallbackQuery, state: FSMContext):
    """Final fallback handler for all unhandled admin callback queries, excluding those handled by additional_admin_handlers"""
    admin_id = callback_query.from_user.id
    data = callback_query.data or "unknown"
    logger.warning(f"Unhandled admin callback from {admin_id}: '{data}'")
    
    # Clear any stuck states
    current_state = await state.get_state()
    if current_state:
        await state.clear()
        logger.info(f"Cleared stuck admin state '{current_state}' for admin {admin_id}")
    
    # Answer the callback to prevent loading
    await callback_query.answer("🤔 عملیات شناخته نشد", show_alert=True)
    
    try:
        await callback_query.message.edit_text("🔄 لطفا از دکمه‌های منوی ادمین استفاده کنید:")
        await callback_query.message.answer("📱 منوی ادمین:", reply_markup=create_main_menu(admin_id))
    except Exception as e:
        logger.warning(f"Could not edit admin callback message: {e}")
        await callback_query.message.answer(
            "🔄 لطفا از دکمه‌های منوی ادمین استفاده کنید:",
            reply_markup=create_main_menu(admin_id)
        )

# Emergency handler for states that should be handled by additional_admin_handlers.py
@router.message(StateFilter(EmergencyConfigState, ButtonManagementState, SendMessageState))
async def handle_additional_admin_states_emergency(message: Message, state: FSMContext):
    """Emergency handler for states that should be processed by additional_admin_handlers.py"""
    admin_id = message.from_user.id
    current_state = await state.get_state()
    logger.error(f"Emergency handler triggered for admin {admin_id} in state {current_state} - this should be handled by additional_admin_handlers!")
    
    # Clear the stuck state
    await state.clear()
    logger.info(f"Cleared stuck admin state '{current_state}' for admin {admin_id}")
    
    if current_state == EmergencyConfigState.waiting_for_emergency_message:
        await message.answer(
            "❌ خطا در پردازش پیام اضطراری. لطفاً دوباره تلاش کنید.",
            reply_markup=create_main_menu(admin_id)
        )
    elif "ButtonManagement" in current_state:
        await message.answer(
            "❌ خطا در تنظیمات بانکی. لطفاً دوباره تلاش کنید.",
            reply_markup=create_main_menu(admin_id)
        )
    elif "SendMessage" in current_state:
        await message.answer(
            "❌ خطا در ارسال پیام. لطفاً دوباره تلاش کنید.",
            reply_markup=create_main_menu(admin_id)
        )
    else:
        await message.answer(
            "❌ خطا در پردازش درخواست. لطفاً دوباره تلاش کنید.",
            reply_markup=create_main_menu(admin_id)
        )

# NEW: Individual field editing handlers
@router.message(StateFilter(PlanManagementState.waiting_for_edit_name), F.text)
async def process_edit_name(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    new_name = message.text.strip()
    logger.info(f"Admin {admin_id} editing plan name to: {new_name}")
    
    try:
        data = await state.get_data()
        plan_id = data.get('edit_plan_id')
        
        if not plan_id:
            await message.reply("❌ خطا در شناسه پلن")
            await state.clear()
            return
            
        # Update plan in database (create helper function for individual field updates)
        success = update_plan_field(plan_id, 'plan_name', new_name)
        
        if success:
            await message.answer(Messages.Admin.PlanManagement.FIELD_UPDATED_SUCCESS("نام پلن", new_name))
            await state.clear()
            
            # Show updated plan info
            plan = get_plan_by_id(plan_id)
            if plan:
                plan_info = Messages.Admin.PlanManagement.PLAN_ITEM(plan)
                from keyboards import create_plan_edit_keyboard
                await message.answer(
                    f"📋 <b>مشخصات به‌روزرسانی شده:</b>\n\n{plan_info}",
                    reply_markup=create_plan_edit_keyboard(plan_id),
                    parse_mode="HTML"
                )
        else:
            await message.answer(Messages.Admin.PlanManagement.FIELD_UPDATE_ERROR("نام پلن"))
            await state.clear()
    except Exception as e:
        logger.error(f"Error updating plan name: {e}")
        await message.answer("❌ خطا در به‌روزرسانی نام پلن")
        await state.clear()

@router.message(StateFilter(PlanManagementState.waiting_for_edit_description), F.text)
async def process_edit_description(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    new_description = message.text.strip()
    logger.info(f"Admin {admin_id} editing plan description")
    
    try:
        data = await state.get_data()
        plan_id = data.get('edit_plan_id')
        
        if not plan_id:
            await message.reply("❌ خطا در شناسه پلن")
            await state.clear()
            return
            
        success = update_plan_field(plan_id, 'description', new_description)
        
        if success:
            await message.answer(Messages.Admin.PlanManagement.FIELD_UPDATED_SUCCESS("توضیحات پلن", new_description))
            await state.clear()
            
            plan = get_plan_by_id(plan_id)
            if plan:
                plan_info = Messages.Admin.PlanManagement.PLAN_ITEM(plan)
                from keyboards import create_plan_edit_keyboard
                await message.answer(
                    f"📋 <b>مشخصات به‌روزرسانی شده:</b>\n\n{plan_info}",
                    reply_markup=create_plan_edit_keyboard(plan_id),
                    parse_mode="HTML"
                )
        else:
            await message.answer(Messages.Admin.PlanManagement.FIELD_UPDATE_ERROR("توضیحات پلن"))
            await state.clear()
    except Exception as e:
        logger.error(f"Error updating plan description: {e}")
        await message.answer("❌ خطا در به‌روزرسانی توضیحات پلن")
        await state.clear()

@router.message(StateFilter(PlanManagementState.waiting_for_edit_days), F.text)
async def process_edit_days(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    logger.info(f"Admin {admin_id} editing plan days")
    
    try:
        new_days = int(message.text.strip())
        if new_days <= 0:
            raise ValueError
            
        data = await state.get_data()
        plan_id = data.get('edit_plan_id')
        
        if not plan_id:
            await message.reply("❌ خطا در شناسه پلن")
            await state.clear()
            return
            
        success = update_plan_field(plan_id, 'days', new_days)
        
        if success:
            await message.answer(Messages.Admin.PlanManagement.FIELD_UPDATED_SUCCESS("تعداد روزها", f"{new_days} روز"))
            await state.clear()
            
            plan = get_plan_by_id(plan_id)
            if plan:
                plan_info = Messages.Admin.PlanManagement.PLAN_ITEM(plan)
                from keyboards import create_plan_edit_keyboard
                await message.answer(
                    f"📋 <b>مشخصات به‌روزرسانی شده:</b>\n\n{plan_info}",
                    reply_markup=create_plan_edit_keyboard(plan_id),
                    parse_mode="HTML"
                )
        else:
            await message.answer(Messages.Admin.PlanManagement.FIELD_UPDATE_ERROR("تعداد روزها"))
            await state.clear()
    except ValueError:
        await message.reply(Messages.Admin.PlanManagement.INVALID_DAYS)
    except Exception as e:
        logger.error(f"Error updating plan days: {e}")
        await message.answer("❌ خطا در به‌روزرسانی تعداد روزها")
        await state.clear()

@router.message(StateFilter(PlanManagementState.waiting_for_edit_volume), F.text)
async def process_edit_volume(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    logger.info(f"Admin {admin_id} editing plan volume")
    
    try:
        new_volume = float(message.text.strip())
        if new_volume <= 0:
            raise ValueError
            
        data = await state.get_data()
        plan_id = data.get('edit_plan_id')
        
        if not plan_id:
            await message.reply("❌ خطا در شناسه پلن")
            await state.clear()
            return
            
        success = update_plan_field(plan_id, 'volume_gb', new_volume)
        
        if success:
            await message.answer(Messages.Admin.PlanManagement.FIELD_UPDATED_SUCCESS("حجم پلن", f"{new_volume} گیگابایت"))
            await state.clear()
            
            plan = get_plan_by_id(plan_id)
            if plan:
                plan_info = Messages.Admin.PlanManagement.PLAN_ITEM(plan)
                from keyboards import create_plan_edit_keyboard
                await message.answer(
                    f"📋 <b>مشخصات به‌روزرسانی شده:</b>\n\n{plan_info}",
                    reply_markup=create_plan_edit_keyboard(plan_id),
                    parse_mode="HTML"
                )
        else:
            await message.answer(Messages.Admin.PlanManagement.FIELD_UPDATE_ERROR("حجم پلن"))
            await state.clear()
    except ValueError:
        await message.reply(Messages.Admin.PlanManagement.INVALID_VOLUME)
    except Exception as e:
        logger.error(f"Error updating plan volume: {e}")
        await message.answer("❌ خطا در به‌روزرسانی حجم پلن")
        await state.clear()

@router.message(StateFilter(PlanManagementState.waiting_for_edit_price), F.text)
async def process_edit_price(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    logger.info(f"Admin {admin_id} editing plan price")
    
    try:
        new_price = int(message.text.strip())
        if new_price <= 0:
            raise ValueError
            
        data = await state.get_data()
        plan_id = data.get('edit_plan_id')
        
        if not plan_id:
            await message.reply("❌ خطا در شناسه پلن")
            await state.clear()
            return
            
        success = update_plan_field(plan_id, 'price_toman', new_price)
        
        if success:
            await message.answer(Messages.Admin.PlanManagement.FIELD_UPDATED_SUCCESS("قیمت پلن", f"{new_price:,} تومان"))
            await state.clear()
            
            plan = get_plan_by_id(plan_id)
            if plan:
                plan_info = Messages.Admin.PlanManagement.PLAN_ITEM(plan)
                from keyboards import create_plan_edit_keyboard
                await message.answer(
                    f"📋 <b>مشخصات به‌روزرسانی شده:</b>\n\n{plan_info}",
                    reply_markup=create_plan_edit_keyboard(plan_id),
                    parse_mode="HTML"
                )
        else:
            await message.answer(Messages.Admin.PlanManagement.FIELD_UPDATE_ERROR("قیمت پلن"))
            await state.clear()
    except ValueError:
        await message.reply(Messages.Admin.PlanManagement.INVALID_PRICE)
    except Exception as e:
        logger.error(f"Error updating plan price: {e}")
        await message.answer("❌ خطا در به‌روزرسانی قیمت پلن")
        await state.clear()

# --- Backup Inline Keyboard Callbacks ---

@router.callback_query(F.data.startswith("admin:backup:"))
async def handle_admin_backup(callback_query: CallbackQuery, state: FSMContext, bot: Bot):
    """Handle inline backup actions: instant, latest, schedule."""
    action = callback_query.data.split(":")[2]

    if action == "instant":
        await callback_query.answer("⏳ در حال ایجاد بکاپ لحظه‌ای…", show_alert=False)
        from scheduler_tasks import send_instant_backup_to_admin
        processing_msg = await callback_query.message.answer(Messages.Admin.Backup.FETCHING)
        await send_instant_backup_to_admin(bot)
        await processing_msg.delete()

    elif action == "latest":
        await callback_query.answer("⏳ در حال دریافت آخرین بکاپ…", show_alert=False)
        processing_msg = await callback_query.message.answer(Messages.Admin.Backup.FETCHING)
        success, data, filename = await fetch_latest_backup_ssh()
        await processing_msg.delete()
        if success and data and filename:
            proxy_path = f"{PANEL_LINK_PREFIX}{HIDDIFY_API_PROXY_PATH}"
            doc = BufferedInputFile(data.getvalue(), filename=filename)
            caption = Messages.Admin.Backup.SENDING_CAPTION(filename=filename, proxy_path=proxy_path)
            await callback_query.message.answer_document(doc, caption=caption)
        else:
            await callback_query.message.answer(Messages.Admin.Backup.FETCH_ERROR(error=str(data)))
    elif action == "schedule":
        await state.set_state(BackupScheduleState.waiting_for_time)
        await callback_query.message.answer("⏰ لطفا زمان ارسال بکاپ (به‌صورت HH:MM 24h) را وارد کنید:\nمثال: 03:15")
        await callback_query.answer()


# --- Backup Schedule Time Input ---

@router.message(StateFilter(BackupScheduleState.waiting_for_time), F.text)
async def process_backup_schedule_time(message: Message, state: FSMContext, bot: Bot):
    time_str = message.text.strip()
    try:
        hour, minute = map(int, time_str.split(":"))
        if not (0 <= hour <= 23 and 0 <= minute <= 59):
            raise ValueError
    except ValueError:
        await message.reply("❌ فرمت زمان نامعتبر است. لطفا به صورت HH:MM وارد کنید. مثال: 03:15")
        return

    from scheduler_tasks import scheduler as global_scheduler, send_instant_backup_to_admin
    if not global_scheduler:
        await message.reply("❌ Scheduler در دسترس نیست. لطفا بعدا تلاش کنید.")
        await state.clear()
        return

    job_id = "scheduled_instant_backup"
    try:
        if global_scheduler.get_job(job_id):
            global_scheduler.remove_job(job_id)
        global_scheduler.add_job(send_instant_backup_to_admin, 'cron', hour=hour, minute=minute, args=[bot], id=job_id)
        await message.reply(f"✅ بکاپ لحظه‌ای روزانه در ساعت {hour:02d}:{minute:02d} برنامه‌ریزی شد.")
    except Exception as e:
        await message.reply(f"❌ خطا در زمان‌بندی بکاپ: {e}")
    finally:
        await state.clear()

async def renew_user_subscription(uuid: str, plan_id: str, admin_id: int, user_tg_id: int, bot: Bot) -> Tuple[bool, str, int]:
    """
    Renews a user's subscription based on the selected plan and configured renewal method.
    Fetches user's current status, calculates new expiry and usage, and applies the changes.
    """
    logger.info(f"Initiating renewal process for UUID: {uuid} with plan: {plan_id}")

    success, user_info_str, status = await fetch_user_details_api(uuid)
    if not success:
        logger.error(f"Failed to fetch user info for {uuid} during renewal. Status: {status}, Error: {user_info_str}")
        return False, f"API Error: {user_info_str}", status

    try:
        user_info = json.loads(user_info_str)
    except json.JSONDecodeError:
        logger.error(f"Failed to parse JSON for user info: {user_info_str}")
        return False, "API response parse error", -1

    plan_details = get_plan_details(plan_id)
    if not plan_details:
        logger.error(f"Could not find details for plan_id: {plan_id} during renewal.")
        return False, "Plan details not found", -1

    renewal_method = get_renewal_method()
    logger.info(f"Applying renewal method: {renewal_method} for user {uuid}")
    logger.debug(f"Renewal method details - Method: {renewal_method}, Plan days: {plan_details.get('days')}, Plan GB: {plan_details.get('limit_gb')}")

    current_limit_gb = float(user_info.get("usage_limit_GB", 0))
    expiry_date_str = user_info.get("expiry_date")
    
    remaining_days = 0
    if expiry_date_str:
        try:
            expiry_date = datetime.date.fromisoformat(expiry_date_str)
            today = datetime.date.today()
            if expiry_date > today:
                remaining_days = (expiry_date - today).days
        except (ValueError, TypeError):
            logger.warning(f"Could not parse expiry date '{expiry_date_str}' for user {uuid}. Assuming 0 remaining days.")

    add_days = plan_details.get("days", 30)
    add_gb = float(plan_details.get("limit_gb", 0))
    plan_description = plan_details.get("desc", "تمدید اشتراک")

    payload_update = {}
    
    # FIX: Reset current usage for ALL methods as per UI description.
    payload_update['current_usage_GB'] = 0

    if renewal_method == 1: # Reset days & volume
        payload_update['package_days'] = add_days
        payload_update['usage_limit_GB'] = add_gb
        # Reset start_date to today for complete reset
        payload_update['start_date'] = datetime.date.today().isoformat()
    elif renewal_method == 2: # Aggregate days & volume
        payload_update['package_days'] = remaining_days + add_days
        payload_update['usage_limit_GB'] = current_limit_gb + add_gb
    elif renewal_method == 3: # Reset days, aggregate volume
        payload_update['package_days'] = add_days
        payload_update['usage_limit_GB'] = current_limit_gb + add_gb
        # Reset start_date to today when resetting days
        payload_update['start_date'] = datetime.date.today().isoformat()
    elif renewal_method == 4: # Aggregate days, reset volume
        payload_update['package_days'] = remaining_days + add_days
        payload_update['usage_limit_GB'] = add_gb

    payload_update['comment'] = create_comment_with_history("renewal", plan_description, admin_id, user_tg_id)

    logger.info(f"Updating user {uuid} with renewal payload: {payload_update}")
    renew_success, renew_response, renew_status = await update_user_api(uuid, payload_update)

    if not renew_success:
        logger.error(f"Failed to apply renewal update for {uuid}. Status: {renew_status}, Response: {renew_response}")

    return renew_success, renew_response, renew_status
