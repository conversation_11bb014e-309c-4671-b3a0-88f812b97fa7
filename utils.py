# hiddy_bot/utils.py

import os  # type: ignore
import re  # type: ignore
import logging  # type: ignore
import datetime  # type: ignore
import json  # type: ignore
import urllib.parse  # type: ignore
from io import BytesIO  # type: ignore
from typing import Union  # type: ignore
from html import escape as html_escape  # type: ignore

import pythonping  # type: ignore
import jdatetime  # type: ignore
import qrcode  # type: ignore
from aiogram import Bot, types  # type: ignore
from aiogram.utils.keyboard import InlineKeyboardBuilder  # type: ignore

from config import PROJECT_DIR, ADMIN_USER_ID, TELEGRAM_INFO_CHAT, HIDDIFY_API_DOMAIN, HIDDIFY_API_USER_PROXY_PATH  # type: ignore
from messages import Messages  # type: ignore
from api_hiddify import fetch_user_configs_api, fetch_user_details_api  # type: ignore

logger = logging.getLogger(__name__)


# --- UUID and Link Extraction ---
async def extract_uuid_from_input(user_input: str) -> Union[str, None]:
    """Extracts a UUID from various input formats including panel links."""
    logger.debug(f"Attempting to extract UUID from input: '{user_input[:100]}...'")
    
    # First try to extract from panel link format
    # Example: https://dlmbnvip.triutama.com/cJcPCKo42uPc1YMkzNKWBPeDZyqJUR/cf7a4306-ce87-4f5c-a6ba-dd80d4e6ddb7/?home=true
    panel_link_regex = re.compile(r"https?://[^/]+/[^/]+/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})", re.IGNORECASE)
    panel_match = panel_link_regex.search(user_input)
    if panel_match:
        extracted_uuid = panel_match.group(1)
        logger.debug(f"UUID extracted from panel link: {extracted_uuid}")
        return extracted_uuid
    
    # If not found in panel link format, try general UUID pattern
    uuid_regex = re.compile(r"([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})", re.IGNORECASE)
    match = uuid_regex.search(user_input)
    if match:
        extracted_uuid = match.group(1)
        logger.debug(f"UUID extracted successfully: {extracted_uuid}")
        return extracted_uuid
    else:
        logger.debug("No UUID found in the input string.")
        return None


# --- Network Utilities ---
async def ping_server(host: str, timeout: int = 1, count: int = 1) -> Union[int, None]:
    """Pings the server IP and returns average latency in ms or None on failure."""
    logger.debug(f"Pinging server '{host}' with timeout={timeout}s, count={count}.")
    
    if not host:
        logger.error("No host provided for ping")
        return None
        
    try:
        logger.debug(f"Starting ping to host: {host}")
        response_list = pythonping.ping(host, timeout=timeout, count=count, verbose=False)
        
        logger.debug(f"Ping response received. Success: {response_list.success}")
        logger.debug(f"RTT values: min={response_list.rtt_min_ms}, avg={response_list.rtt_avg_ms}, max={response_list.rtt_max_ms}")
        logger.debug(f"Response list length: {len(response_list._responses)} responses")
        
        if response_list.success:
            avg_latency = round(response_list.rtt_avg_ms)
            logger.info(f"Ping successful to '{host}'. Average latency: {avg_latency} ms (raw: {response_list.rtt_avg_ms} ms)")
            
            # Check if latency is 0 or very small
            if avg_latency == 0:
                logger.warning(f"Ping returned 0ms for '{host}' - this might indicate localhost or very fast response")
                # Return the raw value if it's very small but not 0
                if response_list.rtt_avg_ms > 0:
                    return max(1, round(response_list.rtt_avg_ms))  # Ensure at least 1ms
                else:
                    logger.warning(f"Raw RTT is also 0 for '{host}'")
            
            return avg_latency
        else:
            logger.warning(f"Ping to '{host}' failed - no successful responses received")
            logger.debug(f"Failed responses details: {len(response_list._responses)} total responses")
            return None
    except Exception as e:
        logger.error(f"Exception occurred during ping to '{host}': {e}", exc_info=True)
        return None


# --- QR Code Generation and Sending ---
def _generate_qr_code_bytes(data: str) -> Union[bytes, None]:
    """Generates a QR code and returns it as bytes."""
    logger.debug(f"Generating QR code for data: '{data[:50]}...'")
    try:
        qr = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_L, box_size=10, border=4)
        qr.add_data(data)
        qr.make(fit=True)
        img = qr.make_image(fill_color="black", back_color="white")
        bio = BytesIO()
        img.save(bio, format='PNG')
        qr_bytes = bio.getvalue()
        logger.debug(f"QR code generated successfully, size: {len(qr_bytes)} bytes.")
        return qr_bytes
    except Exception as e:
        logger.error(f"Failed to generate QR code. Error: {e}", exc_info=True)
        return None

async def send_subscription_qrcode(bot: Bot, chat_id: int, user_uuid: str):
    """Fetches subscription link, generates QR code, and sends it to a user with detailed info and smart buttons."""
    logger.info(f"Attempting to send enhanced subscription QR code for UUID {user_uuid} to chat {chat_id}.")
    
    # Get user details for enhanced info
    success, details_str, _ = await fetch_user_details_api(user_uuid)
    if not success:
        logger.error(f"Failed to fetch user details for QR code generation. UUID: {user_uuid}")
        await bot.send_message(chat_id, Messages.Admin.UserInfoEdit.SUB_QR_LINK_NOT_FOUND(user_uuid=user_uuid))
        return
    
    try:
        user_dict = json.loads(details_str)
        
        # Calculate dates and usage
        start_date_str = user_dict.get('start_date')
        start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d').date() if start_date_str else datetime.date.today()
        package_days = user_dict.get('package_days', 0)
        expiry_date = start_date + datetime.timedelta(days=package_days)
        remaining_days = max(0, (expiry_date - datetime.date.today()).days)
        jalali_expiry = jdatetime.date.fromgregorian(date=expiry_date).strftime('%Y/%m/%d')
        
        usage_limit_gb = user_dict.get('usage_limit_GB', 0.0)
        current_usage_gb = user_dict.get('current_usage_GB', 0.0)
        remaining_usage_gb = max(0.0, usage_limit_gb - current_usage_gb)
        user_name = user_dict.get('name', '')
        
    except (json.JSONDecodeError, Exception) as e:
        logger.error(f"Error parsing user details for QR code: {e}")
        await bot.send_message(chat_id, Messages.Admin.UserInfoEdit.SUB_QR_GENERATION_ERROR(user_uuid=user_uuid))
        return
    
    # Get subscription link
    is_success, configs_or_error, _ = await fetch_user_configs_api(user_uuid)
    sub_link = None
    if is_success and isinstance(configs_or_error, list):
        auto_config = next((c for c in configs_or_error if c.get('name') == 'Auto' and c.get('link')), None)
        sub_link = auto_config['link'] if auto_config else next((c.get('link') for c in configs_or_error if c.get('is_subscription')), None)

    if not sub_link:
        logger.warning(f"Could not find subscription link for UUID {user_uuid} to generate QR code.")
        await bot.send_message(chat_id, Messages.Admin.UserInfoEdit.SUB_QR_LINK_NOT_FOUND(user_uuid=user_uuid))
        return

    qr_bytes = _generate_qr_code_bytes(sub_link)
    if not qr_bytes:
        logger.error(f"QR code generation failed for sub link: {sub_link}")
        await bot.send_message(chat_id, Messages.Admin.UserInfoEdit.SUB_QR_GENERATION_ERROR(user_uuid=user_uuid))
        return
        
    caption = Messages.Admin.UserInfoEdit.SUB_QR_CAPTION(
        user_uuid=user_uuid, 
        sub_link=html_escape(sub_link),
        user_name=html_escape(user_name),
        user_expiry=jalali_expiry,
        user_remaining_days=remaining_days,
        user_volume_limit=usage_limit_gb,
        user_volume_used=current_usage_gb,
        user_volume_remaining=remaining_usage_gb
    )
    
    # Create URL buttons for forwardability
    builder = InlineKeyboardBuilder()
    
    # Panel access URL
    user_name_encoded = urllib.parse.quote(user_name)
    panel_link_url = f"https://{HIDDIFY_API_DOMAIN}/{HIDDIFY_API_USER_PROXY_PATH}/{user_uuid}/#{user_name_encoded}"
    
    # Get bot username for deep link
    try:
        bot_info = await bot.get_me()
        bot_username = bot_info.username
    except Exception as e:
        logger.error(f"Failed to get bot info: {e}")
        bot_username = "YourBotUsername"  # Fallback
    
    # Deep link for entering bot with UUID
    bot_deep_link = f"https://t.me/{bot_username}?start=uuid_{user_uuid}"
    
    # Deep link for showing apps guide
    apps_deep_link = f"https://t.me/{bot_username}?start=show_apps"
    
    builder.button(text="🔗 ورود به پنل کاربری", url=panel_link_url)
    builder.button(text="📱 نمایش برنامه‌ها", url=apps_deep_link)
    builder.button(text="🤖 ورود به ربات با این لینک", url=bot_deep_link)
    builder.adjust(1)
    
    try:
        logger.debug(f"Sending enhanced subscription QR photo to chat {chat_id}.")
        # Send to the original chat (admin/user)
        await bot.send_photo(
            chat_id, 
            types.BufferedInputFile(qr_bytes, filename="subscription_qr.png"), 
            caption=caption,
            reply_markup=builder.as_markup(),
            parse_mode="HTML"
        )
        logger.info(f"Enhanced subscription QR photo sent successfully to chat {chat_id} for UUID {user_uuid}.")
        
        # <<< START OF MODIFICATION >>>
        # Also send to TELEGRAM_INFO_CHAT if configured, NOT by forwarding
        if TELEGRAM_INFO_CHAT:
            try:
                # Send a new message with the same components
                await bot.send_photo(
                    TELEGRAM_INFO_CHAT, 
                    types.BufferedInputFile(qr_bytes, filename="subscription_qr.png"), 
                    caption=caption,
                    reply_markup=builder.as_markup(),
                    parse_mode="HTML"
                )
                logger.info(f"Enhanced subscription QR photo sent successfully to info channel {TELEGRAM_INFO_CHAT} for UUID {user_uuid}.")
            except Exception as e:
                logger.error(f"Failed to send subscription QR code to info channel {TELEGRAM_INFO_CHAT}: {e}")
        # <<< END OF MODIFICATION >>>
        
    except Exception as e:
        logger.error(f"Failed to send enhanced subscription QR photo for {user_uuid}: {e}", exc_info=True)
        await bot.send_message(chat_id, Messages.Admin.UserInfoEdit.SUB_QR_SEND_ERROR(user_uuid=user_uuid))


# --- File and Directory Management ---
async def save_receipt_photo(photo_bytes_io: BytesIO, identifier: str) -> Union[str, None]:
    """Saves the received photo bytes to a user-specific directory."""
    logger.info(f"Saving receipt photo for identifier: '{identifier}'.")
    user_bank_dir = os.path.join(PROJECT_DIR, "bank", str(identifier))
    try:
        logger.debug(f"Ensuring directory exists: {user_bank_dir}")
        os.makedirs(user_bank_dir, exist_ok=True)
        
        timestamp = jdatetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        receipt_filename = f"receipt_{timestamp}.jpg"
        receipt_filepath = os.path.join(user_bank_dir, receipt_filename)
        
        logger.debug(f"Writing photo data to file: {receipt_filepath}")
        with open(receipt_filepath, "wb") as f:
            f.write(photo_bytes_io.getvalue())
        logger.info(f"Receipt photo saved successfully as '{receipt_filename}'.")
        return receipt_filename
    except OSError as e:
        logger.error(f"OS error saving receipt photo to {user_bank_dir}: {e}", exc_info=True)
        return None


# --- Formatting and Data Conversion ---
def _bytes_to_gb(byte_value: Union[int, str, None]) -> Union[float, str]:
    """Safely converts a byte value to gigabytes (GB)."""
    try: 
        return round(int(byte_value) / (1024**3), 2) if byte_value is not None else 0.0
    except (ValueError, TypeError): 
        logger.warning(f"Could not convert byte value '{byte_value}' to GB.")
        return "N/A"

def format_ssh_server_info(server_stats: dict) -> str:
    """Formats the dictionary of enhanced SSH server stats into a readable HTML message."""
    logger.debug("Formatting SSH server stats into HTML message.")
    if not isinstance(server_stats, dict):
        logger.error(f"Invalid data type for server_stats: {type(server_stats)}")
        return Messages.Admin.ServerStats.SSH_ERROR(error="Invalid data received for formatting")

    def get_val(key, unit=""):
        val = server_stats.get(key, "N/A")
        return f"<code>{html_escape(str(val))}{unit}</code>"

    M = Messages.Utils
    message = M.SSH_STATS_TITLE
    message += M.SSH_STATS_OS.format(value=get_val('os_info'))
    message += M.SSH_STATS_KERNEL.format(value=get_val('kernel_version'))
    message += M.SSH_STATS_UPTIME.format(value=get_val('uptime'))
    message += M.SSH_STATS_CPU.format(value=get_val('cpu_usage'))
    message += M.SSH_STATS_LOAD.format(value=get_val('load_avg'))
    message += M.SSH_STATS_RAM.format(used=get_val('ram_used_gb'), total=get_val('ram_total_gb'))
    message += M.SSH_STATS_SWAP.format(used=get_val('swap_used_gb'), total=get_val('swap_total_gb'))
    message += M.SSH_STATS_DISK.format(used=get_val('disk_used'), total=get_val('disk_total'), usage=get_val('disk_usage'))
    message += M.SSH_STATS_PROCS.format(value=get_val('processes'))
    message += M.SSH_STATS_TCP.format(value=get_val('tcp_connections'))
    message += M.SSH_STATS_USERS.format(value=get_val('users_online'))
    
    return message

def format_server_status(status_data_str: str) -> str:
    """Formats the JSON string from the Hiddify /server_status API endpoint with full details."""
    logger.debug("Formatting Hiddify API server status from JSON string.")
    try:
        status = json.loads(status_data_str)
        stats = status.get("stats", {}).get("system", {})
        usage = status.get("usage_history", {})
        top5 = status.get("stats", {}).get("top5", {})

        def get_stat(key, default="N/A", precision=2):
            val = stats.get(key, default)
            return round(val, precision) if isinstance(val, (int, float)) else val

        def get_usage(period, key, is_bytes=False):
            val = usage.get(period, {}).get(key, "N/A")
            return _bytes_to_gb(val) if is_bytes else val

        M = Messages.Utils
        message = M.API_STATS_TITLE
        message += M.API_STATS_CPU.format(percent=get_stat('cpu_percent'), cores=get_stat('num_cpus'))
        message += M.API_STATS_LOAD.format(one_min=get_stat('load_avg_1min'), five_min=get_stat('load_avg_5min'), fifteen_min=get_stat('load_avg_15min'))
        message += M.API_STATS_RAM.format(used=get_stat('ram_used'), total=get_stat('ram_total'))
        message += M.API_STATS_DISK.format(used=get_stat('disk_used'), total=get_stat('disk_total'), hiddify_used=get_stat('hiddify_used'))
        
        message += M.API_STATS_CONNECTIONS.format(value=get_stat('total_connections'))
        message += M.API_STATS_UNIQUE_IPS.format(value=get_stat('total_unique_ips'))

        message += M.API_STATS_TRAFFIC_TITLE
        message += M.API_STATS_TRAFFIC_SENT.format(value=get_stat('net_sent_cumulative_GB'))
        message += M.API_STATS_TRAFFIC_RECV.format(value=_bytes_to_gb(stats.get('bytes_recv_cumulative')))
        message += M.API_STATS_TRAFFIC_TOTAL.format(value=get_stat('net_total_cumulative_GB'))

        message += M.API_STATS_USAGE_TITLE
        message += M.API_STATS_USAGE_USERS.format(value=get_usage('total', 'users'))
        message += M.API_STATS_USAGE_ONLINE_5M.format(value=get_usage('m5', 'online'))
        message += M.API_STATS_USAGE_TODAY.format(usage=get_usage('today', 'usage', is_bytes=True), online=get_usage('today', 'online'))
        message += M.API_STATS_USAGE_YESTERDAY.format(usage=get_usage('yesterday', 'usage', is_bytes=True), online=get_usage('yesterday', 'online'))
        message += M.API_STATS_USAGE_30_DAYS.format(usage=get_usage('last_30_days', 'usage', is_bytes=True), online=get_usage('last_30_days', 'online'))
        message += M.API_STATS_USAGE_TOTAL.format(usage=get_usage('total', 'usage', is_bytes=True))

        if top5:
            cpu_top = top5.get("cpu", [])
            if cpu_top:
                message += M.API_STATS_TOP5_CPU_TITLE
                for proc, use in cpu_top[:5]:
                    message += M.API_STATS_TOP5_ITEM.format(name=html_escape(proc[:20]), value=use, unit="%")
            
            ram_top = top5.get("ram", [])
            if ram_top:
                message += M.API_STATS_TOP5_RAM_TITLE
                for proc, use_gb in ram_top[:5]:
                    message += M.API_STATS_TOP5_ITEM.format(name=html_escape(proc[:20]), value=f"{use_gb:.3f}", unit=" GB")
        
        return message
        
    except json.JSONDecodeError as e:
        logger.error(f"Could not parse server status JSON. Error: {e}. Data: {status_data_str[:200]}...", exc_info=True)
        return Messages.Admin.ServerStats.API_ERROR(Messages.Utils.API_STATS_INVALID_JSON)
    except Exception as e:
        logger.error(f"An unexpected error occurred while formatting server status: {e}", exc_info=True)
        return Messages.Admin.ServerStats.API_ERROR(html_escape(str(e)))


async def send_critical_error_to_admin(bot: Bot, error: Exception, context: str, user_id: int = None):
    """Send critical error reports to admin"""
    if not ADMIN_USER_ID:
        return
        
    try:
        M = Messages.Utils
        error_type = type(error).__name__
        error_message = str(error)
        persian_time = jdatetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')
        
        # Create detailed report for admin
        admin_report = M.CRITICAL_ERROR_TITLE
        admin_report += M.CRITICAL_ERROR_TYPE.format(value=error_type)
        admin_report += M.CRITICAL_ERROR_TIME.format(value=persian_time)
        admin_report += M.CRITICAL_ERROR_USER.format(value=user_id or M.USER_ID_UNKNOWN)
        admin_report += M.CRITICAL_ERROR_CONTEXT.format(value=context)
        
        safe_error_message = html_escape(error_message[:500] + ('...' if len(error_message) > 500 else ''))
        admin_report += M.CRITICAL_ERROR_MESSAGE.format(value=safe_error_message)
        
        await bot.send_message(ADMIN_USER_ID, admin_report)
        logger.info(f"Critical error report sent to admin for error: {error_type}")
        
    except Exception as e:
        logger.error(f"Failed to send critical error to admin: {e}")