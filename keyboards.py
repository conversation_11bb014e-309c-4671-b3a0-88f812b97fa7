# hiddy_bot/keyboards.py

import logging  # type: ignore
from aiogram.types import InlineKeyboardButton  # type: ignore
from aiogram.utils.keyboard import Reply<PERSON>eyboardBuilder, InlineKeyboardBuilder  # type: ignore

from messages import Messages  # type: ignore
from config import ADMIN_USER_ID, SUPPORT_TELEGRAM_LINK  # type: ignore
from database import get_user_trial_status, get_bot_setting, get_all_active_plans  # type: ignore

logger = logging.getLogger(__name__)


# --- Main Menus (Reply Keyboards) ---

def create_main_menu(user_id: int):
    """Creates the main reply keyboard based on user role (admin/regular)."""
    logger.debug(f"Creating main menu for user_id: {user_id}.")
    builder = ReplyKeyboardBuilder()
    if user_id == ADMIN_USER_ID:
        logger.debug("User is an ADMIN. Creating admin-specific main menu.")
        builder.button(text=Messages.Admin.Menu.BUTTON_SERVER_STATS)
        builder.button(text=Messages.Admin.Menu.BUTTON_USER_MANAGEMENT)
        builder.button(text=Messages.Admin.Menu.BUTTON_GET_BACKUP)
        builder.button(text=Messages.Admin.Menu.BUTTON_PANEL_REPORT)
        builder.button(text=Messages.Admin.Menu.BUTTON_SEND_MESSAGE_USERS)
        builder.button(text=Messages.Admin.IncomeStatus.BUTTON_TEXT)
        builder.button(text=Messages.Admin.ButtonManagement.BUTTON_TEXT)
        builder.button(text=Messages.Admin.EmergencyConfig.BUTTON_TEXT)
        builder.button(text=Messages.Admin.PlanManagement.BUTTON_TEXT)
        builder.button(text="🗄️ مدیریت JSON Database")
        builder.adjust(2, 2, 1, 2, 1)
    else:
        logger.debug("User is a regular user. Creating generic base user menu.")
        builder.button(text=Messages.User.Menu.BUTTON_MY_ACCOUNT)
        builder.adjust(1)
    
    markup = builder.as_markup(resize_keyboard=True)
    logger.debug(f"Main menu markup created for user {user_id}.")
    return markup

def create_user_regular_menu(user_id: int):
    """Creates the standard reply keyboard for authenticated regular users."""
    logger.debug(f"Creating regular user menu for user_id: {user_id}.")
    builder = ReplyKeyboardBuilder()
    
    logger.debug(f"Checking trial status for user {user_id} to determine menu buttons.")
    is_trial = get_user_trial_status(user_id)
    
    buy_enabled = get_bot_setting('buy_subscription_enabled', '1') == '1'
    
    if is_trial and buy_enabled:
        action_button_text = Messages.User.Initial.BUTTON_BUY_SUB
        logger.debug(f"User {user_id} is a trial user. Menu will show 'Buy Subscription' button.")
    elif not is_trial:
        action_button_text = Messages.User.Menu.BUTTON_RENEW_SUB
        logger.debug(f"User {user_id} is a paid user. Menu will show 'Renew Subscription' button.")
    else:
        action_button_text = None
        logger.debug(f"User {user_id} is trial but buy is disabled.")

    builder.button(text=Messages.User.Menu.BUTTON_MY_ACCOUNT)
    if action_button_text:
        builder.button(text=action_button_text)
    builder.button(text=Messages.User.Menu.BUTTON_GET_APPS)
    builder.button(text=Messages.User.Menu.BUTTON_SERVER_STATUS)
    
    # Only show emergency config button for non-trial users (paid users)
    if not is_trial:
        builder.button(text=Messages.User.EmergencyConfig.USER_BUTTON_TEXT)
    
    # Add guide button
    builder.button(text=Messages.User.Guide.BUTTON_TEXT)
    builder.button(text=Messages.User.Initial.BUTTON_SUPPORT)
    
    if action_button_text:
        if not is_trial:
            builder.adjust(1, 2, 2, 1, 1, 1)  # account, renew, apps+server, emergency, guide, support
        else:
            builder.adjust(1, 2, 2, 1, 1)  # account, buy, apps+server, guide, support
    else:
        if not is_trial:
            builder.adjust(1, 2, 1, 1, 1)  # account, apps+server, emergency, guide, support
        else:
            builder.adjust(1, 2, 1, 1)  # account, apps+server, guide, support
    
    markup = builder.as_markup(resize_keyboard=True)
    logger.debug(f"Regular user menu markup created for user {user_id}.")
    return markup

def create_user_initial_menu():
    """Creates the reply keyboard for unauthenticated users."""
    logger.debug("Creating initial user menu for unauthenticated users.")
    builder = ReplyKeyboardBuilder()
    
    buy_enabled = get_bot_setting('buy_subscription_enabled', '1') == '1'
    trial_enabled = get_bot_setting('trial_subscription_enabled', '1') == '1'
    
    if buy_enabled:
        builder.button(text=Messages.User.Initial.BUTTON_BUY_SUB)
    if trial_enabled:
        builder.button(text=Messages.User.Initial.BUTTON_GET_TRIAL)
    
    # Emergency config button NOT shown for unauthenticated users
    
    # Add guide button for unauthenticated users
    builder.button(text=Messages.User.Guide.BUTTON_TEXT)
    builder.button(text=Messages.User.Initial.BUTTON_SUPPORT)
    builder.adjust(1)
    
    markup = builder.as_markup(resize_keyboard=True)
    logger.debug("Initial user menu markup created.")
    return markup


# --- Admin Inline Keyboards ---

def create_user_management_menu():
    """Creates the inline keyboard for the admin's user management section."""
    logger.debug("Creating admin user management inline menu.")
    builder = InlineKeyboardBuilder()
    builder.button(text=Messages.Admin.UserManagement.BUTTON_SEARCH_UUID, callback_data="admin:search_user")
    builder.button(text=Messages.Admin.UserManagement.BUTTON_ADD_USER, callback_data="admin:add_user")
    builder.button(text=Messages.Admin.UserManagement.BUTTON_MANAGE_REGULAR, callback_data="admin:list_users:regular:edit:1")
    builder.button(text=Messages.Admin.UserManagement.BUTTON_MANAGE_TRIAL, callback_data="admin:list_users:trial:edit:1")
    builder.button(text=Messages.Admin.UserManagement.BUTTON_UPDATE_USAGE, callback_data="admin:update_usage")
    builder.adjust(1)
    return builder.as_markup()


# NEW: Button Management Keyboards
def create_button_management_menu():
    """Creates the inline keyboard for button management."""
    logger.debug("Creating button management inline menu.")
    builder = InlineKeyboardBuilder()
    
    buy_enabled = get_bot_setting('buy_subscription_enabled', '1') == '1'
    trial_enabled = get_bot_setting('trial_subscription_enabled', '1') == '1'
    
    buy_status = Messages.Admin.ButtonManagement.BUTTON_STATUS(buy_enabled)
    trial_status = Messages.Admin.ButtonManagement.BUTTON_STATUS(trial_enabled)
    
    builder.button(
        text=f"{Messages.Admin.ButtonManagement.BUTTON_TOGGLE_BUY} {buy_status}",
        callback_data="admin:toggle_button:buy"
    )
    builder.button(
        text=f"{Messages.Admin.ButtonManagement.BUTTON_TOGGLE_TRIAL} {trial_status}",
        callback_data="admin:toggle_button:trial"
    )
    builder.button(
        text=Messages.Admin.ButtonManagement.BUTTON_BANK_SETTINGS,
        callback_data="admin:bank_settings"
    )
    builder.button(
        text="⚙️ مدیریت تمدید",
        callback_data="admin:renewal_management"
    )
    builder.adjust(1)
    return builder.as_markup()


# NEW: Renewal Management Keyboard
def create_renewal_management_keyboard(current_method: int):
    """Creates the inline keyboard for renewal method management."""
    logger.debug(f"Creating renewal management keyboard. Current method: {current_method}")
    builder = InlineKeyboardBuilder()

    method_titles = {
        1: "روش ۱: ریست روز و حجم",
        2: "روش ۲: تجمیع روز و حجم",
        3: "روش ۳: ریست روز، تجمیع حجم",
        4: "روش ۴: تجمیع روز، ریست حجم"
    }

    for method, title in method_titles.items():
        prefix = "✅ " if method == current_method else ""
        builder.button(
            text=f"{prefix}{title}",
            callback_data=f"admin:renewal:set:{method}"
        )

    builder.button(text="🔙 بازگشت", callback_data="admin:button_management")
    builder.adjust(1)
    return builder.as_markup()


# NEW: Emergency Config Keyboards
def create_emergency_config_menu():
    """Creates the inline keyboard for emergency config management."""
    logger.debug("Creating emergency config inline menu.")
    builder = InlineKeyboardBuilder()
    
    builder.button(text=Messages.Admin.EmergencyConfig.BUTTON_SET_NEW, callback_data="admin:emergency:set_new")
    builder.button(text=Messages.Admin.EmergencyConfig.BUTTON_VIEW_CURRENT, callback_data="admin:emergency:view")
    builder.button(text=Messages.Admin.EmergencyConfig.BUTTON_DELETE, callback_data="admin:emergency:delete")
    builder.button(text=Messages.Admin.EmergencyConfig.BUTTON_SEND_TEST, callback_data="admin:emergency:test")
    builder.adjust(2, 2)
    return builder.as_markup()


# NEW: Plan Management Keyboards
def create_plan_management_menu():
    """Creates the inline keyboard for plan management."""
    logger.debug("Creating plan management inline menu.")
    builder = InlineKeyboardBuilder()
    
    builder.button(text=Messages.Admin.PlanManagement.BUTTON_VIEW_ALL, callback_data="admin:plans:view_all")
    builder.button(text=Messages.Admin.PlanManagement.BUTTON_ADD_NEW, callback_data="admin:plans:add_new")
    builder.adjust(2)
    return builder.as_markup()


def create_plans_list_keyboard(plans):
    """Creates keyboard for listing plans with edit/delete options."""
    logger.debug(f"Creating plans list keyboard for {len(plans)} plans.")
    builder = InlineKeyboardBuilder()
    
    for plan in plans:
        status_icon = "✅" if plan['is_active'] else "❌"
        # Use plan_id for the button text, not the plan name
        button_text = f"{status_icon} {plan['plan_id']}"
        builder.button(
            text=button_text,
            callback_data=f"admin:plans:edit:{plan['plan_id']}"
        )
    
    builder.button(text="➕ اضافه کردن پلن جدید", callback_data="admin:plans:add_new")
    builder.button(text="🔙 بازگشت", callback_data="admin:plans:menu")
    builder.adjust(1)
    return builder.as_markup()


def create_plan_edit_keyboard(plan_id: str):
    """Creates keyboard for editing a specific plan."""
    logger.debug(f"Creating plan edit keyboard for plan {plan_id}.")
    builder = InlineKeyboardBuilder()
    
    builder.button(text="✏️ ویرایش", callback_data=f"admin:plans:modify:{plan_id}")
    builder.button(text="🔄 تغییر وضعیت", callback_data=f"admin:plans:toggle:{plan_id}")
    builder.button(text="🗑️ حذف", callback_data=f"admin:plans:delete:{plan_id}")
    builder.button(text="🔙 بازگشت", callback_data="admin:plans:view_all")
    builder.adjust(2, 1, 1)
    return builder.as_markup()


def create_plan_field_edit_keyboard(plan_id: str):
    """Creates keyboard for selecting which field of a plan to edit."""
    logger.debug(f"Creating plan field edit keyboard for plan {plan_id}.")
    builder = InlineKeyboardBuilder()
    
    builder.button(text="📝 ویرایش نام پلن", callback_data=f"admin:plans:edit_field:name:{plan_id}")
    builder.button(text="📄 ویرایش توضیحات", callback_data=f"admin:plans:edit_field:description:{plan_id}")
    builder.button(text="📅 ویرایش تعداد روزها", callback_data=f"admin:plans:edit_field:days:{plan_id}")
    builder.button(text="📊 ویرایش حجم (GB)", callback_data=f"admin:plans:edit_field:volume:{plan_id}")
    builder.button(text="💰 ویرایش قیمت", callback_data=f"admin:plans:edit_field:price:{plan_id}")
    builder.button(text="🔙 بازگشت", callback_data=f"admin:plans:edit:{plan_id}")
    builder.adjust(1)
    return builder.as_markup()


# --- User Inline Keyboards ---

def create_support_keyboard():
    """Creates an inline keyboard with a link to the support chat."""
    logger.debug("Creating support inline keyboard.")
    builder = InlineKeyboardBuilder()
    builder.button(text=Messages.Buttons.CONTACT_SUPPORT, url=SUPPORT_TELEGRAM_LINK)
    return builder.as_markup()

def create_plan_menu(action_prefix="renew_plan"):
    """Creates an inline keyboard for selecting subscription plans from database."""
    logger.debug(f"Creating plan selection menu with action prefix: '{action_prefix}'.")
    builder = InlineKeyboardBuilder()
    
    active_plans = get_all_active_plans()
    
    if not active_plans:
        logger.warning("No active plans found in database.")
        return builder.as_markup()
    
    for plan in active_plans:
        price_formatted = f"{plan['price_toman']:,} تومان"
        button_text = Messages.User.Purchase.PLAN_BUTTON_FORMAT(
            description=plan['plan_name'],
            price_formatted=price_formatted
        )
        
        builder.button(
            text=button_text,
            callback_data=f"{action_prefix}:{plan['plan_id']}",
        )
    
    # Only show cancel button for buy_plan, not for renew_plan
    if action_prefix == "buy_plan":
        cancel_callback = "user:cancel_buy"
        logger.debug(f"Cancel button callback for plan menu set to: '{cancel_callback}'.")
        builder.button(text=Messages.Buttons.CANCEL, callback_data=cancel_callback)
    
    builder.adjust(1)
    
    return builder.as_markup()


# NEW: Dynamic Send Message Keyboards
def create_inline_buttons_count_keyboard():
    """Creates keyboard for selecting number of inline buttons."""
    logger.debug("Creating inline buttons count keyboard.")
    builder = InlineKeyboardBuilder()
    
    builder.button(text=Messages.Admin.DynamicSendMessage.BUTTON_NO_BUTTONS, callback_data="admin:send_msg:buttons:0")
    builder.button(text=Messages.Admin.DynamicSendMessage.BUTTON_1_BUTTON, callback_data="admin:send_msg:buttons:1")
    builder.button(text=Messages.Admin.DynamicSendMessage.BUTTON_2_BUTTONS, callback_data="admin:send_msg:buttons:2")
    builder.button(text=Messages.Admin.DynamicSendMessage.BUTTON_3_BUTTONS, callback_data="admin:send_msg:buttons:3")
    builder.button(text=Messages.Admin.DynamicSendMessage.BUTTON_4_BUTTONS, callback_data="admin:send_msg:buttons:4")
    builder.button(text=Messages.Admin.DynamicSendMessage.BUTTON_5_BUTTONS, callback_data="admin:send_msg:buttons:5")
    builder.adjust(2)
    return builder.as_markup()


def create_final_confirmation_keyboard():
    """Creates keyboard for final message confirmation."""
    logger.debug("Creating final confirmation keyboard.")
    builder = InlineKeyboardBuilder()
    
    builder.button(text="✅ تایید و ارسال", callback_data="admin:send_msg:confirm")
    builder.button(text="❌ لغو", callback_data="admin:send_msg:cancel")
    builder.adjust(1)
    return builder.as_markup()


# --- User Guide Keyboards ---

def create_user_guide_keyboard(is_authenticated: bool = False):
    """Creates the guide sections keyboard based on user authentication status."""
    logger.debug(f"Creating user guide keyboard. Authenticated: {is_authenticated}")
    builder = InlineKeyboardBuilder()
    
    if is_authenticated:
        # Guide sections for authenticated users
        builder.button(text=Messages.User.Guide.SECTION_ACCOUNT_INFO, callback_data="guide:account_info")
        builder.button(text=Messages.User.Guide.SECTION_RENEWAL, callback_data="guide:renewal")
        builder.button(text=Messages.User.Guide.SECTION_APPS_DOWNLOAD, callback_data="guide:apps_download")
        builder.button(text=Messages.User.Guide.SECTION_SERVER_STATUS, callback_data="guide:server_status")
        builder.button(text=Messages.User.Guide.SECTION_EMERGENCY_CONFIG, callback_data="guide:emergency_config")
        builder.button(text=Messages.User.Guide.SECTION_TROUBLESHOOTING, callback_data="guide:troubleshooting")
        builder.button(text=Messages.User.Guide.SECTION_SUPPORT_CONTACT, callback_data="guide:support_contact")
        builder.adjust(2)
    else:
        # Guide sections for unauthenticated users  
        builder.button(text=Messages.User.Guide.SECTION_AUTHENTICATION, callback_data="guide:authentication")
        builder.button(text=Messages.User.Guide.SECTION_BUY_SUBSCRIPTION, callback_data="guide:buy_subscription")
        builder.button(text=Messages.User.Guide.SECTION_TRIAL_SUBSCRIPTION, callback_data="guide:trial_subscription")
        builder.button(text=Messages.User.Guide.SECTION_SUPPORT_CONTACT, callback_data="guide:support_contact")
        builder.adjust(2)
    
    # Back button
    builder.row(InlineKeyboardButton(text="🔙 بازگشت به منو", callback_data="guide:back_to_menu"))
    
    return builder.as_markup()


def create_guide_back_keyboard():
    """Creates a simple back keyboard for guide sections."""
    logger.debug("Creating guide back keyboard.")
    builder = InlineKeyboardBuilder()
    builder.button(text="🔙 بازگشت به راهنما", callback_data="guide:back_to_guide")
    builder.adjust(1)
    return builder.as_markup()


# --- General Purpose Keyboards ---

def create_cancel_button(callback_data="general:cancel"):
    """Creates a simple inline keyboard with a cancel button."""
    logger.debug(f"Creating a single cancel button with callback: '{callback_data}'.")
    builder = InlineKeyboardBuilder()
    builder.button(text=Messages.Buttons.CANCEL, callback_data=callback_data)
    return builder.as_markup()

def create_membership_keyboard(channel_username: str):
    """Creates the keyboard for the channel membership requirement message."""
    logger.debug(f"Creating membership join keyboard for channel: {channel_username}.")
    builder = InlineKeyboardBuilder()
    join_url = f"https://t.me/{channel_username.replace('@', '')}"
    builder.button(text=Messages.User.Membership.BUTTON_JOIN_CHANNEL, url=join_url)
    return builder.as_markup()

def create_confirmation_keyboard(confirm_callback: str, cancel_callback: str):
    """Creates a confirmation keyboard with yes/no buttons."""
    logger.debug(f"Creating confirmation keyboard: confirm={confirm_callback}, cancel={cancel_callback}")
    builder = InlineKeyboardBuilder()
    builder.button(text="✅ بله", callback_data=confirm_callback)
    builder.button(text="❌ خیر", callback_data=cancel_callback)
    builder.adjust(2)
    return builder.as_markup()