# hiddy_bot/logging_config.py

import logging  # type: ignore
import sys  # type: ignore

def setup_logging():
    """
    Configures logging to output to both console and a file at DEBUG level.
    The file handler appends to the log file instead of overwriting it.
    """
    log_formatter = logging.Formatter(
        "%(asctime)s - %(name)s:%(lineno)d - %(funcName)s() - %(levelname)s - %(message)s"
    )
    
    # Get the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # --- File Handler ---
    # Logs to outputbot.txt, appends to the file on each run
    file_handler = logging.FileHandler("outputbot.txt", mode='a', encoding='utf-8')
    file_handler.setFormatter(log_formatter)
    file_handler.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)

    # --- Console (Stream) Handler ---
    # Logs to the console
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(log_formatter)
    console_handler.setLevel(logging.INFO) # Show INFO and higher in console to avoid clutter
    root_logger.addHandler(console_handler)

    logging.getLogger("apscheduler").setLevel(logging.WARNING)
    logging.getLogger("paramiko").setLevel(logging.WARNING)
    
    # A log to confirm that this function ran
    root_logger.info("="*50)
    root_logger.info("Logging configured successfully. Level: DEBUG. Output: Console (INFO), outputbot.txt (DEBUG)")
    root_logger.info("="*50)