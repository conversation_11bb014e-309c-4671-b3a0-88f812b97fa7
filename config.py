# hiddy_bot/config.py

import os  # type: ignore
import logging  # type: ignore
from dotenv import load_dotenv  # type: ignore

# [FIX] Get a logger instance for this specific file
logger = logging.getLogger(__name__)

# --- Environment Variable Loading ---
logger.debug("Attempting to load environment variables from .env file.")
load_dotenv()
logger.info("Finished loading from .env file (if it exists).")

# --- Telegram Bot Configuration ---
API_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
logger.debug(f"Loaded API_TOKEN: {'SET' if API_TOKEN else 'NOT SET'}")

ADMIN_USER_ID_STR = os.getenv("TELEGRAM_BOT_SERVER_USER")
ADMIN_USER_ID = int(ADMIN_USER_ID_STR) if ADMIN_USER_ID_STR else None
logger.debug(f"Loaded ADMIN_USER_ID: {ADMIN_USER_ID}")

TELEGRAM_REPORT_CHAT = os.getenv("TELEGRAM_REPORT_CHAT")
logger.debug(f"Loaded TELEGRAM_REPORT_CHAT: {TELEGRAM_REPORT_CHAT}")

TELEGRAM_INFO_CHAT = os.getenv("TELEGRAM_INFO_CHAT")
logger.debug(f"Loaded TELEGRAM_INFO_CHAT: {TELEGRAM_INFO_CHAT}")

MEMBERSHIP_CHANNEL_USERNAME = os.getenv("MEMBERSHIP_CHANNEL_USERNAME", "@mbnvpnpro")
logger.debug(f"Loaded MEMBERSHIP_CHANNEL_USERNAME: {MEMBERSHIP_CHANNEL_USERNAME}")

SUPPORT_TELEGRAM_LINK = os.getenv("SUPPORT_TELEGRAM_LINK", "https://t.me/mbnproo")
logger.debug(f"Loaded SUPPORT_TELEGRAM_LINK: {SUPPORT_TELEGRAM_LINK}")


# --- Hiddify API Configuration ---
HIDDIFY_API_DOMAIN = os.getenv("HIDDIFY_API_DOMAIN")
logger.debug(f"Loaded HIDDIFY_API_DOMAIN: {HIDDIFY_API_DOMAIN}")

HIDDIFY_API_TOKEN = os.getenv("HIDDIFY_API_TOKEN")
logger.debug(f"Loaded HIDDIFY_API_TOKEN: {'SET' if HIDDIFY_API_TOKEN else 'NOT SET'}")

HIDDIFY_API_PROXY_PATH = os.getenv("HIDDIFY_API_PROXY_PATH")
logger.debug(f"Loaded HIDDIFY_API_PROXY_PATH: {HIDDIFY_API_PROXY_PATH}")

HIDDIFY_API_USER_PROXY_PATH = os.getenv("HIDDIFY_API_USER_PROXY_PATH")
logger.debug(f"Loaded HIDDIFY_API_USER_PROXY_PATH: {HIDDIFY_API_USER_PROXY_PATH}")

PANEL_LINK_PREFIX = os.getenv("PANEL_LINK_PREFIX")
logger.debug(f"Loaded PANEL_LINK_PREFIX: {PANEL_LINK_PREFIX}")


# --- Server SSH Configuration ---
IP_SERVER = os.getenv("IP_SERVER")
logger.debug(f"Loaded IP_SERVER: {IP_SERVER}")

PASS_SERVER = os.getenv("PASS_SERVER")
logger.debug(f"Loaded PASS_SERVER: {'SET' if PASS_SERVER else 'NOT SET'}")

USER_SERVER = os.getenv("USER_SERVER", "root")
logger.debug(f"Loaded USER_SERVER: {USER_SERVER}")

SERVER_PORT = os.getenv("SERVER_PORT")
logger.debug(f"Loaded SERVER_PORT: {SERVER_PORT}")

BACKUP_DIR_PATH = os.getenv("BACKUP_DIR_PATH", "/opt/hiddify-manager/hiddify-panel/backup/")
logger.debug(f"Loaded BACKUP_DIR_PATH: {BACKUP_DIR_PATH}")


# --- Bot Internal Configuration ---
PROJECT_DIR = os.path.dirname(os.path.abspath(__file__))
logger.debug(f"PROJECT_DIR determined as: {PROJECT_DIR}")

DATABASE_FILE = os.path.join(PROJECT_DIR, os.getenv("DATABASE_FILE", "users.db"))
logger.debug(f"DATABASE_FILE path set to: {DATABASE_FILE}")

# Function to get bank account info from database
def get_bank_account_info():
    """Get bank account info from database settings with proper fallback"""
    try:
        import sqlite3
        from database import get_bot_setting  # type: ignore
        name = get_bot_setting('bank_account_name', os.getenv("BANK_ACCOUNT_NAME", "مهدی بزرگ نیا"))
        card_number = get_bot_setting('bank_card_number', os.getenv("BANK_ACCOUNT_CARD_NUMBER", "****************"))
        return {"name": name, "card_number": card_number}
    except (ImportError, sqlite3.OperationalError, sqlite3.DatabaseError) as e:
        # Fallback to environment variables if database is not available or tables don't exist yet
        logger.debug(f"Using fallback bank info due to: {e}")
        return {
            "name": os.getenv("BANK_ACCOUNT_NAME", "مهدی بزرگ نیا"),
            "card_number": os.getenv("BANK_ACCOUNT_CARD_NUMBER", "****************")
        }

# Static fallback bank account info (will be overridden by dynamic function calls)
BANK_ACCOUNT_INFO = {
    "name": os.getenv("BANK_ACCOUNT_NAME", "مهدی بزرگ نیا"),
    "card_number": os.getenv("BANK_ACCOUNT_CARD_NUMBER", "****************")
}
logger.debug(f"Static BANK_ACCOUNT_INFO: Name='{BANK_ACCOUNT_INFO['name']}'")

THROTTLE_RATE_LIMIT = 0.5
logger.debug(f"THROTTLE_RATE_LIMIT set to: {THROTTLE_RATE_LIMIT}")


# --- Critical Environment Variable Check ---
logger.debug("Checking for presence of critical environment variables.")
required_vars = [
    API_TOKEN,
    ADMIN_USER_ID,
    HIDDIFY_API_TOKEN,
    HIDDIFY_API_DOMAIN,
    HIDDIFY_API_PROXY_PATH,
    IP_SERVER,
    PASS_SERVER,
    HIDDIFY_API_USER_PROXY_PATH,
    TELEGRAM_REPORT_CHAT,
    PANEL_LINK_PREFIX,
]

# Note: TELEGRAM_INFO_CHAT is optional, so we don't include it in required_vars
optional_vars = {
    'TELEGRAM_INFO_CHAT': TELEGRAM_INFO_CHAT
}

if not all(required_vars):
    logger.critical("Missing one or more critical environment variables. The bot will exit.")
    exit("Error: Missing one or more critical environment variables.")
else:
    logger.info("All critical environment variables are loaded successfully.")
    
# Log optional variables
for var_name, var_value in optional_vars.items():
    if var_value:
        logger.info(f"Optional variable {var_name} is configured.")
    else:
        logger.info(f"Optional variable {var_name} is not configured.")