import os
import json
import logging
import datetime
import jdatetime # type: ignore
import re
from html import escape as html_escape
from typing import Union

from aiogram import Router, F # type: ignore
from aiogram.types import CallbackQuery, Message # type: ignore
from aiogram.fsm.context import FSMContext # type: ignore
from aiogram.fsm.state import State, StatesGroup # type: ignore
from aiogram.filters import StateFilter # type: ignore

from config import (
    ADMIN_USER_ID,
    PROJECT_DIR,
    HIDDIFY_API_DOMAIN,
    HIDDIFY_API_USER_PROXY_PATH,
    TELEGRAM_REPORT_CHAT
)
from database import (
    get_user_from_database,
    update_or_add_user,
    log_income_transaction,
    find_latest_receipt,
    get_renewal_method
)
from api_hiddify import add_user_api, update_user_api, delete_user_api, fetch_user_details_api
from messages import Messages, get_plan_details
from keyboards import create_user_initial_menu, create_user_regular_menu

logger = logging.getLogger(__name__)

router = Router()

# --- Admin Approval Handlers ---
@router.callback_query(F.data.startswith("admin_confirm_buy:"))
async def handle_admin_confirm_buy(callback_query: CallbackQuery, bot):
    admin_id = callback_query.from_user.id
    try:
        _, user_id_str, plan_id = callback_query.data.split(":")
        user_id = int(user_id_str)
        logger.info(
            f"Admin {admin_id} CONFIRMED BUY for user {user_id}, plan: {plan_id}"
        )
        await callback_query.answer("در حال پردازش تایید خرید...")
        original_caption = callback_query.message.caption
        original_photo_file_id = callback_query.message.photo[-1].file_id if callback_query.message.photo else None
        try:
            safe_caption_edit = html_escape(original_caption or "")
            await callback_query.message.edit_caption(
                caption=safe_caption_edit + "\n\n✅ تایید شده توسط ادمین",
                reply_markup=None,
                parse_mode="HTML"
            )
        except Exception as edit_err:
            logger.warning(
                f"Could not edit caption for buy confirmation (User: {user_id}): {edit_err}"
            )

        old_trial_uuid = None
        user_db_check = get_user_from_database(user_id)
        if user_db_check and user_db_check[5] == 1 and user_db_check[4]: # Index 5 is is_trial
            old_trial_uuid = user_db_check[4]
            logger.warning(
                f"User {user_id} has an existing trial subscription (UUID: {old_trial_uuid}). It will be deleted after successful purchase."
            )

        # Fetch plan details using the helper from messages.py or your config
        plan_full_details = get_plan_details(plan_id)
        package_days = plan_full_details.get("days", 30) # Default if not found
        usage_limit_gb = plan_full_details.get("limit_gb", 0)
        mode = plan_full_details.get("mode", "no_reset")
        plan_description = plan_full_details.get("desc", "پلن خریداری شده") # Fallback description
        plan_price_for_log = plan_full_details.get("price", 0)

        telegram_name = user_db_check[0] if user_db_check and user_db_check[0] else f"User_{user_id}"
        telegram_username = user_db_check[1] if user_db_check and user_db_check[1] else "NO_ID"
        if telegram_name == f"User_{user_id}": # Attempt to get a better name
            try:
                user_chat = await bot.get_chat(user_id)
                if user_chat.full_name: telegram_name = user_chat.full_name
                if user_chat.username: telegram_username = f"@{user_chat.username}" # Ensure @ symbol
            except Exception as e:
                logger.error(f"Could not get chat info for user {user_id}: {e}. Using default name.")

        paid_user_name = f"{telegram_name}"
        payload = {
            "comment": f"Purchased {plan_description} via bot by admin {admin_id}",
            "current_usage_GB": 0,
            "enable": True,
            "lang": "fa",
            "mode": mode,
            "name": paid_user_name,
            "package_days": package_days,
            "start_date": datetime.date.today().strftime("%Y-%m-%d"),
            "telegram_id": user_id,
            "usage_limit_GB": usage_limit_gb,
        }
        payload_cleaned = {k: v for k, v in payload.items() if v is not None}
        json_payload = json.dumps(payload_cleaned)
        logger.info(
            f"Creating PAID user via API. User ID: {user_id}, Payload: {json_payload}"
        )
        is_success, response, status, data = add_user_api(json_payload)

        if is_success:
            try:
                response_json = json.loads(response)
            except json.JSONDecodeError:
                logger.error(
                    f"Could not parse successful add paid user response (buy): {response}"
                )
                await callback_query.message.answer(
                    f"⚠️ خطا در پردازش پاسخ API برای کاربر {user_id}"
                )
                return

            new_paid_uuid = response_json.get("uuid")
            logger.info(
                f"Successfully created PAID user. User ID: {user_id}, New UUID: {new_paid_uuid}"
            )

            if new_paid_uuid:
                if old_trial_uuid:
                    logger.warning(
                        f"Attempting to delete old trial user {old_trial_uuid} from panel..."
                    )
                    del_success, del_resp, del_status = delete_user_api(
                        old_trial_uuid
                    )
                    if del_success:
                        logger.info(
                            f"Successfully deleted old trial user {old_trial_uuid} from panel."
                        )
                    else:
                        logger.error(
                            f"Failed to delete old trial user {old_trial_uuid} from panel. Status: {del_status}, Resp: {del_resp}"
                        )
                        await callback_query.message.answer(
                            f"⚠️ خطا در حذف کاربر تست قدیمی {html_escape(old_trial_uuid)}",
                            parse_mode="HTML",
                        )

                join_time_shamsi = jdatetime.datetime.now().strftime(
                    "%Y/%m/%d %H:%M:%S"
                )
                update_or_add_user(
                    paid_user_name,
                    telegram_username, # Use the potentially updated username
                    user_id,
                    join_time_shamsi,
                    new_paid_uuid,
                    is_trial=0,
                )
                logger.info(f"Updated DB for user {user_id} with new UUID {new_paid_uuid}, is_trial=0.")

                # Log income transaction
                receipt_filename_for_log = None
                bank_dir_path = os.path.join(PROJECT_DIR, "bank", f"new_user_{user_id}")
                if old_trial_uuid: # If it was a trial upgrade, receipt might be under old UUID
                    bank_dir_path_trial = os.path.join(PROJECT_DIR, "bank", old_trial_uuid)
                    latest_receipt_trial = find_latest_receipt(bank_dir_path_trial)
                    if latest_receipt_trial:
                        receipt_filename_for_log = os.path.basename(latest_receipt_trial)

                if not receipt_filename_for_log: # Try new_user folder if not found or not trial upgrade
                    latest_receipt_new = find_latest_receipt(bank_dir_path)
                    if latest_receipt_new:
                        receipt_filename_for_log = os.path.basename(latest_receipt_new)

                log_income_transaction(
                    user_tg_id=user_id,
                    user_uuid=new_paid_uuid,
                    transaction_type='buy' if not old_trial_uuid else 'upgrade_from_trial', # Differentiate
                    plan_id=plan_id,
                    plan_description=plan_description,
                    amount_paid=plan_price_for_log,
                    admin_approver_id=admin_id,
                    related_receipt_filename=receipt_filename_for_log
                )

                # Send success message to user
                try:
                    await bot.send_message(user_id, "✅ خرید شما تایید شد! اطلاعات پنل شما ارسال خواهد شد.", reply_markup=create_user_regular_menu(user_id))
                except Exception as notify_err:
                    logger.error(f"Failed to notify user {user_id} about successful purchase: {notify_err}")

                safe_paid_user_name_admin = html_escape(paid_user_name)
                await callback_query.message.answer(
                    f"✅ خرید کاربر {safe_paid_user_name_admin} (ID: {user_id}) تایید شد.\n\nUUID جدید: <code>{html_escape(new_paid_uuid)}</code>",
                    parse_mode="HTML",
                )

            else:
                logger.error(
                    f"API indicated success on paid user creation (buy), but no UUID returned. Response: {response}"
                )
                await callback_query.message.answer(
                    f"⚠️ خطا: UUID در پاسخ API یافت نشد برای کاربر {user_id}"
                )
                try:
                    await bot.send_message(
                        user_id,
                        "خطا در ایجاد حساب کاربری. لطفاً با پشتیبانی تماس بگیرید.",
                    )
                except Exception as e_notify:
                    logger.error(
                        f"Failed to send failure message to user {user_id} (buy, no uuid): {e_notify}"
                    )
        else:
            logger.error(
                f"API Error creating PAID user for purchase. User ID: {user_id}, Status: {status}, Response: {data}"
            )
            await callback_query.message.answer(
                f"⚠️ خطا در API: وضعیت {status} برای کاربر {user_id}\nخطا: {html_escape(data)}",
                parse_mode="HTML",
            )
            try:
                await bot.send_message(
                    user_id,
                    "خطا در ایجاد حساب کاربری. لطفاً با پشتیبانی تماس بگیرید.",
                    reply_markup=create_user_initial_menu(),
                )
            except Exception as e:
                logger.error(
                    f"Failed to send error message to user {user_id} (buy): {e}",
                    exc_info=True,
                )

    except ValueError as ve:
        logger.error(
            f"Invalid data in confirm_buy callback: {callback_query.data}. Error: {ve}"
        )
        await callback_query.answer("خطا در فرمت داده", show_alert=True)
    except Exception as e:
        logger.error(f"Error in handle_admin_confirm_buy: {e}", exc_info=True)
        await callback_query.answer("خطای داخلی رخ داد", show_alert=True)

@router.callback_query(F.data.startswith("admin_confirm_renew:"))
async def handle_admin_confirm_renew(callback_query: CallbackQuery, bot):
    admin_id = callback_query.from_user.id
    try:
        parts = callback_query.data.split(":")
        if len(parts) != 3:
             logger.error(f"Invalid confirm_renew callback data format (expected user_id): {callback_query.data}")
             await callback_query.answer("خطا در فرمت داده", show_alert=True)
             return
        _, user_id_str, plan_id = parts
        user_tg_id = int(user_id_str)

        logger.info(
            f"Admin {admin_id} received RENEW confirmation. User ID: {user_tg_id}, Plan: {plan_id}"
        )

        user_db_data = get_user_from_database(user_tg_id)
        if not user_db_data or not user_db_data[4]:
            logger.error(
                f"Could not find user or UUID in local 'newusers' DB for user_tg_id {user_tg_id} during renewal confirmation."
            )
            await callback_query.answer(
                "خطا: کاربر یا UUID او در دیتابیس محلی یافت نشد!", show_alert=True
            )
            return

        user_uuid = user_db_data[4]
        is_trial_user = bool(user_db_data[5])
        user_name_from_db = user_db_data[0] or f"User_{user_uuid[:6]}"
        user_tid_from_db = user_db_data[1] or "NO_ID"

        logger.info(
            f"User Check: UUID={user_uuid}, TG ID={user_tg_id}, Is Trial={is_trial_user}, Name={user_name_from_db}, TID={user_tid_from_db}"
        )

        plan_full_details = get_plan_details(plan_id)
        plan_description_for_log = plan_full_details.get("desc", "پلن تمدید نامشخص")
        add_days = plan_full_details.get("days", 30)
        new_limit_gb = plan_full_details.get("limit_gb", 0)
        new_mode = plan_full_details.get("mode", "no_reset")
        plan_price_for_log = plan_full_details.get("price", 0)

        original_caption = callback_query.message.caption
        original_photo_file_id = callback_query.message.photo[-1].file_id if callback_query.message.photo else None

        if is_trial_user:
            logger.warning(
                f"Trial user {user_tg_id} (Old UUID: {user_uuid}) is upgrading via 'Renew' button with plan {plan_id}. Creating new paid account."
            )
            await callback_query.answer("در حال ارتقاء کاربر تست به کاربر پرداختی...")
            try:
                safe_caption_edit = html_escape(original_caption or "")
                await callback_query.message.edit_caption(
                    caption=safe_caption_edit + "\n\n✅ ارتقاء از تست تایید شده",
                    reply_markup=None,
                    parse_mode="HTML",
                )
            except Exception as edit_err:
                logger.warning(
                    f"Could not edit caption for trial upgrade (User: {user_uuid}): {edit_err}"
                )

            paid_user_name = user_name_from_db
            if paid_user_name.startswith("Trial_") or paid_user_name == f"User_{user_uuid[:6]}":
                try:
                    user_chat = await bot.get_chat(user_tg_id)
                    if user_chat.full_name: paid_user_name = user_chat.full_name
                    logger.info(
                        f"Fetched potentially better name '{paid_user_name}' for trial upgrade user {user_tg_id}"
                    )
                except Exception as e:
                    logger.warning(f"Could not fetch chat info to improve name for {user_tg_id}: {e}")

            payload_add = {
                "comment": f"Upgraded from trial. Plan: {plan_description_for_log}. Approved by admin {admin_id}",
                "current_usage_GB": 0,
                "enable": True,
                "lang": "fa",
                "mode": new_mode,
                "name": paid_user_name,
                "package_days": add_days,
                "start_date": datetime.date.today().strftime("%Y-%m-%d"),
                "telegram_id": user_tg_id,
                "usage_limit_GB": new_limit_gb,
            }
            payload_cleaned_add = {k: v for k, v in payload_add.items() if v is not None}
            json_payload_add = json.dumps(payload_cleaned_add)
            logger.info(
                f"Creating NEW PAID user via API for trial upgrade. TG ID: {user_tg_id}, Payload: {json_payload_add}"
            )

            is_add_success, add_response, add_status, add_data = add_user_api(json_payload_add)

            if is_add_success:
                try:
                    response_json_add = json.loads(add_response)
                    new_paid_uuid = response_json_add.get("uuid")
                    logger.info(
                        f"Successfully created NEW PAID user for trial upgrade. TG ID: {user_tg_id}, New UUID: {new_paid_uuid}"
                    )

                    if new_paid_uuid:
                        join_time_shamsi = jdatetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
                        update_or_add_user(
                            paid_user_name,
                            user_tid_from_db,
                            user_tg_id,
                            join_time_shamsi,
                            new_paid_uuid,
                            is_trial=0,
                        )
                        logger.info(
                            f"Updated local DB for user {user_tg_id} with new paid UUID {new_paid_uuid} and is_trial=0."
                        )

                        old_trial_uuid = user_uuid
                        logger.warning(f"Attempting to delete old trial user {old_trial_uuid} from panel...")
                        del_success, del_resp, del_status = delete_user_api(old_trial_uuid)
                        if del_success:
                            logger.info(f"Successfully deleted old trial user {old_trial_uuid} from panel.")
                        else:
                            logger.error(f"Failed to delete old trial user {old_trial_uuid} from panel. Status: {del_status}, Resp: {del_resp}")
                            await callback_query.message.answer(
                                f"⚠️ خطا در حذف کاربر تست قدیمی {html_escape(old_trial_uuid)}",
                                parse_mode="HTML",
                            )

                        # Log income transaction for trial upgrade
                        receipt_filename_for_log_trial = None
                        bank_dir_path_trial = os.path.join(PROJECT_DIR, "bank", old_trial_uuid)
                        latest_receipt_trial = find_latest_receipt(bank_dir_path_trial)
                        if not latest_receipt_trial:
                            bank_dir_path_new = os.path.join(PROJECT_DIR, "bank", f"new_user_{user_tg_id}")
                            latest_receipt_trial = find_latest_receipt(bank_dir_path_new)
                        if latest_receipt_trial:
                            receipt_filename_for_log_trial = os.path.basename(latest_receipt_trial)

                        log_income_transaction(
                            user_tg_id=user_tg_id,
                            user_uuid=new_paid_uuid,
                            transaction_type='upgrade_from_trial',
                            plan_id=plan_id,
                            plan_description=plan_description_for_log,
                            amount_paid=plan_price_for_log,
                            admin_approver_id=admin_id,
                            related_receipt_filename=receipt_filename_for_log_trial
                        )

                        # Send success message to user
                        try:
                            await bot.send_message(user_tg_id, "✅ ارتقاء شما با موفقیت انجام شد!", reply_markup=create_user_regular_menu(user_tg_id))
                        except Exception as menu_err:
                            logger.error(f"Failed to send menu after trial upgrade for user {user_tg_id}: {menu_err}")

                        safe_paid_user_name_admin = html_escape(paid_user_name)
                        await callback_query.message.answer(
                            f"✅ ارتقاء کاربر {safe_paid_user_name_admin} (ID: {user_tg_id}) از تست تایید شد.\n\nUUID جدید: <code>{html_escape(new_paid_uuid)}</code>",
                            parse_mode="HTML",
                        )

                    else:
                        logger.error(f"API indicated success on trial upgrade, but no UUID returned. Response: {add_response}")
                        await callback_query.message.answer(f"⚠️ خطا: UUID در پاسخ API یافت نشد برای کاربر {user_tg_id}")
                        try:
                            await bot.send_message(user_tg_id, "خطا در ارتقاء حساب کاربری. لطفاً با پشتیبانی تماس بگیرید.")
                        except Exception as e_notify:
                            logger.error(f"Failed to send failure message to user {user_tg_id} (trial upgrade, no uuid): {e_notify}")

                except json.JSONDecodeError:
                    logger.error(f"Could not parse successful trial upgrade response: {add_response}")
                    await callback_query.message.answer(f"⚠️ خطا در پردازش پاسخ API برای کاربر {user_tg_id}")
                    return

            else:
                logger.error(f"API Error creating PAID user for trial upgrade. TG ID: {user_tg_id}, Status: {add_status}, Response: {add_data}")
                await callback_query.message.answer(f"⚠️ خطا در API: وضعیت {add_status} برای کاربر {user_tg_id}\nخطا: {html_escape(add_data)}", parse_mode="HTML")
                try:
                    await bot.send_message(user_tg_id, "خطا در ارتقاء حساب کاربری. لطفاً با پشتیبانی تماس بگیرید.", reply_markup=create_user_regular_menu(user_tg_id))
                except Exception as e:
                    logger.error(f"Failed to send error message to user {user_tg_id} (trial upgrade): {e}", exc_info=True)

        else:
            # Regular renewal for paid user
            await callback_query.answer("در حال تمدید اشتراک...")
            try:
                safe_caption_edit = html_escape(original_caption or "")
                await callback_query.message.edit_caption(
                    caption=safe_caption_edit + "\n\n✅ تمدید تایید شده",
                    reply_markup=None,
                    parse_mode="HTML",
                )
            except Exception as edit_err:
                logger.warning(f"Could not edit caption for renewal (User: {user_uuid}): {edit_err}")

            # --- NEW RENEWAL LOGIC ---
            renewal_method = get_renewal_method()
            logger.info(f"Applying renewal method #{renewal_method} for user {user_uuid}")

            # Fetch current user details to calculate remaining values
            fetch_success, details_str, _ = await fetch_user_details_api(user_uuid)
            if not fetch_success:
                logger.error(f"Failed to fetch user details for {user_uuid} during renewal.")
                await callback_query.message.answer(f"❌ خطا در دریافت اطلاعات فعلی کاربر {user_uuid} برای تمدید.")
                return
            
            try:
                user_details = json.loads(details_str)
                current_start_date_str = user_details.get('start_date')
                current_start_date = datetime.datetime.strptime(current_start_date_str, '%Y-%m-%d').date() if current_start_date_str else datetime.date.today()
                current_package_days = user_details.get('package_days', 0)
                current_expiry_date = current_start_date + datetime.timedelta(days=current_package_days)
                
                remaining_days = max(0, (current_expiry_date - datetime.date.today()).days)
                
                current_limit_gb = user_details.get('usage_limit_GB', 0.0)
                current_usage_gb = user_details.get('current_usage_GB', 0.0)
                remaining_volume_gb = max(0.0, current_limit_gb - current_usage_gb)

            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"Error parsing user details for renewal calculation: {e}")
                await callback_query.message.answer(f"❌ خطا در پردازش اطلاعات کاربر {user_uuid} برای تمدید.")
                return

            # Purchased plan details are already fetched
            # add_days = plan_full_details.get("days", 30)
            # new_limit_gb = plan_full_details.get("limit_gb", 0)

            final_package_days = add_days
            final_usage_limit_gb = new_limit_gb

            # Method 1: Reset days, Reset volume (Default behavior, days are reset)
            # final_package_days = add_days
            # final_usage_limit_gb = new_limit_gb
            
            if renewal_method == 2: # Accumulate days, Accumulate volume
                final_package_days = remaining_days + add_days
                final_usage_limit_gb = remaining_volume_gb + new_limit_gb
            
            elif renewal_method == 3: # Reset days, Accumulate volume
                final_package_days = add_days
                final_usage_limit_gb = remaining_volume_gb + new_limit_gb
            
            elif renewal_method == 4: # Accumulate days, Reset volume
                final_package_days = remaining_days + add_days
                final_usage_limit_gb = new_limit_gb

            logger.info(f"Renewal Calculation for {user_uuid} (Method {renewal_method}): "
                        f"Final Days={final_package_days}, Final Volume={final_usage_limit_gb} GB")

            # Update user on panel
            update_payload = {
                "package_days": final_package_days,
                "usage_limit_GB": final_usage_limit_gb,
                "start_date": datetime.date.today().strftime("%Y-%m-%d"),
                "current_usage_GB": 0, # Reset usage on every renewal
                "comment": f"Renewed (M{renewal_method}) via bot. Plan: {plan_description_for_log}. Approved by admin {admin_id}"
            }
            
            update_success, update_response, update_status = await update_user_api(user_uuid, update_payload)
            # --- END OF NEW RENEWAL LOGIC ---
            
            if update_success:
                logger.info(f"Successfully renewed user {user_tg_id} (UUID: {user_uuid})")
                
                # Log income transaction for renewal
                receipt_filename_for_log_renew = None
                bank_dir_path_renew = os.path.join(PROJECT_DIR, "bank", user_uuid)
                latest_receipt_renew = find_latest_receipt(bank_dir_path_renew)
                if latest_receipt_renew:
                    receipt_filename_for_log_renew = os.path.basename(latest_receipt_renew)

                log_income_transaction(
                    user_tg_id=user_tg_id,
                    user_uuid=user_uuid,
                    transaction_type='renew',
                    plan_id=plan_id,
                    plan_description=plan_description_for_log,
                    amount_paid=plan_price_for_log,
                    admin_approver_id=admin_id,
                    related_receipt_filename=receipt_filename_for_log_renew
                )

                # Send success message to user
                try:
                    await bot.send_message(user_tg_id, "✅ تمدید شما با موفقیت انجام شد!", reply_markup=create_user_regular_menu(user_tg_id))
                except Exception as menu_err:
                    logger.error(f"Failed to send menu after renewal for user {user_tg_id}: {menu_err}")

                safe_user_name_admin = html_escape(user_name_from_db)
                await callback_query.message.answer(
                    f"✅ تمدید کاربر {safe_user_name_admin} (ID: {user_tg_id}) تایید شد.\n\nUUID: <code>{html_escape(user_uuid)}</code>",
                    parse_mode="HTML",
                )

            else:
                logger.error(f"API Error renewing user. TG ID: {user_tg_id}, UUID: {user_uuid}, Status: {update_status}, Response: {update_response}")
                await callback_query.message.answer(f"⚠️ خطا در API: وضعیت {update_status} برای کاربر {user_tg_id}\nخطا: {html_escape(update_response)}", parse_mode="HTML")
                try:
                    await bot.send_message(user_tg_id, "خطا در تمدید اشتراک. لطفاً با پشتیبانی تماس بگیرید.", reply_markup=create_user_regular_menu(user_tg_id))
                except Exception as e:
                    logger.error(f"Failed to send error message to user {user_tg_id} (renewal): {e}", exc_info=True)

    except ValueError as ve:
        logger.error(f"Invalid data in confirm_renew callback: {callback_query.data}. Error: {ve}")
        await callback_query.answer("خطا در فرمت داده", show_alert=True)
    except Exception as e:
        logger.error(f"Error in handle_admin_confirm_renew: {e}", exc_info=True)
        await callback_query.answer("خطای داخلی رخ داد", show_alert=True)

@router.callback_query(F.data.startswith("admin_reject:"))
async def handle_admin_reject(callback_query: CallbackQuery, bot):
    admin_id = callback_query.from_user.id
    try:
        _, action_type, user_id_str = callback_query.data.split(":")
        user_id = int(user_id_str)
        action_text = "خرید" if action_type == "buy" else "تمدید"
        logger.warning(
            f"Admin {admin_id} REJECTED {action_text} for user {user_id}."
        )
        await callback_query.answer(f"درخواست {action_text} رد شد")

        original_caption = callback_query.message.caption
        original_photo_file_id = callback_query.message.photo[-1].file_id if callback_query.message.photo else None

        try:
            safe_caption_edit = html_escape(original_caption or "")
            await callback_query.message.edit_caption(
                caption=safe_caption_edit + "\n\n❌ رد شده توسط ادمین",
                reply_markup=None,
                parse_mode="HTML"
            )
        except Exception as edit_err:
             logger.warning(f"Could not edit caption for rejection (User: {user_id}): {edit_err}")

        rejection_msg = f"متأسفانه درخواست {action_text} شما رد شده است. لطفاً با پشتیبانی تماس بگیرید."
        reply_markup = (
            create_user_initial_menu()
            if action_type == "buy"
            else create_user_regular_menu(user_id)
        )
        try:
            await bot.send_message(user_id, rejection_msg, reply_markup=reply_markup)
            logger.info(f"Rejection message sent to user {user_id}.")
        except Exception as e:
            logger.error(
                f"Failed to send rejection message to user {user_id}: {e}",
                exc_info=True,
            )

        await callback_query.message.answer(
            f"❌ درخواست {action_text} کاربر {user_id} رد شد.",
            parse_mode="Markdown",
        )

    except ValueError:
        logger.error(
            f"Invalid callback data format for admin_reject: {callback_query.data}"
        )
        await callback_query.answer("خطا در فرمت داده", show_alert=True)
    except Exception as e:
        logger.error(f"Error in handle_admin_reject: {e}", exc_info=True)
        await callback_query.answer("خطای داخلی رخ داد", show_alert=True) 