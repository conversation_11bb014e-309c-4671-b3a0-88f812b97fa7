# hiddy_bot/ssh_utils.py

import logging  # type: ignore
import stat  # type: ignore
from io import BytesIO  # type: ignore

import paramiko  # type: ignore
import asyncio  # type: ignore

from messages import Messages  # type: ignore
from config import IP_SERVER, USER_SERVER, PASS_SERVER, SERVER_PORT, BACKUP_DIR_PATH  # type: ignore

logger = logging.getLogger(__name__)


# --- Generic SSH Connection ---

def _get_ssh_client():
    """Creates and returns a connected Paramiko SSH client."""
    logger.debug("Creating a new Paramiko SSH client instance.")
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    port = 22
    if SERVER_PORT and SERVER_PORT.isdigit():
        port = int(SERVER_PORT)
    logger.debug(f"Attempting to connect to {USER_SERVER}@{IP_SERVER}:{port}")
    
    ssh_client.connect(
        hostname=IP_SERVER,
        username=USER_SERVER,
        password=PASS_SERVER,
        port=port,
        timeout=15,
    )
    logger.info(f"SSH connection established successfully to {IP_SERVER}.")
    return ssh_client


# --- Server Stats ---

async def fetch_server_stats_ssh():
    """Fetches enhanced server stats (CPU, RAM, Disk, etc.) via SSH."""
    logger.info(f"Fetching server stats via SSH from {IP_SERVER}...")
    ssh_client = None
    server_info = {}

    try:
        ssh_client = _get_ssh_client()
        
        commands = {
            "cpu_usage": "top -bn1 | grep 'Cpu(s)' | sed 's/.*, *\\([0-9.]*\\)%* id.*/\\1/' | awk '{print 100 - $1}'",
            "ram_used": "free -m | awk 'NR==2{print $3}'",
            "ram_total": "free -m | awk 'NR==2{print $2}'",
            "swap_used": "free -m | awk 'NR==3{print $3}'",
            "swap_total": "free -m | awk 'NR==3{print $2}'",
            "disk_usage": "df -h / | awk 'NR==2{print $5}'",
            "disk_total": "df -h / | awk 'NR==2{print $2}'",
            "disk_used": "df -h / | awk 'NR==2{print $3}'",
            "uptime": "uptime -p",
            "load_avg": "uptime | grep -o 'load average: .*' | cut -d' ' -f3-",
            "processes": "ps -e --no-headers | wc -l",
            "users_online": "who | wc -l",
            "kernel_version": "uname -r",
            "os_info": 'cat /etc/os-release | grep PRETTY_NAME | cut -d\'=\' -f2 | tr -d \'"\'',
            "tcp_connections": "ss -tn | wc -l",
        }

        for key, command in commands.items():
            logger.debug(f"Executing SSH command for '{key}': {command}")
            stdin, stdout, stderr = ssh_client.exec_command(command, timeout=10)
            output = stdout.read().decode().strip()
            error = stderr.read().decode().strip()

            if error:
                logger.warning(f"SSH command for '{key}' produced an error: {error}")
                server_info[key] = f"Error: {error}"
            elif output:
                logger.debug(f"SSH command for '{key}' produced output: '{output}'")
                if key in ["ram_used", "ram_total", "swap_used", "swap_total"] and output.isdigit():
                    mb_value = int(output)
                    server_info[f"{key}_mb"] = mb_value
                    server_info[f"{key}_gb"] = round(mb_value / 1024, 2) if mb_value > 0 else 0.0
                elif key == "load_avg":
                    server_info[key] = output.replace(",", "")
                elif key == "tcp_connections" and output.isdigit():
                    server_info[key] = max(0, int(output) - 1)
                else:
                    server_info[key] = output
            else:
                logger.debug(f"SSH command for '{key}' produced no output.")
                server_info[key] = "N/A"

        logger.info("Successfully fetched all server stats via SSH.")
        return True, server_info

    except paramiko.AuthenticationException as e:
        logger.error(f"SSH Authentication failed: {e}", exc_info=True)
        return False, "Authentication failed (SSH)."
    except Exception as e:
        logger.error(f"An unexpected error occurred while fetching server stats via SSH: {e}", exc_info=True)
        return False, f"Error fetching server stats: {e}"
    finally:
        if ssh_client:
            logger.debug("Closing SSH client connection for server stats.")
            ssh_client.close()


# --- Backup Management ---

async def fetch_latest_backup_ssh():
    """Fetches the latest backup file from the server via SFTP."""
    logger.info("Fetching latest backup file via SFTP...")
    ssh_client = None
    sftp = None
    
    try:
        ssh_client = _get_ssh_client()
        logger.debug("Opening SFTP session.")
        sftp = ssh_client.open_sftp()
        
        logger.debug(f"Listing files in backup directory: {BACKUP_DIR_PATH}")
        files_in_backup = sftp.listdir_attr(BACKUP_DIR_PATH)
        
        backup_files = [f for f in files_in_backup if stat.S_ISREG(f.st_mode)]
        logger.debug(f"Found {len(backup_files)} regular files in the backup directory.")
        
        if not backup_files:
            logger.warning(f"No regular files found in backup directory: {BACKUP_DIR_PATH}")
            return False, Messages.Admin.Backup.FETCH_ERROR(error="No regular files found"), None

        latest_backup_attr = max(backup_files, key=lambda f: f.st_mtime)
        latest_backup_file = latest_backup_attr.filename
        
        backup_file_path = f"{BACKUP_DIR_PATH.rstrip('/')}/{latest_backup_file}"
        logger.info(f"Latest backup file identified: {latest_backup_file}. Path: {backup_file_path}")
        
        backup_file_obj = BytesIO()
        logger.debug(f"Starting download of '{latest_backup_file}' into memory.")
        sftp.getfo(backup_file_path, backup_file_obj)
        backup_file_obj.seek(0)
        
        logger.info(f"Successfully downloaded backup file '{latest_backup_file}' to memory.")
        return True, backup_file_obj, latest_backup_file

    except IOError as e:
        logger.error(f"SFTP IO error (e.g., directory not found): {e}", exc_info=True)
        return False, Messages.Admin.Backup.FETCH_ERROR(error=str(e)), None
    except Exception as e:
        logger.error(f"An unexpected error occurred while fetching backup via SFTP: {e}", exc_info=True)
        return False, f"Error fetching backup file: {e}", None
    finally:
        if sftp:
            logger.debug("Closing SFTP session.")
            sftp.close()
        if ssh_client:
            logger.debug("Closing SSH client connection for backup.")
            ssh_client.close()


# --- Instant Backup Creation & Fetch ---

async def create_instant_backup_ssh():
    """Creates a fresh backup on the panel and immediately fetches it via SFTP.
    It first executes the panel's backup script over SSH and then reuses
    `fetch_latest_backup_ssh` to download the newly created file.
    Returns the same tuple format as `fetch_latest_backup_ssh`."""
    logger.info("Creating instant backup via SSH (on-demand)...")
    ssh_client = None

    try:
        ssh_client = _get_ssh_client()
        backup_command = "/opt/hiddify-manager/hiddify-panel/backup.sh"
        logger.debug(f"Executing remote backup command: {backup_command}")
        stdin, stdout, stderr = ssh_client.exec_command(backup_command, timeout=600)

        # Wait for command to finish and capture exit status
        exit_status = stdout.channel.recv_exit_status()
        if exit_status != 0:
            error_output = stderr.read().decode().strip() or f"Exit status {exit_status}"
            logger.error(f"Backup command failed with status {exit_status}: {error_output}")
            return False, Messages.Admin.Backup.FETCH_ERROR(error=error_output), None

        logger.info("Backup command executed successfully. Fetching the newest backup file…")
        # Small delay to ensure filesystem sync on the remote server
        await asyncio.sleep(1)
        return await fetch_latest_backup_ssh()

    except Exception as e:
        logger.error(f"Unexpected error during instant backup creation: {e}", exc_info=True)
        return False, f"Error creating instant backup: {e}", None
    finally:
        if ssh_client:
            logger.debug("Closing SSH client after instant backup procedure.")
            ssh_client.close()