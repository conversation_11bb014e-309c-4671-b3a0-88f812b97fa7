# hiddy_bot/database.py

import sqlite3  # type: ignore
import logging  # type: ignore
import datetime  # type: ignore
import os  # type: ignore
import json  # type: ignore
import shutil  # type: ignore
from typing import Union, List, Tuple, Dict, Any  # type: ignore

import jdatetime  # type: ignore

from config import DATABASE_FILE, PROJECT_DIR  # type: ignore

# لاگر مخصوص این فایل را دریافت می‌کنیم
logger = logging.getLogger(__name__)

# مسیر پوشه JSON
JSON_DIR = os.path.join(PROJECT_DIR, "json_database")
USERS_JSON_FILE = os.path.join(JSON_DIR, "users.json")
PANEL_USERS_JSON_FILE = os.path.join(JSON_DIR, "panel_users.json")
DELETED_USERS_JSON_FILE = os.path.join(JSON_DIR, "deleted_users.json")
INCOME_TRANSACTIONS_JSON_FILE = os.path.join(JSON_DIR, "income_transactions.json")
BOT_SETTINGS_JSON_FILE = os.path.join(JSON_DIR, "bot_settings.json")
EMERGENCY_CONFIG_JSON_FILE = os.path.join(JSON_DIR, "emergency_config.json")
SUBSCRIPTION_PLANS_JSON_FILE = os.path.join(JSON_DIR, "subscription_plans.json")

def ensure_json_directory():
    """اطمینان از وجود پوشه JSON"""
    if not os.path.exists(JSON_DIR):
        os.makedirs(JSON_DIR)
        logger.info(f"Created JSON directory: {JSON_DIR}")

def load_json_file(file_path: str, default_value: Union[list, dict] = None) -> Union[list, dict]:
    """بارگیری فایل JSON با مدیریت خطا"""
    if default_value is None:
        default_value = []
    
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.debug(f"Loaded JSON file: {file_path}")
            return data
        else:
            logger.debug(f"JSON file not found, returning default: {file_path}")
            return default_value
    except (json.JSONDecodeError, IOError) as e:
        logger.error(f"Error loading JSON file {file_path}: {e}")
        # Create backup if file is corrupted
        if os.path.exists(file_path):
            backup_path = f"{file_path}.backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(file_path, backup_path)
            logger.warning(f"Created backup of corrupted file: {backup_path}")
        return default_value

def save_json_file(file_path: str, data: Union[list, dict]) -> bool:
    """ذخیره فایل JSON با مدیریت خطا"""
    try:
        ensure_json_directory()
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.debug(f"Saved JSON file: {file_path}")
        return True
    except (IOError, TypeError) as e:
        logger.error(f"Error saving JSON file {file_path}: {e}")
        return False

def sync_sqlite_to_json():
    """همگام‌سازی کامل دیتابیس SQLite با فایل‌های JSON"""
    logger.info("Starting full SQLite to JSON synchronization")
    ensure_json_directory()
    
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        # همگام‌سازی جدول newusers
        cursor.execute("SELECT name, t_id, num_id, join_time, uuid, is_trial FROM newusers")
        users_data = []
        for row in cursor.fetchall():
            users_data.append({
                'name': row[0],
                't_id': row[1],
                'num_id': row[2],
                'join_time': row[3],
                'uuid': row[4],
                'is_trial': row[5]
            })
        save_json_file(USERS_JSON_FILE, users_data)
        
        # همگام‌سازی جدول panelusers
        cursor.execute("SELECT * FROM panelusers")
        panel_columns = [desc[0] for desc in cursor.description]
        panel_users_data = []
        for row in cursor.fetchall():
            panel_users_data.append(dict(zip(panel_columns, row)))
        save_json_file(PANEL_USERS_JSON_FILE, panel_users_data)
        
        # همگام‌سازی جدول deleteusers
        cursor.execute("SELECT * FROM deleteusers")
        deleted_columns = [desc[0] for desc in cursor.description]
        deleted_users_data = []
        for row in cursor.fetchall():
            deleted_users_data.append(dict(zip(deleted_columns, row)))
        save_json_file(DELETED_USERS_JSON_FILE, deleted_users_data)
        
        # همگام‌سازی جدول income_transactions
        cursor.execute("SELECT * FROM income_transactions")
        income_columns = [desc[0] for desc in cursor.description]
        income_data = []
        for row in cursor.fetchall():
            income_data.append(dict(zip(income_columns, row)))
        save_json_file(INCOME_TRANSACTIONS_JSON_FILE, income_data)
        
        # همگام‌سازی جدول bot_settings
        cursor.execute("SELECT setting_key, setting_value, updated_at FROM bot_settings")
        settings_data = {}
        for row in cursor.fetchall():
            settings_data[row[0]] = {
                'value': row[1],
                'updated_at': row[2]
            }
        save_json_file(BOT_SETTINGS_JSON_FILE, settings_data)
        
        # همگام‌سازی جدول emergency_config
        cursor.execute("SELECT * FROM emergency_config")
        if cursor.description:
            emergency_columns = [desc[0] for desc in cursor.description]
            emergency_data = []
            for row in cursor.fetchall():
                emergency_data.append(dict(zip(emergency_columns, row)))
            save_json_file(EMERGENCY_CONFIG_JSON_FILE, emergency_data)
        
        # همگام‌سازی جدول subscription_plans
        cursor.execute("SELECT * FROM subscription_plans")
        if cursor.description:
            plans_columns = [desc[0] for desc in cursor.description]
            plans_data = []
            for row in cursor.fetchall():
                plans_data.append(dict(zip(plans_columns, row)))
            save_json_file(SUBSCRIPTION_PLANS_JSON_FILE, plans_data)
        
        logger.info("Successfully synchronized SQLite to JSON")
        return True
        
    except sqlite3.Error as e:
        logger.error(f"SQLite error during sync: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during sync: {e}")
        return False
    finally:
        if conn:
            conn.close()


# --- Table Creation ---
def create_database_table():
    """Initializes the SQLite database and creates necessary tables if they don't exist."""
    logger.info(f"Checking and initializing database tables in '{DATABASE_FILE}'...")
    conn = None
    try:
        logger.debug("Connecting to database for table creation.")
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        logger.debug("Executing CREATE TABLE IF NOT EXISTS for 'newusers'.")
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS newusers (
                name TEXT,
                t_id TEXT,
                num_id INTEGER PRIMARY KEY,
                join_time TEXT,
                uuid TEXT UNIQUE,
                is_trial INTEGER DEFAULT 0
            )"""
        )

        logger.debug("Executing CREATE TABLE IF NOT EXISTS for 'panelusers'.")
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS panelusers (
                added_by_uuid TEXT, comment TEXT, current_usage_GB REAL DEFAULT 0.0,
                ed25519_private_key TEXT, ed25519_public_key TEXT, enable INTEGER DEFAULT 1,
                id INTEGER, is_active INTEGER DEFAULT 1, lang TEXT DEFAULT 'en',
                last_online TEXT, last_reset_time TEXT, mode TEXT DEFAULT 'no_reset',
                name TEXT NOT NULL, package_days INTEGER DEFAULT 30, start_date TEXT,
                telegram_id INTEGER, usage_limit_GB REAL DEFAULT 0.0,
                uuid TEXT PRIMARY KEY NOT NULL, wg_pk TEXT, wg_psk TEXT, wg_pub TEXT,
                panel_link TEXT
            )"""
        )
        
        logger.debug("Executing CREATE TABLE IF NOT EXISTS for 'deleteusers'.")
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS deleteusers (
                deleted_at TEXT DEFAULT CURRENT_TIMESTAMP, added_by_uuid TEXT, comment TEXT,
                current_usage_GB REAL, ed25519_private_key TEXT, ed25519_public_key TEXT,
                enable INTEGER, id INTEGER, is_active INTEGER, lang TEXT, last_online TEXT,
                last_reset_time TEXT, mode TEXT, name TEXT, package_days INTEGER,
                start_date TEXT, telegram_id INTEGER, usage_limit_GB REAL,
                uuid TEXT PRIMARY KEY NOT NULL, wg_pk TEXT, wg_psk TEXT, wg_pub TEXT,
                panel_link TEXT
            )"""
        )

        logger.debug("Executing CREATE TABLE IF NOT EXISTS for 'income_transactions'.")
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS income_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_tg_id INTEGER NOT NULL,
                user_uuid TEXT,
                transaction_type TEXT NOT NULL, -- 'buy', 'renew', 'upgrade_from_trial'
                plan_id TEXT NOT NULL,
                plan_description TEXT,
                amount_paid REAL NOT NULL,
                transaction_date TEXT NOT NULL, -- YYYY-MM-DD of payment confirmation
                admin_approver_id INTEGER,
                approval_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                related_receipt_filename TEXT
            )"""
        )

        # جدول تنظیمات ربات
        logger.debug("Executing CREATE TABLE IF NOT EXISTS for 'bot_settings'.")
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS bot_settings (
                setting_key TEXT PRIMARY KEY,
                setting_value TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )"""
        )

        # جدول پیام اضطراری
        logger.debug("Executing CREATE TABLE IF NOT EXISTS for 'emergency_config'.")
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS emergency_config (
                id INTEGER PRIMARY KEY,
                message_text TEXT,
                media_type TEXT, -- 'photo', 'video', 'document', 'voice', 'audio', 'animation', 'sticker', None
                media_file_id TEXT,
                media_caption TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER
            )"""
        )

        # جدول پلن‌های خرید
        logger.debug("Executing CREATE TABLE IF NOT EXISTS for 'subscription_plans'.")
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS subscription_plans (
                plan_id TEXT PRIMARY KEY,
                plan_name TEXT NOT NULL,
                description TEXT,
                days INTEGER NOT NULL,
                volume_gb REAL NOT NULL,
                price_toman INTEGER NOT NULL,
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )"""
        )

        conn.commit()
        logger.info("Database tables initialized or verified successfully.")
        
        # اضافه کردن تنظیمات پیش‌فرض
        _initialize_default_settings(cursor)
        _initialize_default_plans(cursor)
        conn.commit()
        
        # همگام‌سازی با JSON پس از ایجاد جداول
        logger.info("Performing initial SQLite to JSON synchronization...")
        sync_success = sync_sqlite_to_json()
        if sync_success:
            logger.info("Initial synchronization completed successfully.")
        else:
            logger.warning("Initial synchronization had some issues, but continuing...")
        
    except sqlite3.Error as e:
        logger.error("SQLite error during table creation: %s", e, exc_info=True)
        if conn:
            logger.debug("Rolling back transaction due to error.")
            conn.rollback()
    finally:
        if conn:
            logger.debug("Closing database connection after table creation.")
            conn.close()


def _initialize_default_settings(cursor):
    """Initialize default bot settings"""
    default_settings = [
        ('buy_subscription_enabled', '1'),
        ('trial_subscription_enabled', '1'),
        ('bank_account_name', 'مهدی بزرگ نیا'),
        ('bank_card_number', '****************')
    ]
    
    for key, value in default_settings:
        cursor.execute(
            "INSERT OR IGNORE INTO bot_settings (setting_key, setting_value) VALUES (?, ?)",
            (key, value)
        )
    logger.debug("Default bot settings initialized.")


def _initialize_default_plans(cursor):
    """Initialize default subscription plans"""
    default_plans = [
        ('1month_100gb', 'یک ماهه | تک کاربره | 100 گیگ', 'پلن یک ماهه با 100 گیگابایت حجم', 31, 100.0, 100000)
    ]
    
    for plan_id, name, desc, days, volume, price in default_plans:
        cursor.execute(
            """INSERT OR IGNORE INTO subscription_plans 
               (plan_id, plan_name, description, days, volume_gb, price_toman) 
               VALUES (?, ?, ?, ?, ?, ?)""",
            (plan_id, name, desc, days, volume, price)
        )
    logger.debug("Default subscription plans initialized.")


# --- User Data Management (newusers table) ---
def update_or_add_user(name: str, t_id: str, num_id: int, join_time: str, uuid: Union[str, None] = None, is_trial: int = 0) -> Union[str, None]:
    """Adds or updates a user in the 'newusers' table and JSON file."""
    logger.info(f"Attempting to update/add user in 'newusers': num_id={num_id}, uuid={uuid}, is_trial={is_trial}")
    conn = None
    try:
        logger.debug(f"Connecting to database for user {num_id}.")
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        # If UUID is provided, check if it's already assigned to a different user
        if uuid:
            logger.debug(f"Checking if UUID {uuid} is already assigned to another user.")
            cursor.execute("SELECT num_id FROM newusers WHERE uuid=? AND num_id != ?", (uuid, num_id))
            existing_user = cursor.fetchone()
            if existing_user:
                existing_user_id = existing_user[0]
                logger.warning(f"UUID {uuid} is already assigned to user {existing_user_id}. Cannot assign to user {num_id}.")
                
                # Check if the current user already has this UUID
                cursor.execute("SELECT uuid FROM newusers WHERE num_id=?", (num_id,))
                current_user_data = cursor.fetchone()
                if current_user_data and current_user_data[0] == uuid:
                    logger.info(f"User {num_id} already has UUID {uuid}. Just updating other fields.")
                    # User already has this UUID, just update other fields
                    cursor.execute("UPDATE newusers SET name = ?, t_id = ?, is_trial = ? WHERE num_id = ?",
                                   (name, t_id, is_trial, num_id))
                    logger.info(f"Successfully updated user {num_id} (UUID already assigned).")
                    conn.commit()
                    
                    # همگام‌سازی با JSON
                    _sync_users_to_json()
                    
                    return uuid
                else:
                    # UUID belongs to another user - this is an authentication conflict
                    logger.error(f"Authentication conflict: UUID {uuid} belongs to user {existing_user_id}, not {num_id}")
                    return None
        
        logger.debug(f"Checking if user {num_id} already exists in 'newusers'.")
        cursor.execute("SELECT uuid FROM newusers WHERE num_id=?", (num_id,))
        existing_record = cursor.fetchone()
        
        if existing_record:
            logger.debug(f"User {num_id} exists. Preparing UPDATE statement.")
            cursor.execute("UPDATE newusers SET name = ?, t_id = ?, uuid = ?, is_trial = ? WHERE num_id = ?",
                           (name, t_id, uuid, is_trial, num_id))
            logger.info(f"Successfully updated user {num_id}.")
        else:
            logger.debug(f"User {num_id} does not exist. Preparing INSERT statement.")
            cursor.execute("INSERT INTO newusers (name, t_id, num_id, join_time, uuid, is_trial) VALUES (?, ?, ?, ?, ?, ?)",
                           (name, t_id, num_id, join_time, uuid, is_trial))
            logger.info(f"Successfully inserted new user {num_id}.")
            
        conn.commit()
        
        # همگام‌سازی با JSON
        logger.debug(f"Syncing user {num_id} to JSON file.")
        _sync_users_to_json()
        
        return uuid
    except sqlite3.IntegrityError as e:
        if "UNIQUE constraint failed: newusers.uuid" in str(e):
            logger.error(f"UNIQUE constraint failed for UUID {uuid} with user {num_id}. This UUID is already in use by another user.")
            # Try to get the conflicting user
            try:
                cursor.execute("SELECT num_id, name FROM newusers WHERE uuid=?", (uuid,))
                conflicting_user = cursor.fetchone()
                if conflicting_user:
                    logger.error(f"UUID {uuid} is already assigned to user {conflicting_user[0]} ({conflicting_user[1]})")
            except:
                pass
            return None
        else:
            logger.error(f"Integrity error in update_or_add_user for user {num_id}: {e}", exc_info=True)
            if conn:
                logger.debug(f"Rolling back transaction for user {num_id} due to integrity error.")
                conn.rollback()
            return None
    except sqlite3.Error as e:
        logger.error(f"SQLite error in update_or_add_user for user {num_id}: {e}", exc_info=True)
        if conn:
            logger.debug(f"Rolling back transaction for user {num_id} due to error.")
            conn.rollback()
        return None
    finally:
        if conn:
            logger.debug(f"Closing database connection for user {num_id}.")
            conn.close()

def _sync_users_to_json():
    """همگام‌سازی جدول newusers با JSON"""
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT name, t_id, num_id, join_time, uuid, is_trial FROM newusers")
        users_data = []
        for row in cursor.fetchall():
            users_data.append({
                'name': row[0],
                't_id': row[1],
                'num_id': row[2],
                'join_time': row[3],
                'uuid': row[4],
                'is_trial': row[5]
            })
        save_json_file(USERS_JSON_FILE, users_data)
        conn.close()
        logger.debug("Users synced to JSON successfully.")
    except Exception as e:
        logger.error(f"Error syncing users to JSON: {e}")

def get_user_from_database(num_id: int) -> Union[Tuple, None]:
    """Fetches user data from the 'newusers' table by their numeric Telegram ID."""
    logger.debug(f"Fetching user from 'newusers' by num_id: {num_id}")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT name, t_id, num_id, join_time, uuid, is_trial FROM newusers WHERE num_id=?", (num_id,))
        user_data = cursor.fetchone()
        if user_data:
            logger.debug(f"User {num_id} found in database.")
        else:
            logger.debug(f"User {num_id} not found in database.")
        return user_data
    except sqlite3.Error as e:
        logger.error(f"SQLite error fetching user {num_id}: {e}", exc_info=True)
        return None
    finally:
        if conn:
            logger.debug(f"Closing database connection after fetching user {num_id}.")
            conn.close()

def get_user_from_database_by_uuid(uuid: str) -> Union[Tuple, None]:
    """Fetches user data from the 'newusers' table by their Hiddify UUID."""
    logger.debug(f"Fetching user from 'newusers' by uuid: {uuid}")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT name, t_id, num_id, join_time, uuid FROM newusers WHERE uuid=?", (uuid,))
        user_data = cursor.fetchone()
        if user_data:
            logger.debug(f"User with UUID {uuid} found in database.")
        else:
            logger.debug(f"User with UUID {uuid} not found in database.")
        return user_data
    except sqlite3.Error as e:
        logger.error(f"SQLite error fetching user by uuid {uuid}: {e}", exc_info=True)
        return None
    finally:
        if conn:
            logger.debug(f"Closing database connection after fetching user by uuid {uuid}.")
            conn.close()

def get_all_users_with_uuid() -> List[Tuple]:
    """Fetches all users (num_id, uuid, is_trial) who have a UUID linked."""
    logger.debug("Fetching all users with a non-null UUID from 'newusers'.")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT num_id, uuid, is_trial FROM newusers WHERE uuid IS NOT NULL")
        users = cursor.fetchall()
        logger.debug(f"Found {len(users)} users with UUIDs.")
        return users
    except sqlite3.Error as e:
        logger.error("SQLite error fetching all users with UUID: {e}", exc_info=True)
        return []
    finally:
        if conn:
            logger.debug("Closing database connection after fetching all users with UUID.")
            conn.close()

def get_all_bot_users() -> List[Tuple]:
    """Fetches all users from the 'newusers' table."""
    logger.debug("Fetching all users from 'newusers'.")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT name, t_id, num_id, join_time, uuid, is_trial FROM newusers")
        users = cursor.fetchall()
        logger.debug(f"Found {len(users)} total users in 'newusers' table.")
        return users
    except sqlite3.Error as e:
        logger.error("SQLite error fetching all bot users: {e}", exc_info=True)
        return []
    finally:
        if conn:
            logger.debug("Closing database connection after fetching all bot users.")
            conn.close()


def clear_user_uuid(num_id: int):
    """Resets the UUID and trial status for a user."""
    logger.warning(f"Clearing UUID and trial status for user {num_id}.")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("UPDATE newusers SET uuid = NULL, is_trial = 0 WHERE num_id = ?", (num_id,))
        conn.commit()
        logger.info(f"Successfully cleared UUID for user {num_id}.")
        
        # همگام‌سازی با JSON
        _sync_users_to_json()
        
    except sqlite3.Error as e:
        logger.error(f"Error clearing UUID for user {num_id}: {e}", exc_info=True)
        if conn: conn.rollback()
    finally:
        if conn: conn.close()

def get_user_trial_status(num_id: int) -> bool:
    """Checks if a user has the is_trial flag set."""
    logger.debug(f"Checking trial status for user {num_id}.")
    user_data = get_user_from_database(num_id)
    is_trial = user_data is not None and user_data[5] == 1
    logger.debug(f"User {num_id} trial status is: {is_trial}.")
    return is_trial


# --- Panel User Cache Management (panelusers table) ---
async def store_panel_users_in_db(users_data: List[dict]) -> Tuple[bool, Union[str, None]]:
    """Deletes all existing users from 'panelusers' and inserts the fresh list, also syncs to JSON."""
    logger.info(f"Attempting to store {len(users_data)} panel users in local DB cache and JSON.")
    if not users_data:
        logger.warning("No user data provided to store_panel_users_in_db. Aborting.")
        return True, None
        
    db_insert_data = []
    logger.debug("Preparing user data for batch insertion.")
    for user in users_data:
        if user.get("uuid"):
            db_insert_data.append((
                user.get("added_by_uuid"), user.get("comment"), user.get("current_usage_GB", 0.0),
                user.get("ed25519_private_key"), user.get("ed25519_public_key"), user.get("enable", 1),
                user.get("id"), user.get("is_active", 1), user.get("lang", "en"),
                user.get("last_online"), user.get("last_reset_time"), user.get("mode", "no_reset"),
                user.get("name", f"User_{user.get('uuid', 'no_uuid')[:8]}"), user.get("package_days", 30),
                user.get("start_date"), user.get("telegram_id"), user.get("usage_limit_GB", 0.0),
                user.get("uuid"), user.get("wg_pk"), user.get("wg_psk"), user.get("wg_pub"), None
            ))
        else:
            logger.warning(f"Skipping a user from panel data because it has no UUID: {user.get('name', 'No Name')}")

    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        logger.warning("Executing DELETE FROM panelusers to clear the cache.")
        cursor.execute("DELETE FROM panelusers")
        
        logger.debug(f"Executing executemany to insert {len(db_insert_data)} users.")
        cursor.executemany("INSERT INTO panelusers VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", db_insert_data)
        
        conn.commit()
        logger.info("Successfully stored panel users in the database.")
        
        # همگام‌سازی با JSON
        logger.debug("Syncing panel users to JSON file.")
        _sync_panel_users_to_json()
        
        return True, None
    except sqlite3.Error as e:
        logger.error(f"SQLite error storing panel users: {e}", exc_info=True)
        if conn: conn.rollback()
        return False, str(e)
    finally:
        if conn:
            logger.debug("Closing database connection after storing panel users.")
            conn.close()

def _sync_panel_users_to_json():
    """همگام‌سازی جدول panelusers با JSON"""
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM panelusers")
        panel_columns = [desc[0] for desc in cursor.description]
        panel_users_data = []
        for row in cursor.fetchall():
            panel_users_data.append(dict(zip(panel_columns, row)))
        save_json_file(PANEL_USERS_JSON_FILE, panel_users_data)
        conn.close()
        logger.debug("Panel users synced to JSON successfully.")
    except Exception as e:
        logger.error(f"Error syncing panel users to JSON: {e}")

def archive_deleted_user(uuid_to_delete: str) -> bool:
    logger.warning(f"Archiving user {uuid_to_delete} from 'panelusers' to 'deleteusers'.")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        logger.debug(f"Fetching user {uuid_to_delete} from panelusers for archival.")
        cursor.execute("SELECT * FROM panelusers WHERE uuid=?", (uuid_to_delete,))
        user_data = cursor.fetchone()
        if user_data:
            columns = [desc[0] for desc in cursor.description]
            logger.debug(f"Inserting user {uuid_to_delete} into deleteusers.")
            cursor.execute(f"INSERT INTO deleteusers ({','.join(columns)}) VALUES ({','.join(['?']*len(columns))})", user_data)
            logger.debug(f"Deleting user {uuid_to_delete} from panelusers.")
            cursor.execute("DELETE FROM panelusers WHERE uuid=?", (uuid_to_delete,))
            conn.commit()
            logger.info(f"Successfully archived user {uuid_to_delete}.")
            
            # همگام‌سازی با JSON
            _sync_panel_users_to_json()  # برای به‌روزرسانی panelusers
            _sync_deleted_users_to_json()  # برای به‌روزرسانی deleteusers
            
            return True
        logger.warning(f"User {uuid_to_delete} not found in panelusers for archival.")
        return False
    except sqlite3.Error as e:
        logger.error(f"DB: SQLite error archiving user {uuid_to_delete}: {e}", exc_info=True)
        if conn: conn.rollback()
        return False
    finally:
        if conn: conn.close()

def _sync_deleted_users_to_json():
    """همگام‌سازی جدول deleteusers با JSON"""
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM deleteusers")
        deleted_columns = [desc[0] for desc in cursor.description]
        deleted_users_data = []
        for row in cursor.fetchall():
            deleted_users_data.append(dict(zip(deleted_columns, row)))
        save_json_file(DELETED_USERS_JSON_FILE, deleted_users_data)
        conn.close()
        logger.debug("Deleted users synced to JSON successfully.")
    except Exception as e:
        logger.error(f"Error syncing deleted users to JSON: {e}")

def get_panel_users_page(page_num: int, page_size: int = 9, user_type: str = 'regular') -> List[Tuple]:
    logger.debug(f"Fetching page {page_num} of panel users. Type: {user_type}, Page size: {page_size}.")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        offset = (page_num - 1) * page_size
        sql, params = "SELECT uuid, name FROM panelusers", []
        if user_type == 'regular':
            sql += " WHERE name NOT LIKE ?"
            params.append('Trial_%')
        elif user_type == 'trial':
            sql += " WHERE name LIKE ?"
            params.append('Trial_%')
        sql += " ORDER BY name LIMIT ? OFFSET ?"
        params.extend([page_size, offset])
        logger.debug(f"Executing SQL for pagination: {sql} with params: {params}")
        cursor.execute(sql, tuple(params))
        users = cursor.fetchall()
        logger.debug(f"Fetched {len(users)} users for page {page_num}.")
        return users
    except sqlite3.Error as e:
        logger.error(f"DB: SQLite error fetching users page {page_num}: {e}", exc_info=True)
        return []
    finally:
        if conn: conn.close()

def get_total_panel_users_count(user_type: str = 'all') -> int:
    logger.debug(f"Counting total panel users of type: {user_type}")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        sql, params = "SELECT COUNT(*) FROM panelusers", []
        if user_type == 'regular':
            sql += " WHERE name NOT LIKE ?"
            params.append('Trial_%')
        elif user_type == 'trial':
            sql += " WHERE name LIKE ?"
            params.append('Trial_%')
        logger.debug(f"Executing SQL for count: {sql} with params: {params}")
        cursor.execute(sql, tuple(params))
        count = cursor.fetchone()[0]
        logger.debug(f"Total count is {count}.")
        return count
    except sqlite3.Error as e:
        logger.error(f"DB: SQLite error counting panel users: {e}", exc_info=True)
        return 0
    finally:
        if conn: conn.close()


# --- Income Transaction Management ---
def log_income_transaction(user_tg_id: int, user_uuid: Union[str, None], transaction_type: str,
                           plan_id: str, plan_description: str, amount_paid: float,
                           admin_approver_id: int, related_receipt_filename: Union[str, None] = None):
    logger.info(f"Logging income transaction for user {user_tg_id}, type: {transaction_type}, amount: {amount_paid}")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO income_transactions (
                user_tg_id, user_uuid, transaction_type, plan_id, plan_description,
                amount_paid, transaction_date, admin_approver_id, approval_timestamp,
                related_receipt_filename
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
            (user_tg_id, user_uuid, transaction_type, plan_id, plan_description, amount_paid,
             datetime.date.today().strftime("%Y-%m-%d"), admin_approver_id,
             datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), related_receipt_filename)
        )
        conn.commit()
        logger.info("Successfully logged income transaction.")
        
        # همگام‌سازی با JSON
        _sync_income_transactions_to_json()
        
    except sqlite3.Error as e:
        logger.error(f"DB: SQLite error logging income: {e}", exc_info=True)
        if conn: conn.rollback()
    finally:
        if conn: conn.close()

def _sync_income_transactions_to_json():
    """همگام‌سازی جدول income_transactions با JSON"""
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM income_transactions")
        income_columns = [desc[0] for desc in cursor.description]
        income_data = []
        for row in cursor.fetchall():
            income_data.append(dict(zip(income_columns, row)))
        save_json_file(INCOME_TRANSACTIONS_JSON_FILE, income_data)
        conn.close()
        logger.debug("Income transactions synced to JSON successfully.")
    except Exception as e:
        logger.error(f"Error syncing income transactions to JSON: {e}")

def get_transaction_stats_for_period(start_date_str: str, end_date_str: str) -> tuple[float, int, int, list, list]:
    logger.debug(f"Calculating transaction stats for period {start_date_str} to {end_date_str}")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT SUM(amount_paid) FROM income_transactions WHERE transaction_date BETWEEN ? AND ?", (start_date_str, end_date_str))
        total_income = (res[0] if (res := cursor.fetchone()) and res[0] else 0.0)
        cursor.execute("SELECT DISTINCT user_tg_id FROM income_transactions WHERE transaction_type IN ('buy', 'upgrade_from_trial') AND transaction_date BETWEEN ? AND ?", (start_date_str, end_date_str))
        new_ids = [r[0] for r in cursor.fetchall()]
        cursor.execute("SELECT DISTINCT user_tg_id FROM income_transactions WHERE transaction_type = 'renew' AND transaction_date BETWEEN ? AND ?", (start_date_str, end_date_str))
        renew_ids = [r[0] for r in cursor.fetchall()]
        logger.debug(f"Stats result: Income={total_income}, New={len(new_ids)}, Renewed={len(renew_ids)}")
        return total_income, len(new_ids), len(renew_ids), new_ids, renew_ids
    except sqlite3.Error as e:
        logger.error(f"DB: SQLite error in get_transaction_stats: {e}", exc_info=True)
        return 0.0, 0, 0, [], []
    finally:
        if conn: conn.close()

def find_latest_receipt(directory: str) -> Union[str, None]:
    logger.debug(f"Finding latest receipt in directory: {directory}")
    if not os.path.isdir(directory): 
        logger.warning(f"Directory not found: {directory}")
        return None
    try:
        receipts = [os.path.join(directory, f) for f in os.listdir(directory) if f.startswith("receipt_")]
        latest = max(receipts, key=os.path.getctime) if receipts else None
        logger.debug(f"Latest receipt found: {latest}")
        return latest
    except Exception as e:
        logger.error(f"DB: Error finding latest receipt in {directory}: {e}", exc_info=True)
        return None

async def get_renewal_projection_stats() -> tuple[int, int, float, list, list]:
    logger.debug("Calculating renewal projection stats.")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        today, current_month_start = datetime.date.today(), datetime.date.today().replace(day=1)
        next_month_start = (current_month_start + datetime.timedelta(days=32)).replace(day=1)
        current_month_end, seven_days_from_now = next_month_start - datetime.timedelta(days=1), today + datetime.timedelta(days=7)

        cursor.execute("SELECT uuid, start_date, package_days FROM panelusers WHERE enable = 1 AND is_active = 1")
        active_users = cursor.fetchall()
        logger.debug(f"Found {len(active_users)} active users for projection.")
        due_uuids, expiring_uuids, projected_income = [], [], 0.0

        for uuid, start_date_str, package_days in active_users:
            if not (start_date_str and package_days is not None): continue
            try:
                expiry_date = datetime.datetime.strptime(start_date_str, "%Y-%m-%d").date() + datetime.timedelta(days=int(package_days))
                if today <= expiry_date <= seven_days_from_now: expiring_uuids.append(uuid)
                if current_month_start <= expiry_date <= current_month_end:
                    due_uuids.append(uuid)
                    cursor.execute("SELECT amount_paid FROM income_transactions WHERE user_uuid = ? ORDER BY transaction_date DESC, id DESC LIMIT 1", (uuid,))
                    if (res := cursor.fetchone()) and res[0] is not None: projected_income += res[0]
            except (ValueError, TypeError): continue
        logger.debug(f"Projection stats: Due={len(due_uuids)}, Expiring={len(expiring_uuids)}, Income={projected_income}")
        return len(due_uuids), len(expiring_uuids), projected_income, due_uuids, expiring_uuids
    except sqlite3.Error as e:
        logger.error(f"DB: SQLite error in get_renewal_projection: {e}", exc_info=True)
        return 0, 0, 0.0, [], []
    finally:
        if conn: conn.close()


# --- Bot Settings Management ---
def get_bot_setting(setting_key: str, default_value: str = None) -> str:
    """Get a bot setting value by key"""
    logger.debug(f"Getting bot setting: {setting_key}")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT setting_value FROM bot_settings WHERE setting_key = ?", (setting_key,))
        result = cursor.fetchone()
        value = result[0] if result else default_value
        logger.debug(f"Bot setting {setting_key} = {value}")
        return value
    except sqlite3.OperationalError as e:
        if "no such table: bot_settings" in str(e):
            logger.warning(f"bot_settings table not found. Initializing database tables...")
            if conn: conn.close()
            create_database_table()
            # Retry after creating tables
            return get_bot_setting(setting_key, default_value)
        else:
            logger.error(f"SQLite OperationalError getting bot setting {setting_key}: {e}", exc_info=True)
            return default_value
    except sqlite3.Error as e:
        logger.error(f"Error getting bot setting {setting_key}: {e}", exc_info=True)
        return default_value
    finally:
        if conn: conn.close()


def set_bot_setting(setting_key: str, setting_value: str) -> bool:
    """Set a bot setting value"""
    logger.info(f"Setting bot setting: {setting_key} = {setting_value}")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute(
            """INSERT OR REPLACE INTO bot_settings (setting_key, setting_value, updated_at) 
               VALUES (?, ?, ?)""",
            (setting_key, setting_value, datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        )
        conn.commit()
        logger.info(f"Successfully set bot setting {setting_key}")
        
        # همگام‌سازی با JSON
        _sync_bot_settings_to_json()
        
        return True
    except sqlite3.Error as e:
        logger.error(f"Error setting bot setting {setting_key}: {e}", exc_info=True)
        return False
    finally:
        if conn: conn.close()


def _sync_bot_settings_to_json():
    """همگام‌سازی جدول bot_settings با JSON"""
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT setting_key, setting_value, updated_at FROM bot_settings")
        settings_data = {}
        for row in cursor.fetchall():
            settings_data[row[0]] = {
                'value': row[1],
                'updated_at': row[2]
            }
        save_json_file(BOT_SETTINGS_JSON_FILE, settings_data)
        conn.close()
        logger.debug("Bot settings synced to JSON successfully.")
    except Exception as e:
        logger.error(f"Error syncing bot settings to JSON: {e}")


def get_all_bot_settings() -> Dict[str, str]:
    """Get all bot settings as a dictionary"""
    logger.debug("Getting all bot settings")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT setting_key, setting_value FROM bot_settings")
        settings = dict(cursor.fetchall())
        logger.debug(f"Retrieved {len(settings)} bot settings")
        return settings
    except sqlite3.Error as e:
        logger.error(f"Error getting all bot settings: {e}", exc_info=True)
        return {}
    finally:
        if conn: conn.close()


def get_renewal_method() -> int:
    """Get the currently configured renewal method."""
    # Default to 2 (accumulate days and data) if not set
    return int(get_bot_setting('renewal_method', '2'))


def set_renewal_method(method: int) -> bool:
    """Set the renewal method for user subscriptions."""
    return set_bot_setting('renewal_method', str(method))


# --- Emergency Config Management ---
def save_emergency_config(message_text: str, media_type: str = None, media_file_id: str = None, 
                         media_caption: str = None, created_by: int = None) -> bool:
    """Save emergency config message"""
    logger.info("Saving emergency config message")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        # حذف پیام قبلی
        cursor.execute("DELETE FROM emergency_config")
        
        # اضافه کردن پیام جدید
        cursor.execute(
            """INSERT INTO emergency_config 
               (message_text, media_type, media_file_id, media_caption, created_by) 
               VALUES (?, ?, ?, ?, ?)""",
            (message_text, media_type, media_file_id, media_caption, created_by)
        )
        
        conn.commit()
        logger.info("Emergency config saved successfully")
        
        # همگام‌سازی با JSON
        _sync_emergency_config_to_json()
        
        return True
    except sqlite3.Error as e:
        logger.error(f"Error saving emergency config: {e}", exc_info=True)
        return False
    finally:
        if conn: conn.close()

def delete_emergency_config() -> bool:
    """Delete emergency config"""
    logger.info("Deleting emergency config")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM emergency_config")
        conn.commit()
        logger.info("Emergency config deleted successfully")
        
        # همگام‌سازی با JSON (پاک کردن فایل)
        _sync_emergency_config_to_json()
        
        return True
    except sqlite3.Error as e:
        logger.error(f"Error deleting emergency config: {e}", exc_info=True)
        return False
    finally:
        if conn: conn.close()

def _sync_emergency_config_to_json():
    """همگام‌سازی جدول emergency_config با JSON"""
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM emergency_config")
        if cursor.description:
            emergency_columns = [desc[0] for desc in cursor.description]
            emergency_data = []
            for row in cursor.fetchall():
                emergency_data.append(dict(zip(emergency_columns, row)))
            save_json_file(EMERGENCY_CONFIG_JSON_FILE, emergency_data)
        else:
            save_json_file(EMERGENCY_CONFIG_JSON_FILE, [])
        conn.close()
        logger.debug("Emergency config synced to JSON successfully.")
    except Exception as e:
        logger.error(f"Error syncing emergency config to JSON: {e}")

def get_emergency_config() -> Dict[str, Any]:
    """Get current emergency config"""
    logger.debug("Getting emergency config")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute(
            """SELECT message_text, media_type, media_file_id, media_caption, created_at 
               FROM emergency_config ORDER BY id DESC LIMIT 1"""
        )
        result = cursor.fetchone()
        if result:
            config = {
                'message_text': result[0],
                'media_type': result[1],
                'media_file_id': result[2],
                'media_caption': result[3],
                'created_at': result[4]
            }
            logger.debug("Emergency config found")
            return config
        else:
            logger.debug("No emergency config found")
            return {}
    except sqlite3.OperationalError as e:
        if "no such table: emergency_config" in str(e):
            logger.warning(f"emergency_config table not found. Initializing database tables...")
            if conn: conn.close()
            create_database_table()
            # Retry after creating tables
            return get_emergency_config()
        else:
            logger.error(f"SQLite OperationalError getting emergency config: {e}", exc_info=True)
            return {}
    except sqlite3.Error as e:
        logger.error(f"Error getting emergency config: {e}", exc_info=True)
        return {}
    finally:
        if conn: conn.close()


# --- Subscription Plans Management ---
def get_all_active_plans() -> List[Dict[str, Any]]:
    """Get all active subscription plans"""
    logger.debug("Getting all active subscription plans")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute(
            """SELECT plan_id, plan_name, description, days, volume_gb, price_toman 
               FROM subscription_plans WHERE is_active = 1 ORDER BY price_toman"""
        )
        plans = []
        for row in cursor.fetchall():
            plans.append({
                'plan_id': row[0],
                'plan_name': row[1],
                'description': row[2],
                'days': row[3],
                'volume_gb': row[4],
                'price_toman': row[5]
            })
        logger.debug(f"Found {len(plans)} active subscription plans")
        return plans
    except sqlite3.OperationalError as e:
        if "no such table: subscription_plans" in str(e):
            logger.warning(f"subscription_plans table not found. Initializing database tables...")
            if conn: conn.close()
            create_database_table()
            # Retry after creating tables
            return get_all_active_plans()
        else:
            logger.error(f"SQLite OperationalError getting active plans: {e}", exc_info=True)
            return []
    except sqlite3.Error as e:
        logger.error(f"Error getting active plans: {e}", exc_info=True)
        return []
    finally:
        if conn: conn.close()


def get_all_plans() -> List[Dict[str, Any]]:
    """Get all subscription plans (active and inactive)"""
    logger.debug("Getting all subscription plans")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute(
            """SELECT plan_id, plan_name, description, days, volume_gb, price_toman, is_active 
               FROM subscription_plans ORDER BY price_toman"""
        )
        plans = []
        for row in cursor.fetchall():
            plans.append({
                'plan_id': row[0],
                'plan_name': row[1],
                'description': row[2],
                'days': row[3],
                'volume_gb': row[4],
                'price_toman': row[5],
                'is_active': bool(row[6])
            })
        logger.debug(f"Found {len(plans)} total subscription plans")
        return plans
    except sqlite3.Error as e:
        logger.error(f"Error getting all plans: {e}", exc_info=True)
        return []
    finally:
        if conn: conn.close()


def get_plan_by_id(plan_id: str) -> Dict[str, Any]:
    """Get a specific plan by ID"""
    logger.debug(f"Getting plan by ID: {plan_id}")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute(
            """SELECT plan_id, plan_name, description, days, volume_gb, price_toman, is_active 
               FROM subscription_plans WHERE plan_id = ?""",
            (plan_id,)
        )
        result = cursor.fetchone()
        if result:
            plan = {
                'plan_id': result[0],
                'plan_name': result[1],
                'description': result[2],
                'days': result[3],
                'volume_gb': result[4],
                'price_toman': result[5],
                'is_active': bool(result[6])
            }
            logger.debug(f"Plan found: {plan_id}")
            return plan
        else:
            logger.debug(f"Plan not found: {plan_id}")
            return {}
    except sqlite3.OperationalError as e:
        if "no such table: subscription_plans" in str(e):
            logger.warning(f"subscription_plans table not found. Initializing database tables...")
            if conn: conn.close()
            create_database_table()
            # Retry after creating tables
            return get_plan_by_id(plan_id)
        else:
            logger.error(f"SQLite OperationalError getting plan {plan_id}: {e}", exc_info=True)
            return {}
    except sqlite3.Error as e:
        logger.error(f"Error getting plan {plan_id}: {e}", exc_info=True)
        return {}
    finally:
        if conn: conn.close()


def save_plan(plan_id: str, plan_name: str, description: str, days: int, 
              volume_gb: float, price_toman: int, is_active: bool = True) -> bool:
    """Save or update a subscription plan"""
    logger.info(f"Saving plan: {plan_id}")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        # Check if plan exists
        cursor.execute("SELECT plan_id FROM subscription_plans WHERE plan_id = ?", (plan_id,))
        exists = cursor.fetchone() is not None
        
        if exists:
            # Update existing plan
            cursor.execute(
                """UPDATE subscription_plans 
                   SET plan_name = ?, description = ?, days = ?, volume_gb = ?, 
                       price_toman = ?, is_active = ?, updated_at = ?
                   WHERE plan_id = ?""",
                (plan_name, description, days, volume_gb, price_toman, 
                 int(is_active), datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), plan_id)
            )
            logger.info(f"Updated existing plan: {plan_id}")
        else:
            # Insert new plan
            cursor.execute(
                """INSERT INTO subscription_plans 
                   (plan_id, plan_name, description, days, volume_gb, price_toman, is_active) 
                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                (plan_id, plan_name, description, days, volume_gb, price_toman, int(is_active))
            )
            logger.info(f"Created new plan: {plan_id}")
        
        conn.commit()
        
        # همگام‌سازی با JSON
        _sync_subscription_plans_to_json()
        
        return True
    except sqlite3.Error as e:
        logger.error(f"Error saving plan {plan_id}: {e}", exc_info=True)
        return False
    finally:
        if conn: conn.close()


def update_plan_field(plan_id: str, field_name: str, new_value) -> bool:
    """Update a specific field of a plan"""
    logger.info(f"Updating plan {plan_id} field {field_name} to {new_value}")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        # Validate field name to prevent SQL injection
        valid_fields = ['plan_name', 'description', 'days', 'volume_gb', 'price_toman']
        if field_name not in valid_fields:
            logger.error(f"Invalid field name: {field_name}")
            return False
        
        # Update the specific field
        query = f"UPDATE subscription_plans SET {field_name} = ?, updated_at = ? WHERE plan_id = ?"
        cursor.execute(query, (new_value, datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), plan_id))
        conn.commit()
        
        if cursor.rowcount > 0:
            logger.info(f"Successfully updated plan {plan_id} field {field_name}")
            
            # همگام‌سازی با JSON
            _sync_subscription_plans_to_json()
            
            return True
        else:
            logger.warning(f"Plan not found for update: {plan_id}")
            return False
    except sqlite3.Error as e:
        logger.error(f"Error updating plan {plan_id} field {field_name}: {e}", exc_info=True)
        return False
    finally:
        if conn: conn.close()


def delete_plan(plan_id: str) -> bool:
    """Delete a subscription plan"""
    logger.info(f"Deleting plan: {plan_id}")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM subscription_plans WHERE plan_id = ?", (plan_id,))
        conn.commit()
        
        if cursor.rowcount > 0:
            logger.info(f"Plan deleted successfully: {plan_id}")
            
            # همگام‌سازی با JSON
            _sync_subscription_plans_to_json()
            
            return True
        else:
            logger.warning(f"Plan not found for deletion: {plan_id}")
            return False
    except sqlite3.Error as e:
        logger.error(f"Error deleting plan {plan_id}: {e}", exc_info=True)
        return False
    finally:
        if conn: conn.close()


def toggle_plan_status(plan_id: str) -> bool:
    """Toggle the active status of a plan"""
    logger.info(f"Toggling plan status: {plan_id}")
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute(
            """UPDATE subscription_plans 
               SET is_active = NOT is_active, updated_at = ?
               WHERE plan_id = ?""",
            (datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), plan_id)
        )
        conn.commit()
        
        if cursor.rowcount > 0:
            logger.info(f"Plan status toggled successfully: {plan_id}")
            
            # همگام‌سازی با JSON
            _sync_subscription_plans_to_json()
            
            return True
        else:
            logger.warning(f"Plan not found for status toggle: {plan_id}")
            return False
    except sqlite3.Error as e:
        logger.error(f"Error toggling plan status {plan_id}: {e}", exc_info=True)
        return False
    finally:
        if conn: conn.close()

def _sync_subscription_plans_to_json():
    """همگام‌سازی جدول subscription_plans با JSON"""
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM subscription_plans")
        if cursor.description:
            plans_columns = [desc[0] for desc in cursor.description]
            plans_data = []
            for row in cursor.fetchall():
                plans_data.append(dict(zip(plans_columns, row)))
            save_json_file(SUBSCRIPTION_PLANS_JSON_FILE, plans_data)
        else:
            save_json_file(SUBSCRIPTION_PLANS_JSON_FILE, [])
        conn.close()
        logger.debug("Subscription plans synced to JSON successfully.")
    except Exception as e:
        logger.error(f"Error syncing subscription plans to JSON: {e}")

# --- JSON Read Helper Functions ---
def get_users_from_json() -> List[Dict[str, Any]]:
    """دریافت تمام کاربران از فایل JSON"""
    return load_json_file(USERS_JSON_FILE, [])

def get_panel_users_from_json() -> List[Dict[str, Any]]:
    """دریافت کاربران پنل از فایل JSON"""
    return load_json_file(PANEL_USERS_JSON_FILE, [])

def get_deleted_users_from_json() -> List[Dict[str, Any]]:
    """دریافت کاربران حذف شده از فایل JSON"""
    return load_json_file(DELETED_USERS_JSON_FILE, [])

def get_income_transactions_from_json() -> List[Dict[str, Any]]:
    """دریافت تراکنش‌های درآمد از فایل JSON"""
    return load_json_file(INCOME_TRANSACTIONS_JSON_FILE, [])

def get_bot_settings_from_json() -> Dict[str, Any]:
    """دریافت تنظیمات ربات از فایل JSON"""
    return load_json_file(BOT_SETTINGS_JSON_FILE, {})

def get_emergency_config_from_json() -> List[Dict[str, Any]]:
    """دریافت تنظیمات اضطراری از فایل JSON"""
    return load_json_file(EMERGENCY_CONFIG_JSON_FILE, [])

def get_subscription_plans_from_json() -> List[Dict[str, Any]]:
    """دریافت پلن‌های اشتراک از فایل JSON"""
    return load_json_file(SUBSCRIPTION_PLANS_JSON_FILE, [])

# --- JSON Maintenance Functions ---
def force_sync_all_to_json():
    """اجبار به همگام‌سازی کامل تمام جداول با JSON"""
    logger.info("Starting forced synchronization of all tables to JSON")
    try:
        sync_sqlite_to_json()
        logger.info("Forced synchronization completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error in forced synchronization: {e}")
        return False

def backup_json_files():
    """پشتیبان‌گیری از فایل‌های JSON"""
    logger.info("Creating backup of JSON files")
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = os.path.join(JSON_DIR, f"backup_{timestamp}")
    
    try:
        ensure_json_directory()
        os.makedirs(backup_dir, exist_ok=True)
        
        json_files = [
            USERS_JSON_FILE,
            PANEL_USERS_JSON_FILE,
            DELETED_USERS_JSON_FILE,
            INCOME_TRANSACTIONS_JSON_FILE,
            BOT_SETTINGS_JSON_FILE,
            EMERGENCY_CONFIG_JSON_FILE,
            SUBSCRIPTION_PLANS_JSON_FILE
        ]
        
        backed_up_count = 0
        for json_file in json_files:
            if os.path.exists(json_file):
                filename = os.path.basename(json_file)
                backup_path = os.path.join(backup_dir, filename)
                shutil.copy2(json_file, backup_path)
                backed_up_count += 1
        
        logger.info(f"Successfully backed up {backed_up_count} JSON files to {backup_dir}")
        return True, backup_dir, backed_up_count
    except Exception as e:
        logger.error(f"Error creating JSON backup: {e}")
        return False, None, 0

def get_json_database_stats():
    """دریافت آمار فایل‌های JSON"""
    stats = {}
    json_files = {
        'users': USERS_JSON_FILE,
        'panel_users': PANEL_USERS_JSON_FILE,
        'deleted_users': DELETED_USERS_JSON_FILE,
        'income_transactions': INCOME_TRANSACTIONS_JSON_FILE,
        'bot_settings': BOT_SETTINGS_JSON_FILE,
        'emergency_config': EMERGENCY_CONFIG_JSON_FILE,
        'subscription_plans': SUBSCRIPTION_PLANS_JSON_FILE
    }
    
    for name, file_path in json_files.items():
        try:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        record_count = len(data)
                    elif isinstance(data, dict):
                        record_count = len(data.keys())
                    else:
                        record_count = 1
                
                stats[name] = {
                    'exists': True,
                    'size_bytes': file_size,
                    'size_kb': round(file_size / 1024, 2),
                    'record_count': record_count,
                    'last_modified': datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')
                }
            else:
                stats[name] = {
                    'exists': False,
                    'size_bytes': 0,
                    'size_kb': 0,
                    'record_count': 0,
                    'last_modified': None
                }
        except Exception as e:
            logger.error(f"Error getting stats for {name}: {e}")
            stats[name] = {
                'exists': False,
                'error': str(e)
            }
    
    return stats

async def get_comprehensive_income_stats() -> dict:
    """Get comprehensive income and user statistics"""
    logger.debug("Calculating comprehensive income statistics")
    
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        today = datetime.date.today()
        first_day_of_month = today.replace(day=1)
        first_day_of_year = today.replace(month=1, day=1)
        six_months_ago = today - datetime.timedelta(days=180)
        seven_days_from_now = today + datetime.timedelta(days=7)
        
        stats = {
            'daily_income': 0,
            'monthly_income': 0,
            'six_month_income': 0,
            'yearly_income': 0,
            'renewals_this_month': 0,
            'new_purchases_this_month': 0,
            'due_for_renewal': 0,
            'expiring_soon': 0,
            'total_active_users': 0,
            'projected_monthly_income': 0
        }

        income_records: list = []  # Will be filled from JSON
        
        # Exclude trial users from active count (names starting with 'Trial_')
        try:
            cursor.execute("SELECT COUNT(*) FROM panelusers WHERE enable = 1 AND name NOT LIKE 'Trial_%'")
            result = cursor.fetchone()
            stats['total_active_users'] = result[0] if result else 0
        except Exception as e:
            logger.warning(f"Could not get active users count: {e}")
        
        # Get all active plans for income calculations
        all_plans = get_all_plans()
        plan_prices = {plan['plan_id']: plan['price_toman'] for plan in all_plans}
        
        # --- New: Calculate income using persisted JSON transactions to survive restarts ---
        try:
            income_records = get_income_transactions_from_json()

            for record in income_records:
                plan_id = record.get('plan_id')
                t_date_str = record.get('transaction_date')
                if not (plan_id and t_date_str):
                    continue
                try:
                    t_date = datetime.datetime.strptime(t_date_str, "%Y-%m-%d").date()
                except ValueError:
                    continue

                if plan_id not in plan_prices:
                    continue

                price = plan_prices[plan_id]

                # Daily
                if t_date == today:
                    stats['daily_income'] += price

                # Monthly
                if t_date >= first_day_of_month:
                    stats['monthly_income'] += price

                # Six months
                if t_date >= six_months_ago:
                    stats['six_month_income'] += price

                # Yearly
                if t_date >= first_day_of_year:
                    stats['yearly_income'] += price

            logger.debug("Income calculated from JSON transactions")
        except Exception as e:
            logger.warning(f"Could not calculate income from JSON transactions: {e}")
        
        # --- New: Purchases/Renewals counts via JSON ---
        try:
            renew_ids, new_ids = set(), set()
            for record in income_records:
                t_date_str = record.get('transaction_date')
                if not t_date_str:
                    continue
                try:
                    t_date = datetime.datetime.strptime(t_date_str, "%Y-%m-%d").date()
                except ValueError:
                    continue

                if t_date < first_day_of_month:
                    continue

                t_type = record.get('transaction_type')
                user_tg_id = record.get('user_tg_id')
                if not user_tg_id:
                    continue

                if t_type == 'renew':
                    renew_ids.add(user_tg_id)
                elif t_type in ('buy', 'upgrade_from_trial'):
                    new_ids.add(user_tg_id)

            stats['renewals_this_month'] = len(renew_ids)
            stats['new_purchases_this_month'] = len(new_ids)
        except Exception as e:
            logger.warning(f"Could not calculate user statistics from JSON: {e}")
        
        # Get users from panel for expiry calculations
        try:
            from api_hiddify import fetch_panel_users
            success, panel_users = await fetch_panel_users()
            
            if success:
                import json
                users_data = panel_users if isinstance(panel_users, list) else json.loads(panel_users)
                
                due_for_renewal_count = 0
                expiring_soon_count = 0
                projected_income = 0
                
                for user in users_data:
                    try:
                        if not user.get('enable', False):
                            continue

                        # Skip trial users (name starts with 'Trial_')
                        if str(user.get('name', '')).startswith('Trial_'):
                            continue
                            
                        start_date_str = user.get('start_date')
                        package_days = user.get('package_days', 0)
                        
                        if start_date_str and package_days:
                            start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d').date()
                            expiry_date = start_date + datetime.timedelta(days=package_days)
                            
                            # Check if expiring within 7 days
                            if today <= expiry_date <= seven_days_from_now:
                                expiring_soon_count += 1
                            
                            # Check if due for renewal this month (expires this month)
                            if (expiry_date.year == today.year and 
                                expiry_date.month == today.month):
                                due_for_renewal_count += 1
                                
                                # Try to estimate renewal income
                                usage_gb = user.get('usage_limit_GB', 0)
                                # Find similar plan for projection
                                for plan in all_plans:
                                    if (abs(plan['days'] - package_days) <= 5 and 
                                        abs(plan['volume_gb'] - usage_gb) <= 10):
                                        projected_income += plan['price_toman']
                                        break
                    except Exception as e:
                        logger.debug(f"Error processing user {user.get('name', 'Unknown')}: {e}")
                        continue
                
                stats['due_for_renewal'] = due_for_renewal_count
                stats['expiring_soon'] = expiring_soon_count
                stats['projected_monthly_income'] = projected_income
                
        except Exception as e:
            logger.warning(f"Could not fetch panel data for expiry calculations: {e}")
        
        logger.info(f"Income statistics calculated: {stats}")
        return stats
        
    except Exception as e:
        logger.error(f"Error calculating comprehensive income stats: {e}", exc_info=True)
        return {
            'daily_income': 0,
            'monthly_income': 0,
            'six_month_income': 0,
            'yearly_income': 0,
            'renewals_this_month': 0,
            'new_purchases_this_month': 0,
            'due_for_renewal': 0,
            'expiring_soon': 0,
            'total_active_users': 0,
            'projected_monthly_income': 0
        }
    finally:
        if conn:
            conn.close()