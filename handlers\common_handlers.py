# hiddy_bot/handlers/common_handlers.py

import logging  # type: ignore
import jdatetime  # type: ignore
from aiogram import Router, Bo<PERSON>, F  # type: ignore
from aiogram.filters import Command  # type: ignore
from aiogram.fsm.context import FSMContext  # type: ignore
from aiogram.types import Message  # type: ignore
from aiogram.utils.markdown import html_decoration  # type: ignore

from messages import Messages  # type: ignore
from config import ADMIN_USER_ID, MEMBERSHIP_CHANNEL_USERNAME  # type: ignore
from database import get_user_from_database, update_or_add_user  # type: ignore
from states import GetUserInfoState  # type: ignore
from keyboards import create_main_menu, create_user_initial_menu, create_user_regular_menu, create_membership_keyboard  # type: ignore
from handlers.user_handlers import send_user_panel_info  # type: ignore
from api_hiddify import fetch_panel_users # Import the function to fetch all users

router = Router()
logger = logging.getLogger(__name__)


# --- New function to find and reconnect user from panel ---
async def reconnect_user_by_telegram_id(user_id: int, full_name: str, username: str) -> tuple | None:
    """
    Checks if a user with the given telegram_id exists in the Hiddify panel.
    If found, it recreates the user in the local database and returns the user data.
    """
    logger.info(f"User {user_id} not found in local DB. Attempting to find user in Hiddify panel by telegram_id.")
    
    success, panel_users = await fetch_panel_users()
    if not success or not isinstance(panel_users, list):
        logger.error(f"Failed to fetch or parse panel users for reconnection check. API Error: {panel_users}")
        return None

    found_user = None
    for panel_user in panel_users:
        if panel_user.get("telegram_id") and int(panel_user.get("telegram_id")) == user_id:
            found_user = panel_user
            break
    
    if found_user:
        user_uuid = found_user.get("uuid")
        logger.info(f"Found user {user_id} in panel with UUID {user_uuid}. Recreating local DB entry.")
        
        join_time = jdatetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
        username_str = f"@{username}" if username else "NO_ID"
        
        # Add user to local DB
        update_or_add_user(full_name, username_str, user_id, join_time, user_uuid)
        
        # Return the newly created user data from DB
        return get_user_from_database(user_id)
        
    logger.info(f"User {user_id} not found in Hiddify panel by telegram_id. They are a genuinely new user.")
    return None


# --- Utility for Membership Check ---
async def check_membership(bot: Bot, user_id: int) -> bool:
    logger.debug(f"Checking membership for user {user_id} in channel '{MEMBERSHIP_CHANNEL_USERNAME}'.")
    if not MEMBERSHIP_CHANNEL_USERNAME:
        logger.warning("Membership check skipped: MEMBERSHIP_CHANNEL_USERNAME not set in config.")
        return True
    try:
        member = await bot.get_chat_member(chat_id=MEMBERSHIP_CHANNEL_USERNAME, user_id=user_id)
        is_member = member.status not in ["left", "kicked"]
        logger.debug(f"User {user_id} membership status is '{member.status}'. Is member: {is_member}.")
        return is_member
    except Exception as e:
        logger.error(f"Could not check membership for {user_id} in {MEMBERSHIP_CHANNEL_USERNAME}: {e}", exc_info=True)
        return False

# --- /help Command Handler ---
@router.message(Command(commands=["help"]))
async def help_command_handler(message: Message, state: FSMContext):
    user = message.from_user
    logger.info(f"Received /help command from user: {user.full_name} (ID: {user.id})")

    # Clear any active state
    await state.clear()

    # Check if user is authenticated
    user_data = get_user_from_database(user.id)
    is_authenticated = user_data and user_data[4]

    # Create help message
    help_text = "🤖 <b>راهنمای استفاده از ربات MBN VPN</b>\n\n"

    if user.id == ADMIN_USER_ID:
        help_text += "👑 <b>شما ادمین ربات هستید</b>\n\n"
        help_text += "📋 <b>دستورات ادمین:</b>\n"
        help_text += "• /start - منوی اصلی ادمین\n"
        help_text += "• /help - نمایش این راهنما\n"
        help_text += "• /status - وضعیت سرور\n"
        help_text += "• /cancel - لغو عملیات جاری\n"
        help_text += "• /test_error - تست سیستم خطایابی\n"
        help_text += "• /check_renewal - بررسی روش تمدید فعلی\n\n"
        help_text += "🔧 <b>امکانات ادمین:</b>\n"
        help_text += "• مدیریت کاربران و اشتراک‌ها\n"
        help_text += "• مشاهده آمار درآمد\n"
        help_text += "• ارسال پیام به کاربران\n"
        help_text += "• مدیریت پلن‌ها و تنظیمات\n"
        help_text += "• دریافت بکاپ و گزارشات\n"
        keyboard = create_main_menu()
    elif is_authenticated:
        help_text += "✅ <b>شما کاربر احراز هویت شده هستید</b>\n\n"
        help_text += "📋 <b>دستورات موجود:</b>\n"
        help_text += "• /start - منوی اصلی\n"
        help_text += "• /help - نمایش این راهنما\n"
        help_text += "• /status - وضعیت سرور\n"
        help_text += "• /cancel - لغو عملیات جاری\n\n"
        help_text += "🔧 <b>امکانات شما:</b>\n"
        help_text += "• مشاهده اطلاعات حساب کاربری\n"
        help_text += "• تمدید اشتراک\n"
        help_text += "• دانلود اپلیکیشن‌ها\n"
        help_text += "• بررسی وضعیت سرور\n"
        help_text += "• دسترسی به پشتیبانی\n"
        keyboard = create_user_regular_menu(user.id)
    else:
        help_text += "👋 <b>خوش آمدید!</b>\n\n"
        help_text += "📋 <b>دستورات موجود:</b>\n"
        help_text += "• /start - شروع کار با ربات\n"
        help_text += "• /help - نمایش این راهنما\n"
        help_text += "• /status - وضعیت سرور\n\n"
        help_text += "🔧 <b>امکانات موجود:</b>\n"
        help_text += "• خرید اشتراک جدید\n"
        help_text += "• دریافت اشتراک آزمایشی\n"
        help_text += "• دانلود اپلیکیشن‌ها\n"
        help_text += "• دسترسی به پشتیبانی\n\n"
        help_text += "💡 <b>برای شروع /start را بزنید</b>"
        keyboard = create_user_initial_menu()

    await message.answer(help_text, reply_markup=keyboard, parse_mode="HTML")

# --- /status Command Handler ---
@router.message(Command(commands=["status"]))
async def status_command_handler(message: Message, state: FSMContext):
    user = message.from_user
    logger.info(f"Received /status command from user: {user.full_name} (ID: {user.id})")

    # Clear any active state
    await state.clear()

    # Check if user is admin
    if user.id == ADMIN_USER_ID:
        # For admin, show detailed server status (like admin button)
        from handlers.admin_handlers import handle_server_stats_request
        await handle_server_stats_request(message)
    else:
        # For regular users, show simple server status (like user button)
        from handlers.user_handlers import handle_server_status_request
        await handle_server_status_request(message)

# --- /start Command Handler ---
@router.message(Command(commands=["start"]))
async def start_command_handler(message: Message, state: FSMContext, bot: Bot):
    user = message.from_user
    logger.info(f"Received /start command from user: {user.full_name} (ID: {user.id})")
    
    # Check for deep link parameter
    deep_link_param = None
    if message.text:
        # Check for traditional deep link format: /start parameter
        if " " in message.text:
            parts = message.text.split(" ", 1)
            if len(parts) > 1:
                deep_link_param = parts[1]
                logger.info(f"Traditional deep link parameter detected: {deep_link_param}")
        # Check for UUID-based command format: /startUUID
        elif message.text.startswith("/start") and len(message.text) > 6:
            potential_uuid = message.text[6:]  # Remove "/start" prefix
            # Basic UUID format validation (36 characters with hyphens)
            if len(potential_uuid) == 36 and potential_uuid.count('-') == 4:
                deep_link_param = f"uuid_{potential_uuid}"
                logger.info(f"UUID-based command detected: /start{potential_uuid} -> {deep_link_param}")
    
    logger.debug(f"Clearing state for user {user.id} at the beginning of /start.")
    await state.clear()

    if not await check_membership(bot, user.id):
        logger.warning(f"User {user.id} is not a member. Blocking access and showing join message.")
        
        # Store deep link parameter in FSM state for after membership
        if deep_link_param:
            logger.info(f"Storing deep link parameter '{deep_link_param}' for user {user.id} until membership is confirmed")
            await state.update_data(pending_deep_link=deep_link_param)
        
        # Check if it's a UUID deep link to provide better user experience
        if deep_link_param and deep_link_param.startswith("uuid_"):
            target_uuid = deep_link_param[5:]  # Remove "uuid_" prefix
            logger.info(f"User {user.id} has UUID deep link. Fetching account name for better user experience.")
            
            # Try to get account name from API
            account_name = None
            try:
                from api_hiddify import fetch_user_details_api
                import json
                success, details_resp, _ = await fetch_user_details_api(target_uuid)
                if success:
                    user_dict = json.loads(details_resp)
                    account_name = user_dict.get('name', target_uuid)
                    logger.debug(f"Found account name: {account_name} for UUID {target_uuid}")
                else:
                    logger.warning(f"Could not fetch account details for UUID {target_uuid}")
                    account_name = target_uuid
            except Exception as e:
                logger.error(f"Error fetching account name for UUID {target_uuid}: {e}")
                account_name = target_uuid
            
            # Create improved membership message with direct command
            membership_message = (
                f"🔐 <b>احراز هویت مورد نیاز</b>\n\n"
                f"👋 سلام! برای دسترسی به اکانت <b>{account_name}</b>، ابتدا باید در کانال ما عضو شوید:\n\n"
                f"📢 <b>کانال:</b> {MEMBERSHIP_CHANNEL_USERNAME}\n\n"
                f"📝 <b>مراحل احراز هویت:</b>\n"
                f"1️⃣ روی دکمه زیر کلیک کنید و در کانال عضو شوید\n"
                f"2️⃣ بعد از عضویت، دستور زیر را کپی و ارسال کنید:\n\n"
                f"<code>/start{target_uuid}</code>\n\n"
                f"✨ <i>با ارسال دستور بالا، مستقیماً به اکانت شما دسترسی پیدا می‌کنید!</i>"
            )
        else:
            # Regular membership message for other cases
            membership_message = Messages.User.Membership.REQUIRED_MESSAGE(channel_username=MEMBERSHIP_CHANNEL_USERNAME)
            if deep_link_param:
                membership_message += f"\n\n💡 <i>بعد از عضویت، درخواست شما ({deep_link_param}) پردازش خواهد شد.</i>"
        
        await message.reply(
            membership_message,
            reply_markup=create_membership_keyboard(MEMBERSHIP_CHANNEL_USERNAME),
            parse_mode="HTML"
        )
        return

    # Admin flow
    if user.id == ADMIN_USER_ID:
        logger.info(f"User {user.id} identified as ADMIN. Sending admin greeting and menu.")
        await message.reply(
            Messages.Admin.Menu.GREETING(username=html_decoration.quote(user.full_name)),
            reply_markup=create_main_menu(user.id)
        )
        return

    # Check for pending deep link from state (for users who joined after being shown membership requirement)
    state_data = await state.get_data()
    pending_deep_link = state_data.get('pending_deep_link')
    
    # Use either current deep_link_param or pending one from state
    active_deep_link = deep_link_param or pending_deep_link
    
    if pending_deep_link:
        logger.info(f"Found pending deep link '{pending_deep_link}' for user {user.id} after membership confirmation")
        # Clear the pending deep link from state
        await state.update_data(pending_deep_link=None)
    
    # Process deep link if present (e.g., start=uuid_xxxxxxxx)
    if active_deep_link and active_deep_link.startswith("uuid_"):
        target_uuid = active_deep_link[5:]  # Remove "uuid_" prefix
        logger.info(f"Processing deep link UUID authentication for user {user.id} with UUID: {target_uuid}")
        
        # Import needed functions
        from api_hiddify import fetch_user_details_api, update_user_api
        from utils import extract_uuid_from_input
        
        # Validate UUID format
        extracted_uuid = await extract_uuid_from_input(target_uuid)
        if extracted_uuid:
            # Verify UUID exists on panel
            success, _, _ = await fetch_user_details_api(extracted_uuid)
            if success:
                logger.info(f"Deep link UUID {extracted_uuid} is valid.")
                
                # Check if user already has this UUID
                user_data_db = get_user_from_database(user.id)
                if user_data_db and user_data_db[4] == extracted_uuid:
                    # User already has this UUID, just show panel info
                    logger.info(f"User {user.id} already has UUID {extracted_uuid}. Showing panel info directly.")
                    processing_msg = await message.answer("✅ اطلاعات اکانت شما در حال بارگذاری...")
                    panel_sent = await send_user_panel_info(message, bot, user_data_db, False, processing_msg.message_id)
                    
                    if panel_sent:
                        await message.answer(Messages.User.Panel.MENU_AFTER_INFO, reply_markup=create_user_regular_menu(user.id))
                    else:
                        logger.error(f"Panel info sending failed for existing user {user.id}.")
                        await message.answer("خطایی در نمایش اطلاعات رخ داد. لطفا دوباره /start را بزنید.", reply_markup=create_user_regular_menu(user.id))
                    return
                
                # User doesn't have this UUID, try to authenticate
                join_time = jdatetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
                username_str = f"@{user.username}" if user.username else "NO_ID"
                auth_result = update_or_add_user(user.full_name, username_str, user.id, join_time, extracted_uuid, is_trial=0)
                
                if auth_result is None:
                    # UUID is already assigned to another user
                    logger.error(f"Deep link authentication failed for user {user.id} with UUID {extracted_uuid}. UUID is already assigned to another user.")
                    await message.answer(
                        "⚠️ این لینک قبلاً توسط کاربر دیگری استفاده شده است.\n\n"
                        "اگر این لینک متعلق به شما است، لطفاً با پشتیبانی تماس بگیرید.",
                        reply_markup=create_user_initial_menu()
                    )
                    return
                
                logger.debug(f"Attempting to set telegram_id={user.id} on panel for UUID {extracted_uuid}.")
                await update_user_api(extracted_uuid, {"telegram_id": user.id})
                
                processing_msg = await message.answer("✅ احراز هویت موفق! در حال دریافت اطلاعات...")
                updated_user_data_db = get_user_from_database(user.id)
                panel_sent = await send_user_panel_info(message, bot, updated_user_data_db, True, processing_msg.message_id)
                
                if panel_sent:
                    await message.answer(Messages.User.Panel.MENU_AFTER_INFO, reply_markup=create_user_regular_menu(user.id))
                else:
                    logger.error(f"Panel info sending failed for user {user.id} even after deep link auth.")
                    await message.answer("احراز هویت موفق بود اما خطایی در نمایش اطلاعات رخ داد. لطفا دوباره /start را بزنید.", reply_markup=create_user_regular_menu(user.id))
                return
            else:
                logger.warning(f"Deep link UUID {extracted_uuid} not found on panel for user {user.id}.")
                await message.answer("⚠️ لینک نامعتبر است یا منقضی شده است.", reply_markup=create_user_initial_menu())
                return
    
    # Handle special deep link actions
    elif active_deep_link == "show_apps":
        logger.info(f"Processing show_apps deep link for user {user.id}")
        
        # Import the function for sending app download guide
        from handlers.user_handlers import send_app_download_links
        
        # Send the app download guide
        await send_app_download_links(message)
        
        # Also show appropriate menu
        user_data = get_user_from_database(user.id)
        is_authenticated = user_data and user_data[4]
        if is_authenticated:
            await message.answer("منوی شما:", reply_markup=create_user_regular_menu(user.id))
        else:
            await message.answer("منوی اصلی:", reply_markup=create_user_initial_menu())
        return

    # Regular user flow
    logger.debug(f"Processing regular user {user.id}.")
    # Check if user is in local database and has a UUID
    user_data_db = get_user_from_database(user.id)
    is_authenticated = user_data_db and user_data_db[4]

    if is_authenticated:
        logger.info(f"User {user.id} is authenticated with UUID {user_data_db[4]}. Sending panel info.")
        try:
            processing_msg = await message.answer(Messages.User.Panel.FETCHING_LATEST_INFO)
            panel_sent = await send_user_panel_info(bot=bot, user_data_db=user_data_db, message=message, is_first_time_auth=False, processing_msg_id=processing_msg.message_id)
            if panel_sent:
                logger.debug(f"Panel info sent successfully to user {user.id}. Showing regular menu.")
                await message.answer(Messages.User.Panel.MENU_AFTER_INFO, reply_markup=create_user_regular_menu(user.id))
        except Exception as e:
            logger.error(f"An unexpected error occurred in /start for authenticated user {user.id}: {e}", exc_info=True)
            await message.answer(Messages.General.INTERNAL_ERROR_MESSAGE)
    else: # New or unauthenticated user
        logger.info(f"User {user.id} is new or unauthenticated. Checking panel before greeting.")
        
        # Try to find user in the panel by telegram_id if they don't exist locally
        reconnected_user = await reconnect_user_by_telegram_id(user.id, user.full_name, user.username)

        if reconnected_user:
            assert reconnected_user is not None  # Explicitly tell type checker it's not None
            logger.info(f"User {user.id} was reconnected from panel. Sending panel info.")
            processing_msg = await message.answer(Messages.User.Panel.FETCHING_LATEST_INFO)
            try:
                # FIX: Correct argument order for send_user_panel_info
                panel_sent = await send_user_panel_info(
                    bot=bot,
                    user_data_db=reconnected_user,
                    message=message,
                    is_first_time_auth=False,
                    processing_msg_id=processing_msg.message_id
                )
                if not panel_sent:
                    logger.error(f"send_user_panel_info returned False for reconnected user {user.id}.")
            except Exception as e:
                logger.error(f"An unexpected error occurred in /start for reconnected user {user.id}: {e}", exc_info=True)
                await message.answer(Messages.General.INTERNAL_ERROR_MESSAGE)
            return

        # If user was not reconnected, proceed as a new user
        logger.info(f"User {user.id} is confirmed as new or unauthenticated. Showing initial greeting.")
        if not user_data_db:
            logger.debug(f"User {user.id} not found in DB. Creating new record.")
            join_time = jdatetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
            username_str = f"@{user.username}" if user.username else "NO_ID"
            update_or_add_user(user.full_name, username_str, user.id, join_time)
        
        await message.reply(
            Messages.User.Initial.GREETING_UNAUTHENTICATED(username=html_decoration.quote(user.full_name)),
            reply_markup=create_user_initial_menu()
        )
        await state.set_state(GetUserInfoState.waiting_for_user_info)
        logger.debug(f"Set state to GetUserInfoState.waiting_for_user_info for user {user.id}.")