import os

# Define the base directory
base_dir = "hiddy_bot"

# Define the directory and file structure
structure = {
    "hiddy_bot": [
        ".env",
        "messages.py",
        "config.py",
        "states.py",
        "database.py",
        "api_hiddify.py",
        "ssh_utils.py",
        "keyboards.py",
        "utils.py",
        "scheduler_tasks.py",
        "bot.py"
    ],
    "hiddy_bot/handlers": [
        "__init__.py",
        "admin_handlers.py",
        "user_handlers.py",
        "common_handlers.py"
    ]
}

def create_project_structure(base_path, project_structure):
    """
    Creates a directory and file structure for the project.
    """
    # Create the base directory if it doesn't exist
    if not os.path.exists(base_path):
        os.makedirs(base_path)
        print(f"Created directory: {base_path}")

    # Iterate through the structure to create directories and files
    for directory, files in project_structure.items():
        # Create subdirectories if they don't exist
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")

        # Create empty files within the directories
        for file_name in files:
            file_path = os.path.join(directory, file_name)
            if not os.path.exists(file_path):
                with open(file_path, 'w') as f:
                    pass  # Create an empty file
                print(f"  Created file: {file_path}")
            else:
                print(f"  File already exists: {file_path}")


# --- Main Execution ---
if __name__ == "__main__":
    print("Setting up the project structure for hiddy_bot...")
    create_project_structure(base_dir, structure)
    print("\nProject structure created successfully!")