# hiddy_bot/filters.py

import logging  # type: ignore
from aiogram.filters import Filter  # type: ignore
from aiogram.fsm.context import FSMContext  # type: ignore
from aiogram.types import Message  # type: ignore

logger = logging.getLogger(__name__)

class IsInState(Filter):
    """
    Custom filter to check if the user is in any FSM state, excluding the default 'None' state.
    """
    async def __call__(self, message: Message, state: FSMContext) -> bool:
        user_id = message.from_user.id
        current_state = await state.get_state()
        
        # The filter will pass only if the state is not None.
        result = current_state is not None
        
        logger.debug(
            f"Filter 'IsInState' checked for user {user_id}. "
            f"Current state: '{current_state}'. Filter result: {result}."
        )
        
        return result