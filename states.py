# hiddy_bot/states.py

from aiogram.fsm.state import State, StatesGroup  # type: ignore

class AddUserState(StatesGroup):
    waiting_for_name = State()
    waiting_for_note = State()
    waiting_for_usage_limit = State()
    waiting_for_package_days = State()
    waiting_for_mode = State()
    waiting_for_confirmation = State()

class SearchUserState(StatesGroup):
    waiting_for_uuid = State()

class GetUserInfoState(StatesGroup):
    waiting_for_user_info = State()

class RenewSubscriptionState(StatesGroup):
    waiting_for_plan_confirmation = State()
    waiting_for_receipt_photo = State()
    waiting_for_admin_confirmation = State()

class BuySubscriptionState(StatesGroup):
    waiting_for_plan_confirmation = State()
    waiting_for_receipt_photo = State()
    waiting_for_admin_confirmation = State()

class EditUserState(StatesGroup):
    waiting_for_new_value = State()

# [MODIFIED] States for the Send Message feature
class SendMessageState(StatesGroup):
    # This initial state might be useful if you add more target types later
    waiting_for_target_type = State() 
    # State for when the admin needs to provide a UUID
    waiting_for_target_uuid = State()
    # State for when the bot is waiting for the message content
    waiting_for_message_text = State()
    # State for the final "Are you sure?" confirmation for broadcasts
    waiting_for_confirmation = State()
    # New states for dynamic messaging
    waiting_for_inline_buttons_count = State()
    waiting_for_button_name = State()
    waiting_for_button_url = State()
    waiting_for_final_confirmation = State()

# NEW: Button Management States
class ButtonManagementState(StatesGroup):
    waiting_for_bank_account_name = State()
    waiting_for_bank_card_number = State()

# NEW: Emergency Config States  
class EmergencyConfigState(StatesGroup):
    waiting_for_emergency_message = State()

# NEW: Plan Management States
class PlanManagementState(StatesGroup):
    waiting_for_plan_id = State()
    waiting_for_plan_name = State()
    waiting_for_plan_description = State()
    waiting_for_plan_days = State()
    waiting_for_plan_volume = State()
    waiting_for_plan_price = State()
    waiting_for_plan_confirmation = State()
    
    # NEW: Individual field editing states
    waiting_for_edit_name = State()
    waiting_for_edit_description = State()
    waiting_for_edit_days = State()
    waiting_for_edit_volume = State()
    waiting_for_edit_price = State()

# NEW: Backup Schedule State
class BackupScheduleState(StatesGroup):
    waiting_for_time = State()