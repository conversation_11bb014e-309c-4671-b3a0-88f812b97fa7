# hiddy_bot/api_hiddify.py

import http.client  # type: ignore
import json  # type: ignore
import logging  # type: ignore
from html import escape as html_escape  # type: ignore
import asyncio  # type: ignore

from messages import Messages  # type: ignore
from config import (  # type: ignore
    HIDDIFY_API_DOMAIN,
    HIDDIFY_API_TOKEN,
    HIDDIFY_API_PROXY_PATH,
    HIDDIFY_API_USER_PROXY_PATH
)

# لاگر مخصوص این فایل را دریافت می‌کنیم
logger = logging.getLogger(__name__)

# --- Base API Caller (async) ---

async def _call_api(method: str, endpoint: str, user_key: str = None, payload: dict = None) -> tuple[bool, str, int | None]:
    """An async generic function to make requests to the Hiddify API."""
    
    def request_sync():
        conn = None
        # Admin endpoints use the main proxy path
        full_endpoint = f"/{HIDDIFY_API_PROXY_PATH}/api/v2/admin/{endpoint}"
        
        headers = {
            "Accept": "application/json",
            "Hiddify-API-Key": HIDDIFY_API_TOKEN,
        }

        # User-specific endpoints use a different path and key
        if user_key:
            logger.debug(f"This is a USER-SPECIFIC API call. Overriding key and endpoint.")
            headers["Hiddify-API-Key"] = user_key
            full_endpoint = f"/{HIDDIFY_API_USER_PROXY_PATH}/api/v2/user/{endpoint}"
            
        if payload:
            headers["Content-Type"] = "application/json"
            
        logger.info(f"API Call: {method} https://{HIDDIFY_API_DOMAIN}{full_endpoint}")
        if payload:
            logger.debug(f"Payload: {json.dumps(payload, ensure_ascii=False)}")
        
        try:
            logger.debug(f"Opening HTTPS connection to {HIDDIFY_API_DOMAIN}.")
            conn = http.client.HTTPSConnection(HIDDIFY_API_DOMAIN, timeout=20)
            body = json.dumps(payload) if payload else None
            
            conn.request(method, full_endpoint, body=body, headers=headers)
            
            res = conn.getresponse()
            status_code = res.status
            data = res.read().decode("utf-8")
            
            logger.info(f"API Response: Status {status_code}")
            # Log the full response data at DEBUG level for troubleshooting
            logger.debug(f"Response Data (raw): {data}")
            
            if not (200 <= status_code < 300):
                logger.error(f"API Error: Status {status_code}, Response: {data[:500]}...")
                return False, data, status_code
                
            return True, data, status_code
            
        except Exception as e:
            logger.critical(f"API Exception during {method} call to {full_endpoint}: {e}", exc_info=True)
            return False, str(e), None
        finally:
            if conn:
                logger.debug("Closing HTTPS connection.")
                conn.close()

    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(None, request_sync)


# --- Server Information ---

async def get_server_info() -> tuple[bool, str]:
    """Fetches server information from the Hiddify API."""
    logger.debug("Calling get_server_info which uses _call_api.")
    success, data, _ = await _call_api("GET", "server_info/")
    return success, data
    
async def get_server_status() -> tuple[bool, str, int | None]:
    """Fetches server status and statistics from the Hiddify API."""
    logger.debug("Calling get_server_status which uses _call_api.")
    success, data, status = await _call_api("GET", "server_status/")
    return success, data, status

async def update_user_usage_api() -> tuple[bool, str]:
    """Triggers the Hiddify API endpoint to recalculate user usage."""
    logger.debug("Calling update_user_usage_api which uses _call_api.")
    success, data, _ = await _call_api("GET", "update_user_usage/")
    return success, data


# --- User Management (now fully async) ---

async def fetch_panel_users() -> tuple[bool, list | str]:
    """Fetches all users from the Hiddify panel API."""
    logger.debug("Calling fetch_panel_users which uses _call_api.")
    success, data, _ = await _call_api("GET", "user/")
    if success:
        try:
            parsed_data = json.loads(data)
            logger.debug(f"Successfully parsed {len(parsed_data)} users from API response.")
            return True, parsed_data
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from fetch_panel_users: {e}. Response: {data}", exc_info=True)
            return False, f"JSON decode error: {e}"
    return False, data

async def fetch_user_details_api(uuid) -> tuple[bool, str, int | None]:
    """Fetches details for a specific user UUID from the Hiddify API."""
    logger.debug(f"Calling fetch_user_details_api for UUID {uuid} using _call_api.")
    success, data, status = await _call_api("GET", f"user/{uuid}/")
    return success, data, status

async def add_user_api(payload: dict) -> tuple[bool, str, int | None]:
    """Adds a new user via the Hiddify API using POST."""
    logger.debug(f"Calling add_user_api using _call_api.")
    success, data, status = await _call_api("POST", "user/", payload=payload)
    return success, data, status

async def update_user_api(uuid: str, payload: dict) -> tuple[bool, str, int | None]:
    """Updates a user's details via the Hiddify API using PATCH."""
    logger.debug(f"Calling update_user_api for UUID {uuid} using _call_api.")
    success, data, status = await _call_api("PATCH", f"user/{uuid}/", payload=payload)
    return success, data, status

async def delete_user_api(uuid_to_delete: str) -> tuple[bool, str, int | None]:
    """Deletes a user via the Hiddify API."""
    logger.debug(f"Calling delete_user_api for UUID {uuid_to_delete} using _call_api.")
    success, data, status = await _call_api("DELETE", f"user/{uuid_to_delete}/")
    return success, data, status


# --- User-Facing Endpoints ---

async def fetch_user_configs_api(uuid: str) -> tuple[bool, list | str, int | None]:
    """Fetches all configuration links for a specific user UUID using user's UUID as key."""
    logger.debug(f"Calling fetch_user_configs_api for UUID {uuid} using _call_api (user-specific).")
    success, data, status = await _call_api("GET", "all-configs/", user_key=uuid)
    if success:
        try:
            parsed_data = json.loads(data)
            logger.debug(f"Successfully parsed user configs for {uuid}.")
            return True, parsed_data, status
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from fetch_user_configs_api for {uuid}: {e}", exc_info=True)
            return False, Messages.General.API_ERROR_PARSE_DETAILS, status
    return False, Messages.General.API_ERROR_FETCH_DETAILS(error=f"Status {status}: {html_escape(data)}"), status

async def fetch_apps_deeplinks_api(uuid: str, platform="all") -> tuple[bool, list | str]:
    """Fetches app deeplinks for a specific user UUID from the Hiddify API."""
    # This function has a different structure and is kept separate from _call_api for now.
    logger.debug(f"Calling specialized fetch_apps_deeplinks_api for UUID {uuid}.")
    
    def request_sync():
        conn = None
        try:
            conn = http.client.HTTPSConnection(HIDDIFY_API_DOMAIN, timeout=15)
            headers = { "Accept": "application/json", "Hiddify-API-Key": HIDDIFY_API_TOKEN }
            endpoint = f"/{HIDDIFY_API_USER_PROXY_PATH}/{uuid}/api/v2/user/apps/?platform={platform}"
            logger.info(f"API Call (specialized): GET https://{HIDDIFY_API_DOMAIN}{endpoint}")
            conn.request("GET", endpoint, headers=headers)
            res = conn.getresponse()
            data = res.read().decode("utf-8")
            
            logger.info(f"API Response (specialized): Status {res.status}")
            logger.debug(f"Response Data (specialized, raw): {data}")

            if res.status != 200:
                logger.error(f"Failed to fetch app deeplinks for {uuid}. Status: {res.status}")
                return False, Messages.General.API_ERROR_FETCH_DETAILS(error=f"Status {res.status}")
            return True, json.loads(data)
        except Exception as e:
            logger.critical(f"Exception during fetch_apps_deeplinks_api for {uuid}: {e}", exc_info=True)
            return False, str(e)
        finally:
            if conn:
                logger.debug("Closing HTTPS connection (specialized).")
                conn.close()
            
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(None, request_sync)