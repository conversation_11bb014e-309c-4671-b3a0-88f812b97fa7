# hiddy_bot/middlewares.py

import time  # type: ignore
import logging  # type: ignore
import traceback  # type: ignore
import jdatetime  # type: ignore
from typing import Callable, Dict, Any, Awaitable  # type: ignore

from aiogram import BaseMiddleware, Bot  # type: ignore
from aiogram.types import Message, CallbackQuery  # type: ignore
from aiogram.utils.keyboard import InlineKeyboardBuilder  # type: ignore

from messages import Messages  # type: ignore
from config import ADMIN_USER_ID, THROTTLE_RATE_LIMIT, SUPPORT_TELEGRAM_LINK  # type: ignore

logger = logging.getLogger(__name__)

class ErrorHandlingMiddleware(BaseMiddleware):
    """
    A middleware to catch all unhandled exceptions and send user-friendly error reports.
    """
    
    async def __call__(
        self,
        handler: Callable[[Message | CallbackQuery, Dict[str, Any]], Awaitable[Any]],
        event: Message | CallbackQuery,
        data: Dict[str, Any]
    ) -> Any:
        
        try:
            # Execute the handler
            return await handler(event, data)
        except Exception as e:
            # Log the error
            user = data.get('event_from_user')
            user_id = user.id if user else "Unknown"
            error_context = f"User {user_id} - Event: {type(event).__name__}"
            
            logger.error(f"Unhandled exception in {error_context}: {e}", exc_info=True)
            
            # Send error report to user
            await self._send_error_report_to_user(event, e, data)
            
            # Send critical errors to admin
            await self._send_critical_error_to_admin(event, e, data, error_context)
            
            # Don't re-raise the exception to prevent bot from crashing
            return None
    
    async def _send_error_report_to_user(self, event: Message | CallbackQuery, error: Exception, data: Dict[str, Any]):
        """Send a formatted error report to the user"""
        try:
            bot: Bot = data.get('bot')
            if not bot:
                return
            
            user = data.get('event_from_user')
            if not user:
                return
            
            # Generate error report
            error_type = f"🔴 نوع خطا: {type(error).__name__}"
            persian_time = f"🕐 زمان: {jdatetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')}"
            error_details = f"📋 جزئیات: {str(error)[:200]}{'...' if len(str(error)) > 200 else ''}"
            
            error_report = Messages.General.ERROR_REPORT_FORMAT(error_type, persian_time, error_details)
            error_message = Messages.General.ERROR_OCCURRED_USER.format(error_report=error_report)
            
            # Create support button
            builder = InlineKeyboardBuilder()
            if SUPPORT_TELEGRAM_LINK:
                builder.button(text=Messages.General.ERROR_BUTTON_SUPPORT, url=SUPPORT_TELEGRAM_LINK)
            
            # Send error message to user
            if isinstance(event, CallbackQuery):
                # Try to edit the callback message or send a new one
                try:
                    await event.message.edit_text(
                        error_message,
                        reply_markup=builder.as_markup() if SUPPORT_TELEGRAM_LINK else None
                    )
                except:
                    # If editing fails, send a new message
                    await bot.send_message(
                        user.id,
                        error_message,
                        reply_markup=builder.as_markup() if SUPPORT_TELEGRAM_LINK else None
                    )
                try:
                    await event.answer("❌ خطایی رخ داد", show_alert=True)
                except:
                    pass
            else:
                # For regular messages
                await bot.send_message(
                    user.id,
                    error_message,
                    reply_markup=builder.as_markup() if SUPPORT_TELEGRAM_LINK else None
                )
                
        except Exception as report_error:
            # If sending error report fails, just log it
            logger.error(f"Failed to send error report to user: {report_error}")

    async def _send_critical_error_to_admin(self, event: Message | CallbackQuery, error: Exception, data: Dict[str, Any], error_context: str):
        """Send a critical error report to the admin"""
        try:
            bot: Bot = data.get('bot')
            if not bot or not ADMIN_USER_ID:
                return
            
            user = data.get('event_from_user')
            user_id = user.id if user else "Unknown"
            
            # Generate error report
            error_type = f"🔴 نوع خطا: {type(error).__name__}"
            persian_time = f"🕐 زمان: {jdatetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')}"
            user_info = f"👤 کاربر: {user_id}"
            context_info = f"📍 محل: {error_context}"
            error_details = f"📋 جزئیات: {str(error)[:300]}{'...' if len(str(error)) > 300 else ''}"
            
            error_report = f"{error_type}\n{persian_time}\n{user_info}\n{context_info}\n{error_details}"
            error_message = Messages.General.ERROR_OCCURRED_ADMIN.format(error_report=error_report)
            
            # Send error message to admin
            await bot.send_message(ADMIN_USER_ID, error_message)
            
        except Exception as report_error:
            # If sending error report fails, just log it
            logger.error(f"Failed to send critical error report to admin: {report_error}")


class ThrottlingMiddleware(BaseMiddleware):
    """
    A custom middleware to prevent users from spamming the bot.
    """
    def __init__(self, rate_limit: float = THROTTLE_RATE_LIMIT):
        self.rate_limit = rate_limit
        self.user_last_action: Dict[int, float] = {}
        logger.info(f"Throttling Middleware initialized with rate limit: {rate_limit}s")

    async def __call__(
        self,
        handler: Callable[[Message | CallbackQuery, Dict[str, Any]], Awaitable[Any]],
        event: Message | CallbackQuery,
        data: Dict[str, Any]
    ) -> Any:
        
        user = data.get('event_from_user')
        if not user:
            logger.debug("ThrottlingMiddleware: Could not find 'event_from_user' in data. Skipping.")
            return await handler(event, data)

        user_id = user.id

        # Admin is exempt from throttling
        if user_id == ADMIN_USER_ID:
            logger.debug(f"User {user_id} is ADMIN, exempt from throttling.")
            return await handler(event, data)

        current_time = time.time()
        last_action_time = self.user_last_action.get(user_id, 0)

        if (current_time - last_action_time) < self.rate_limit:
            logger.warning(f"User {user_id} is being throttled. Time since last action: {current_time - last_action_time:.2f}s.")
            if isinstance(event, CallbackQuery):
                try:
                    await event.answer(Messages.General.THROTTLE_MESSAGE, show_alert=False)
                except Exception:
                    logger.warning(f"Could not answer throttled callback query for user {user_id}.")
            # We just ignore Message events to prevent spamming the chat
            return

        self.user_last_action[user_id] = current_time
        logger.debug(f"User {user_id} is not throttled. Proceeding with handler.")
        return await handler(event, data)