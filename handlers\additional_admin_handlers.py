# handlers/additional_admin_handlers.py

import logging
import asyncio
from aiogram import Router, F, Bot # type: ignore
from aiogram.fsm.context import FSMContext # type: ignore
from aiogram.types import CallbackQuery, Message, InlineKeyboardButton # type: ignore
from aiogram.filters import StateFilter # type: ignore
from aiogram.utils.keyboard import InlineKeyboardBuilder # type: ignore

from messages import Messages
from states import ButtonManagementState, EmergencyConfigState, SendMessageState
from database import get_comprehensive_income_stats, get_bot_setting, set_bot_setting, get_emergency_config, save_emergency_config, delete_emergency_config, get_user_from_database_by_uuid, get_all_users_with_uuid, get_renewal_method, set_renewal_method
from keyboards import create_button_management_menu, create_emergency_config_menu, create_renewal_management_keyboard

router = Router()
logger = logging.getLogger(__name__)


async def safe_edit_message(callback_query: CallbackQuery, text: str, reply_markup=None, parse_mode="HTML"):
    """Safely edit a message, handling the 'message is not modified' error"""
    try:
        await callback_query.message.edit_text(text, reply_markup=reply_markup, parse_mode=parse_mode)
    except Exception as e:
        if "message is not modified" in str(e).lower():
            logger.debug(f"Message not modified for admin {callback_query.from_user.id}, content is the same")
            if not callback_query.message.reply_markup and not reply_markup:
                await callback_query.answer()
            else:
                try:
                    await callback_query.message.edit_reply_markup(reply_markup=reply_markup)
                except:
                    await callback_query.answer()
        else:
            raise e


# Income Status Callback Handlers
@router.callback_query(F.data.startswith("admin:income:"))
async def handle_income_status_callbacks(callback_query: CallbackQuery):
    admin_id = callback_query.from_user.id
    action = callback_query.data.split(":")[-1]
    logger.info(f"Admin {admin_id} income status callback: {action}")
    
    try:
        M = Messages.Admin.IncomeStatus
        stats = await get_comprehensive_income_stats()
        
        message_text = ""
        if action == "show_renewed":
            message_text = M.USER_LIST_TITLE.format(category_name=M.CATEGORY_RENEWED)
            message_text += M.RENEWED_TITLE
            message_text += M.TOTAL_COUNT.format(stats['renewals_this_month'])
            message_text += M.DETAILS_NOTE
            
        elif action == "show_new_purchases":
            message_text = M.USER_LIST_TITLE.format(category_name=M.CATEGORY_NEW_PURCHASES)
            message_text += M.NEW_PURCHASES_TITLE
            message_text += M.TOTAL_COUNT.format(stats['new_purchases_this_month'])
            message_text += M.DETAILS_NOTE
            
        elif action == "show_due_renewal":
            message_text = M.USER_LIST_TITLE.format(category_name=M.CATEGORY_DUE_RENEWAL)
            message_text += M.DUE_RENEWAL_TITLE
            message_text += M.TOTAL_COUNT.format(stats['due_for_renewal'])
            message_text += M.PROJECTED_INCOME.format(stats['projected_monthly_income'])
            message_text += M.DUE_RENEWAL_NOTE
            
        elif action == "show_expiring_soon":
            message_text = M.USER_LIST_TITLE.format(category_name=M.CATEGORY_EXPIRING_SOON)
            message_text += M.EXPIRING_SOON_TITLE
            message_text += M.TOTAL_COUNT.format(stats['expiring_soon'])
            message_text += M.EXPIRING_SOON_NOTE
            
        else:
            await callback_query.answer(M.INVALID_ACTION, show_alert=True)
            return
        
        builder = InlineKeyboardBuilder()
        builder.button(text=M.BUTTON_BACK_TO_INCOME, callback_data="admin:income_status")
        
        await safe_edit_message(callback_query, message_text, reply_markup=builder.as_markup())
        await callback_query.answer()
        
    except Exception as e:
        logger.error(f"Error in income status callback {action}: {e}", exc_info=True)
        await callback_query.answer(Messages.Admin.IncomeStatus.INFO_ERROR, show_alert=True)


# Income Status Back Handler
@router.callback_query(F.data == "admin:income_status")
async def handle_income_status_return(callback_query: CallbackQuery):
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} returning to income status")
    
    try:
        stats = await get_comprehensive_income_stats()
        
        message_text = Messages.Admin.IncomeStatus.TITLE
        message_text += Messages.Admin.IncomeStatus.DAILY_INCOME.format(amount=f"{stats['daily_income']:,}")
        message_text += Messages.Admin.IncomeStatus.MONTHLY_INCOME_MTD.format(amount=f"{stats['monthly_income']:,}")
        message_text += Messages.Admin.IncomeStatus.SIX_MONTHLY_INCOME_ROLLING.format(amount=f"{stats['six_month_income']:,}")
        message_text += Messages.Admin.IncomeStatus.YEARLY_INCOME_YTD.format(amount=f"{stats['yearly_income']:,}")
        message_text += Messages.Admin.IncomeStatus.PROJECTED_MONTHLY_INCOME.format(amount=f"{stats['projected_monthly_income']:,}")
        message_text += Messages.Admin.IncomeStatus.RENEWALS_THIS_MONTH_COUNT.format(count=stats['renewals_this_month'])
        message_text += Messages.Admin.IncomeStatus.NEW_PURCHASES_THIS_MONTH_COUNT.format(count=stats['new_purchases_this_month'])
        message_text += Messages.Admin.IncomeStatus.DUE_FOR_RENEWAL_THIS_MONTH_COUNT.format(count=stats['due_for_renewal'])
        message_text += Messages.Admin.IncomeStatus.EXPIRING_SOON_NEXT_7_DAYS_COUNT.format(count=stats['expiring_soon'])
        message_text += Messages.Admin.IncomeStatus.TOTAL_ACTIVE_USERS_PANEL_COUNT.format(count=stats['total_active_users'])
        
        # Build action buttons dynamically based on available data (avoid duplicate/unused buttons)
        builder = InlineKeyboardBuilder()
        if stats['renewals_this_month'] > 0:
            builder.button(text=Messages.Admin.IncomeStatus.BUTTON_SHOW_RENEWED_THIS_MONTH,
                           callback_data="admin:income:show_renewed")
        if stats['new_purchases_this_month'] > 0:
            builder.button(text=Messages.Admin.IncomeStatus.BUTTON_SHOW_NEW_PURCHASES_THIS_MONTH,
                           callback_data="admin:income:show_new_purchases")
        if stats['due_for_renewal'] > 0:
            builder.button(text=Messages.Admin.IncomeStatus.BUTTON_SHOW_DUE_RENEWAL_THIS_MONTH,
                           callback_data="admin:income:show_due_renewal")
        if stats['expiring_soon'] > 0:
            builder.button(text=Messages.Admin.IncomeStatus.BUTTON_SHOW_EXPIRING_SOON_7_DAYS,
                           callback_data="admin:income:show_expiring_soon")

        builder.adjust(1)
        
        await safe_edit_message(callback_query, message_text, reply_markup=builder.as_markup())
        await callback_query.answer()
        
    except Exception as e:
        logger.error(f"Error returning to income status: {e}", exc_info=True)
        await callback_query.answer("خطا در نمایش اطلاعات", show_alert=True)


# Button Management Handlers
@router.callback_query(F.data.startswith("admin:toggle_button:"))
async def handle_button_toggle(callback_query: CallbackQuery):
    admin_id = callback_query.from_user.id
    button_type = callback_query.data.split(":")[-1]
    logger.info(f"Admin {admin_id} toggling button: {button_type}")
    
    M = Messages.Admin.ButtonManagement
    setting_key = f"{button_type}_subscription_enabled"
    current_status = get_bot_setting(setting_key, '1') == '1'
    new_status = not current_status
    
    if set_bot_setting(setting_key, '1' if new_status else '0'):
        button_name = M.BUTTON_NAME_BUY if button_type == "buy" else M.BUTTON_NAME_TRIAL
        await callback_query.answer(M.TOGGLE_SUCCESS(button_name, new_status), show_alert=True)
        try:
            await callback_query.message.edit_reply_markup(reply_markup=create_button_management_menu())
        except Exception as e:
            if "message is not modified" in str(e).lower():
                logger.debug("Button management menu not modified")
            else:
                raise
    else:
        await callback_query.answer(M.TOGGLE_ERROR, show_alert=True)


@router.callback_query(F.data == "admin:bank_settings")
async def handle_bank_settings(callback_query: CallbackQuery, state: FSMContext):
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} accessing bank settings")
    
    M = Messages.Admin.ButtonManagement
    current_name = get_bot_setting('bank_account_name', M.UNKNOWN_ACCOUNT_HOLDER)
    current_card = get_bot_setting('bank_card_number', M.UNKNOWN_CARD_NUMBER)
    
    message_text = M.BANK_SETTINGS_TITLE + M.CURRENT_BANK_INFO(current_name, current_card)
    
    builder = InlineKeyboardBuilder()
    builder.button(text=M.BUTTON_EDIT_NAME, callback_data="admin:bank:edit_name")
    builder.button(text=M.BUTTON_EDIT_CARD, callback_data="admin:bank:edit_card")
    builder.button(text=Messages.Buttons.BACK, callback_data="admin:button_management")
    builder.adjust(1)
    
    await safe_edit_message(callback_query, message_text, reply_markup=builder.as_markup())
    await callback_query.answer()


@router.callback_query(F.data.startswith("admin:bank:"))
async def handle_bank_edit(callback_query: CallbackQuery, state: FSMContext):
    admin_id = callback_query.from_user.id
    edit_type = callback_query.data.split(":")[-1]
    logger.info(f"Admin {admin_id} editing bank {edit_type}")
    
    if edit_type == "edit_name":
        await state.set_state(ButtonManagementState.waiting_for_bank_account_name)
        await safe_edit_message(callback_query, Messages.Admin.ButtonManagement.PROMPT_BANK_NAME)
    elif edit_type == "edit_card":
        await state.set_state(ButtonManagementState.waiting_for_bank_card_number)
        await safe_edit_message(callback_query, Messages.Admin.ButtonManagement.PROMPT_BANK_CARD)
    
    await callback_query.answer()


# --- Renewal Management Handlers ---
@router.callback_query(F.data == "admin:renewal_management")
async def handle_renewal_management(callback_query: CallbackQuery):
    """Displays the renewal management options."""
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} navigating to Renewal Management.")
    
    await callback_query.answer()
    
    current_method = get_renewal_method()
    
    # Using hardcoded strings as a fallback
    method_titles = {
        1: "روش ۱: ریست کامل",
        2: "روش ۲: تجمیع کامل",
        3: "روش ۳: ریست روز، تجمیع حجم",
        4: "روش ۴: تجمیع روز، ریست حجم"
    }
    
    descriptions = {
        1: "<b>روزهای باقیمانده حذف</b> و روزهای پلن جدید جایگزین می‌شود.\n<b>حجم باقیمانده حذف</b> و حجم پلن جدید جایگزین می‌شود.",
        2: "<b>روزهای باقیمانده حفظ</b> و روزهای پلن جدید به آن اضافه می‌شود.\n<b>حجم باقیمانده حفظ</b> و حجم پلن جدید به آن اضافه می‌شود.",
        3: "<b>روزهای باقیمانده حذف</b> و روزهای پلن جدید جایگزین می‌شود.\n<b>حجم باقیمانده حفظ</b> و حجم پلن جدید به آن اضافه می‌شود.",
        4: "<b>روزهای باقیمانده حفظ</b> و روزهای پلن جدید به آن اضافه می‌شود.\n<b>حجم باقیمانده حذف</b> و حجم پلن جدید جایگزین می‌شود."
    }

    current_method_title = method_titles.get(current_method, "تنظیم نشده")
    
    title = f"<b>⚙️ مدیریت روش تمدید اشتراک</b>\n\n"
    title += f"روش فعلی انتخاب شده: <b>{current_method_title}</b>\n\n"
    title += "لطفا روش پیش‌فرض برای تمدید اشتراک کاربران را انتخاب کنید. در تمامی روش‌ها، حجم مصرفی کاربر پس از تمدید صفر خواهد شد.\n\n"
    title += "--------------\n\n"
    
    for method, desc in descriptions.items():
        title += f"<b>{method_titles[method]}</b>\n{desc}\n\n"
        
    await safe_edit_message(
        callback_query,
        title,
        reply_markup=create_renewal_management_keyboard(current_method)
    )

@router.callback_query(F.data.startswith("admin:renewal:set:"))
async def handle_set_renewal_method(callback_query: CallbackQuery):
    """Sets the selected renewal method."""
    admin_id = callback_query.from_user.id
    try:
        method = int(callback_query.data.split(":")[-1])
        logger.info(f"Admin {admin_id} is setting renewal method to: {method}")

        if method not in [1, 2, 3, 4]:
            raise ValueError("Invalid method")

        set_renewal_method(method)
        
        try:
            M = Messages.Admin.RenewalManagement
            selected_method_title = M.METHOD_TITLES.get(method)
            success_message = M.SUCCESS_MESSAGE.format(selected_method_title=selected_method_title)
            current_method_title = M.METHOD_TITLES.get(method, M.NO_METHOD_SET)
            title = M.TITLE.format(current_method_title=current_method_title)
        except AttributeError:
            method_titles = {1: "ریست کامل (روز و حجم)", 2: "تجمیع کامل (روز و حجم)", 3: "ریست روز، تجمیع حجم", 4: "تجمیع روز، ریست حجم"}
            selected_method_title = method_titles.get(method)
            success_message = f"✅ روش تمدید با موفقیت به <b>{selected_method_title}</b> تغییر یافت."
            current_method_title = method_titles.get(method, "تنظیم نشده")
            title = f"<b>⚙️ مدیریت روش تمدید اشتراک کاربران</b>\n\nروش فعلی: <b>{current_method_title}</b>"


        await callback_query.answer(success_message.replace("<b>", "").replace("</b>", ""), show_alert=True)
        
        await safe_edit_message(
            callback_query,
            title,
            reply_markup=create_renewal_management_keyboard(method)
        )

    except (ValueError, IndexError) as e:
        logger.error(f"Invalid renewal method callback data: {callback_query.data}, error: {e}")
        try:
            error_msg = Messages.Admin.RenewalManagement.ERROR_MESSAGE
        except AttributeError:
            error_msg = "❌ خطا در بروزرسانی روش تمدید."
        await callback_query.answer(error_msg, show_alert=True)


# Emergency Config Handlers  
@router.callback_query(F.data.startswith("admin:emergency:"))
async def handle_emergency_config(callback_query: CallbackQuery, state: FSMContext, bot: Bot):
    admin_id = callback_query.from_user.id
    action = callback_query.data.split(":")[-1]
    logger.info(f"Admin {admin_id} emergency config action: {action}")
    
    M = Messages.Admin.EmergencyConfig
    if action == "set_new":
        await state.set_state(EmergencyConfigState.waiting_for_emergency_message)
        builder = InlineKeyboardBuilder()
        builder.button(text=Messages.Buttons.BACK, callback_data="admin:emergency_config")
        await safe_edit_message(callback_query, M.PROMPT_NEW_MESSAGE, reply_markup=builder.as_markup())
    
    elif action == "view":
        config = get_emergency_config()
        if not config:
            await callback_query.answer(M.NO_CONFIG_SET, show_alert=True)
            return
        
        message_text = M.CURRENT_MESSAGE_TITLE
        message_text += config.get('message_text', '')
        if config.get('created_at'):
            message_text += M.CREATED_AT(config['created_at'])
        
        builder = InlineKeyboardBuilder()
        builder.button(text=Messages.Buttons.BACK, callback_data="admin:emergency_config")
        
        if config.get('media_type') and config.get('media_file_id'):
            media_type = config['media_type']
            media_file_id = config['media_file_id']
            caption = message_text
            
            try:
                if media_type == 'photo':
                    await bot.send_photo(admin_id, photo=media_file_id, caption=caption, reply_markup=builder.as_markup(), parse_mode="HTML")
                elif media_type == 'video':
                    await bot.send_video(admin_id, video=media_file_id, caption=caption, reply_markup=builder.as_markup(), parse_mode="HTML")
                # ... (add other media types with parse_mode="HTML")
                else:
                    # Fallback for other types
                    await bot.send_message(admin_id, caption, reply_markup=builder.as_markup(), parse_mode="HTML")

                await safe_edit_message(callback_query, M.MEDIA_SENT_SUCCESS, reply_markup=create_emergency_config_menu())
            except Exception as e:
                logger.error(f"Error sending emergency config media: {e}")
                await safe_edit_message(callback_query, message_text, reply_markup=builder.as_markup())
        else:
            await safe_edit_message(callback_query, message_text, reply_markup=builder.as_markup())
    
    elif action == "delete":
        if delete_emergency_config():
            await callback_query.answer(M.MESSAGE_DELETED, show_alert=True)
            success_text = M.DELETE_SUCCESS_TEXT + M.ADMIN_TITLE
            await safe_edit_message(callback_query, success_text, reply_markup=create_emergency_config_menu())
        else:
            await callback_query.answer(M.MESSAGE_DELETE_ERROR, show_alert=True)
    
    elif action == "test":
        config = get_emergency_config()
        if not config:
            await callback_query.answer(M.NO_CONFIG_SET, show_alert=True)
            return
        
        message_text = M.TEST_MESSAGE_TITLE + config.get('message_text', '')
        
        if config.get('media_type') and config.get('media_file_id'):
            # Simplified for brevity, you should handle all media types as in 'view'
            try:
                await bot.send_photo(admin_id, photo=config['media_file_id'], caption=message_text, parse_mode="HTML")
                await callback_query.answer(M.TEST_SENT_SUCCESS, show_alert=True)
            except Exception as e:
                logger.error(f"Error sending test emergency message: {e}")
                await callback_query.answer(M.TEST_SENT_ERROR, show_alert=True)
        else:
            await bot.send_message(admin_id, message_text, parse_mode="HTML")
            await callback_query.answer(M.TEST_SENT_SUCCESS, show_alert=True)
    
    await callback_query.answer()


# Emergency Config Back Handler
@router.callback_query(F.data == "admin:emergency_config")
async def handle_emergency_config_return(callback_query: CallbackQuery, state: FSMContext):
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} returning to emergency config menu")
    
    await state.clear()
    await safe_edit_message(callback_query, Messages.Admin.EmergencyConfig.ADMIN_TITLE, reply_markup=create_emergency_config_menu())
    await callback_query.answer()


# Button Management FSM Handlers
@router.message(StateFilter(ButtonManagementState.waiting_for_bank_account_name), F.text)
async def process_bank_name_change(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    new_name = message.text.strip()
    logger.info(f"Admin {admin_id} updating bank account name to: {new_name}")
    
    if set_bot_setting('bank_account_name', new_name):
        await message.answer(Messages.Admin.ButtonManagement.BANK_UPDATE_SUCCESS)
    else:
        await message.answer(Messages.Admin.ButtonManagement.BANK_UPDATE_ERROR)
    
    await state.clear()
    
    current_name = get_bot_setting('bank_account_name', 'نامشخص')
    current_card = get_bot_setting('bank_card_number', 'نامشخص')
    
    message_text = Messages.Admin.ButtonManagement.BANK_SETTINGS_TITLE
    message_text += Messages.Admin.ButtonManagement.CURRENT_BANK_INFO(current_name, current_card)
    
    builder = InlineKeyboardBuilder()
    builder.button(text="✏️ تغییر نام صاحب حساب", callback_data="admin:bank:edit_name")
    builder.button(text="✏️ تغییر شماره کارت", callback_data="admin:bank:edit_card")
    builder.button(text="🔙 بازگشت", callback_data="admin:button_management")
    builder.adjust(1)
    
    await message.answer(message_text, reply_markup=builder.as_markup(), parse_mode="HTML")


@router.message(StateFilter(ButtonManagementState.waiting_for_bank_card_number), F.text)
async def process_bank_card_change(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    new_card = message.text.strip().replace(' ', '').replace('-', '')
    logger.info(f"Admin {admin_id} updating bank card number")
    
    if not new_card.isdigit() or len(new_card) != 16:
        await message.reply(Messages.Admin.ButtonManagement.INVALID_CARD_NUMBER)
        return
    
    if set_bot_setting('bank_card_number', new_card):
        await message.answer(Messages.Admin.ButtonManagement.BANK_UPDATE_SUCCESS)
    else:
        await message.answer(Messages.Admin.ButtonManagement.BANK_UPDATE_ERROR)
    
    await state.clear()
    
    current_name = get_bot_setting('bank_account_name', 'نامشخص')
    current_card = get_bot_setting('bank_card_number', 'نامشخص')
    
    message_text = Messages.Admin.ButtonManagement.BANK_SETTINGS_TITLE
    message_text += Messages.Admin.ButtonManagement.CURRENT_BANK_INFO(current_name, current_card)
    
    builder = InlineKeyboardBuilder()
    builder.button(text="✏️ تغییر نام صاحب حساب", callback_data="admin:bank:edit_name")
    builder.button(text="✏️ تغییر شماره کارت", callback_data="admin:bank:edit_card")
    builder.button(text="🔙 بازگشت", callback_data="admin:button_management")
    builder.adjust(1)
    
    await message.answer(message_text, reply_markup=builder.as_markup(), parse_mode="HTML")


# Emergency Config FSM Handler
@router.message(StateFilter(EmergencyConfigState.waiting_for_emergency_message))
async def process_emergency_message(message: Message, state: FSMContext):
    admin_id = message.from_user.id
    logger.info(f"Admin {admin_id} setting emergency config message")
    
    message_text = message.text or message.caption or ""
    media_type, media_file_id = None, None
    
    if message.photo: media_type, media_file_id = "photo", message.photo[-1].file_id
    elif message.video: media_type, media_file_id = "video", message.video.file_id
    # ... (add other media types)

    if save_emergency_config(message_text, media_type, media_file_id, message.caption, admin_id):
        await message.answer(Messages.Admin.EmergencyConfig.MESSAGE_SAVED)
    else:
        await message.answer(Messages.Admin.DynamicSendMessage.SAVE_ERROR)
    
    await state.clear()
    await message.answer(Messages.Admin.EmergencyConfig.ADMIN_TITLE, reply_markup=create_emergency_config_menu())

# ... (rest of the file remains the same)

@router.callback_query(F.data == "admin:button_management")
async def handle_button_management_return(callback_query: CallbackQuery):
    admin_id = callback_query.from_user.id
    logger.info(f"Admin {admin_id} returning to button management")
    
    await safe_edit_message(callback_query, Messages.Admin.ButtonManagement.TITLE, reply_markup=create_button_management_menu())
    await callback_query.answer()