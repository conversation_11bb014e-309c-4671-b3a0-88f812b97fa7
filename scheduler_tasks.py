# hiddy_bot/scheduler_tasks.py

import logging  # type: ignore
import asyncio  # type: ignore
import datetime  # type: ignore
import json  # type: ignore
import pytz  # type: ignore
import jdatetime  # type: ignore

from aiogram import Bo<PERSON>, exceptions  # type: ignore
from aiogram.types import BufferedInputFile  # type: ignore
from aiogram.utils.markdown import html_decoration  # type: ignore
from aiogram.utils.keyboard import InlineKeyboardBuilder  # type: ignore

from messages import Messages  # type: ignore
from api_hiddify import fetch_user_details_api  # type: ignore
from ssh_utils import fetch_latest_backup_ssh, create_instant_backup_ssh  # type: ignore
from database import (  # type: ignore
    create_database_table,
    update_or_add_user,
    store_panel_users_in_db,
    get_renewal_projection_stats,
    get_all_bot_users,
)
from config import ADMIN_USER_ID, PANEL_LINK_PREFIX, HIDDIFY_API_PROXY_PATH, PROJECT_DIR
from keyboards import create_user_initial_menu, create_user_regular_menu  # type: ignore
from api_hiddify import fetch_panel_users  # type: ignore
import os

logger = logging.getLogger(__name__)

# Scheduler instance will be injected from bot.py
scheduler = None  # type: ignore


# --- Daily Subscription Check ---

async def check_subscription_expiry(bot: Bot):
    logger.info("[Scheduler] Starting daily subscription check job...")
    users_to_clear_uuid = []
    
    try:
        from database import get_all_users_with_uuid
        users_to_check = get_all_users_with_uuid()
        logger.info(f"[Scheduler] Found {len(users_to_check)} users with UUIDs to check.")

        today = datetime.date.today()

        for user_tg_id, user_uuid, is_trial_db in users_to_check:
            logger.debug(f"[Scheduler] Checking user TG ID: {user_tg_id}, UUID: {user_uuid}, Is Trial: {is_trial_db}")
            await asyncio.sleep(0.1) # Be gentle with the API
            is_trial_user = bool(is_trial_db)
            
            success, details_resp, status = await fetch_user_details_api(user_uuid)
            if not success:
                logger.warning(f"[Scheduler] API call failed for user {user_uuid}. Status: {status}, Resp: {details_resp}")
                if "user not found" in str(details_resp).lower():
                    logger.warning(f"[Scheduler] User {user_uuid} not found on panel. Adding to cleanup list.")
                    users_to_clear_uuid.append(user_tg_id)
                continue

            try:
                user_dict = json.loads(details_resp)
                if not user_dict.get("enable", False):
                    logger.debug(f"[Scheduler] Skipping disabled user {user_uuid}.")
                    continue

                start_date_str = user_dict.get("start_date")
                package_days = user_dict.get("package_days")
                
                if not (start_date_str and isinstance(package_days, int)):
                    logger.warning(f"[Scheduler] Skipping user {user_uuid} due to invalid start_date or package_days.")
                    continue

                start_date = datetime.datetime.strptime(start_date_str, "%Y-%m-%d").date()
                expiry_date = start_date + datetime.timedelta(days=package_days)
                remaining_days = (expiry_date - today).days
                logger.debug(f"[Scheduler] User {user_uuid} has {remaining_days} days remaining.")

                message_to_send = None
                
                if is_trial_user and remaining_days < 0:
                    logger.info(f"[Scheduler] Trial for user {user_tg_id} has expired.")
                    message_to_send = Messages.Scheduler.TRIAL_EXPIRED
                    users_to_clear_uuid.append(user_tg_id)
                elif not is_trial_user:
                    if remaining_days == 5: message_to_send = Messages.Scheduler.EXPIRY_5_DAYS
                    elif remaining_days == 2: message_to_send = Messages.Scheduler.EXPIRY_2_DAYS
                    elif remaining_days == 1: message_to_send = Messages.Scheduler.EXPIRY_1_DAY
                    elif remaining_days < 0: message_to_send = Messages.Scheduler.SUBSCRIPTION_EXPIRED

                current_usage_gb = user_dict.get('current_usage_GB', 0.0)
                usage_limit_gb = user_dict.get('usage_limit_GB', 0.0)
                remaining_usage_gb = max(0.0, usage_limit_gb - current_usage_gb)
                
                if not message_to_send and not is_trial_user:
                    logger.debug(f"[Scheduler] Checking usage for {user_uuid}. Remaining: {remaining_usage_gb:.2f}GB")
                    if 0.0 < remaining_usage_gb < 1.0:
                        message_to_send = Messages.Scheduler.USAGE_LOW_1GB(remaining_gb=remaining_usage_gb)
                    elif 1.0 <= remaining_usage_gb < 2.0:
                        message_to_send = Messages.Scheduler.USAGE_LOW_2GB(remaining_gb=remaining_usage_gb)
                
                if message_to_send:
                    logger.info(f"[Scheduler] Preparing to send notification to user {user_tg_id}.")
                    reply_markup = create_user_initial_menu() if is_trial_user else create_user_regular_menu(user_tg_id)
                    try:
                        await bot.send_message(user_tg_id, message_to_send, reply_markup=reply_markup, parse_mode="Markdown")
                        logger.info(f"[Scheduler] Sent notification successfully to {user_tg_id}.")
                    except Exception as notify_err:
                        logger.error(f"[Scheduler] Failed to send notification to {user_tg_id}: {notify_err}", exc_info=True)

            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"[Scheduler] Error processing API data for {user_uuid}: {e}", exc_info=True)

    except Exception as e:
        logger.critical(f"[Scheduler] An unexpected error occurred during the expiry check job: {e}", exc_info=True)
    
    if users_to_clear_uuid:
        logger.info(f"[Scheduler] Cleaning up UUIDs for {len(users_to_clear_uuid)} users.")
        from database import clear_user_uuid
        for user_id in set(users_to_clear_uuid):
            clear_user_uuid(user_id)
            logger.debug(f"[Scheduler] Cleared UUID for user {user_id}.")


# --- Hourly DB Update ---

async def scheduled_update_panel_db():
    logger.info("[Scheduler] Starting hourly user DB update job...")
    try:
        success, users_data = await fetch_panel_users()
        if success:
            logger.debug(f"[Scheduler] Fetched {len(users_data)} users from panel. Storing in DB.")
            await store_panel_users_in_db(users_data)
            logger.info("[Scheduler] Hourly user DB update completed successfully.")
        else:
            logger.error(f"[Scheduler] Failed to fetch users during scheduled update: {users_data}")
    except Exception as e:
        logger.error(f"[Scheduler] An unexpected error occurred during scheduled user DB update: {e}", exc_info=True)


# --- Nightly Backup ---

async def send_nightly_backup_to_admin(bot: Bot):
    logger.info("[Scheduler] Starting nightly backup task...")
    if not ADMIN_USER_ID:
        logger.error("[Scheduler] Admin User ID not set. Cannot send backup.")
        return

    try:
        success, backup_data, filename = await fetch_latest_backup_ssh()
        if success and backup_data and filename:
            logger.info(f"[Scheduler] Backup '{filename}' fetched. Sending to admin {ADMIN_USER_ID}.")
            backup_time = datetime.datetime.now().strftime("%Y/%m/%d %H:%M")
            caption = Messages.Admin.Backup.NIGHTLY_BACKUP_CAPTION(time=backup_time, filename=html_decoration.quote(filename))
            document = BufferedInputFile(backup_data.getvalue(), filename=filename)
            await bot.send_document(chat_id=ADMIN_USER_ID, document=document, caption=caption, parse_mode="HTML")
            logger.info(f"[Scheduler] Nightly backup sent to admin successfully.")
        else:
            logger.error(f"[Scheduler] Failed to fetch nightly backup. Error: {backup_data}")
            error_msg = html_decoration.quote(str(backup_data))
            await bot.send_message(ADMIN_USER_ID, Messages.Admin.Backup.NIGHTLY_BACKUP_FETCH_ERROR(error=error_msg), parse_mode="HTML")
    except Exception as e:
        logger.critical(f"[Scheduler] An unexpected error occurred during nightly backup: {e}", exc_info=True)
        error_msg = html_decoration.quote(str(e))
        await bot.send_message(ADMIN_USER_ID, Messages.Admin.Backup.NIGHTLY_BACKUP_UNEXPECTED_ERROR(error=error_msg), parse_mode="HTML")


# --- NEW: Trial Expiry Warning (30 minutes before) ---

async def check_trial_expiry_warning(bot: Bot):
    """Check for trial users expiring in 30 minutes and send warning notifications"""
    logger.info("[Scheduler] Starting trial expiry warning check...")
    
    try:
        from database import get_all_users_with_uuid
        users_to_check = get_all_users_with_uuid()
        trial_users = [(tg_id, uuid) for tg_id, uuid, is_trial in users_to_check if is_trial]
        
        logger.info(f"[Scheduler] Found {len(trial_users)} trial users to check for expiry warnings.")
        
        now = datetime.datetime.now()
        warning_threshold = now + datetime.timedelta(minutes=30)
        
        for user_tg_id, user_uuid in trial_users:
            logger.debug(f"[Scheduler] Checking trial expiry for user TG ID: {user_tg_id}, UUID: {user_uuid}")
            await asyncio.sleep(0.1)  # Be gentle with the API
            
            success, details_resp, status = await fetch_user_details_api(user_uuid)
            if not success:
                logger.warning(f"[Scheduler] API call failed for trial user {user_uuid}. Status: {status}")
                continue
            
            try:
                user_dict = json.loads(details_resp)
                if not user_dict.get("enable", False):
                    continue
                
                start_date_str = user_dict.get("start_date")
                package_days = user_dict.get("package_days", 1)  # Trial is usually 1 day
                
                if not start_date_str:
                    continue
                
                start_date = datetime.datetime.strptime(start_date_str, "%Y-%m-%d")
                expiry_datetime = start_date + datetime.timedelta(days=package_days)
                
                # Check if expiry is within 30 minutes
                if now <= expiry_datetime <= warning_threshold:
                    logger.info(f"[Scheduler] Trial user {user_tg_id} expires in ~30 minutes. Sending warning.")
                    
                    # Create notification message with buy button
                    builder = InlineKeyboardBuilder()
                    builder.button(text=Messages.User.Notifications.BUTTON_BUY_NOW, callback_data="buy_plan_trial_warning")
                    builder.button(text=Messages.User.Notifications.BUTTON_VIEW_PLANS, callback_data="view_plans_trial_warning")
                    builder.button(text=Messages.User.Notifications.BUTTON_CONTACT_SUPPORT, url="https://t.me/mbnproo")
                    builder.adjust(1)
                    
                    notification_text = (
                        Messages.User.Notifications.TRIAL_EXPIRING_30MIN_TITLE +
                        Messages.User.Notifications.TRIAL_EXPIRING_30MIN_MESSAGE
                    )
                    
                    try:
                        await bot.send_message(
                            user_tg_id, 
                            notification_text, 
                            reply_markup=builder.as_markup(),
                            parse_mode="HTML"
                        )
                        logger.info(f"[Scheduler] Trial expiry warning sent successfully to {user_tg_id}")
                    except Exception as notify_err:
                        logger.error(f"[Scheduler] Failed to send trial warning to {user_tg_id}: {notify_err}")
                
            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"[Scheduler] Error processing trial user data {user_uuid}: {e}")
                
    except Exception as e:
        logger.critical(f"[Scheduler] Unexpected error in trial expiry warning check: {e}", exc_info=True)


# --- NEW: Subscription Renewal Reminders (Daily at 12:00 PM Iran Time) ---

async def check_subscription_renewal_reminders(bot: Bot):
    """Check for subscriptions expiring in 5, 3, or 1 days and send renewal reminders at 12:00 PM Iran time"""
    logger.info("[Scheduler] Starting subscription renewal reminder check...")
    
    # Check if it's 12:00 PM Iran time (UTC+3:30)
    iran_tz = pytz.timezone('Asia/Tehran')
    iran_now = datetime.datetime.now(iran_tz)
    
    # Only run at 12:00 PM Iran time (allow 5 minute window)
    if not (11 <= iran_now.hour <= 12 and iran_now.minute <= 5):
        logger.debug("[Scheduler] Not 12:00 PM Iran time, skipping renewal reminders")
        return
    
    try:
        from database import get_all_users_with_uuid
        users_to_check = get_all_users_with_uuid()
        paid_users = [(tg_id, uuid) for tg_id, uuid, is_trial in users_to_check if not is_trial]
        
        logger.info(f"[Scheduler] Found {len(paid_users)} paid users to check for renewal reminders.")
        
        today = datetime.date.today()
        
        for user_tg_id, user_uuid in paid_users:
            logger.debug(f"[Scheduler] Checking renewal reminder for user TG ID: {user_tg_id}, UUID: {user_uuid}")
            await asyncio.sleep(0.1)  # Be gentle with the API
            
            success, details_resp, status = await fetch_user_details_api(user_uuid)
            if not success:
                logger.warning(f"[Scheduler] API call failed for user {user_uuid}. Status: {status}")
                continue
            
            try:
                user_dict = json.loads(details_resp)
                if not user_dict.get("enable", False):
                    continue
                
                user_name = user_dict.get('name', 'کاربر عزیز')
                start_date_str = user_dict.get("start_date")
                package_days = user_dict.get("package_days")
                
                if not (start_date_str and isinstance(package_days, int)):
                    continue
                
                start_date = datetime.datetime.strptime(start_date_str, "%Y-%m-%d").date()
                expiry_date = start_date + datetime.timedelta(days=package_days)
                remaining_days = (expiry_date - today).days
                
                # Format expiry date in Persian
                jalali_expiry = jdatetime.date.fromgregorian(date=expiry_date).strftime('%Y/%m/%d')
                
                notification_text = None
                title = None
                
                if remaining_days == 5:
                    title = Messages.User.Notifications.RENEWAL_5DAYS_TITLE
                    notification_text = Messages.User.Notifications.RENEWAL_5DAYS_MESSAGE(user_name, jalali_expiry)
                elif remaining_days == 3:
                    title = Messages.User.Notifications.RENEWAL_3DAYS_TITLE
                    notification_text = Messages.User.Notifications.RENEWAL_3DAYS_MESSAGE(user_name, jalali_expiry)
                elif remaining_days == 1:
                    title = Messages.User.Notifications.RENEWAL_1DAY_TITLE
                    notification_text = Messages.User.Notifications.RENEWAL_1DAY_MESSAGE(user_name, jalali_expiry)
                
                if notification_text:
                    logger.info(f"[Scheduler] User {user_tg_id} needs renewal reminder ({remaining_days} days remaining)")
                    
                    # Create notification buttons
                    builder = InlineKeyboardBuilder()
                    builder.button(text=Messages.User.Notifications.BUTTON_RENEW_NOW, callback_data="renew_plan_reminder")
                    builder.button(text=Messages.User.Notifications.BUTTON_VIEW_PLANS, callback_data="view_plans_renewal")
                    builder.button(text=Messages.User.Notifications.BUTTON_CONTACT_SUPPORT, url="https://t.me/mbnproo")
                    builder.adjust(1)
                    
                    full_message = title + notification_text
                    
                    try:
                        await bot.send_message(
                            user_tg_id,
                            full_message,
                            reply_markup=builder.as_markup(),
                            parse_mode="HTML"
                        )
                        logger.info(f"[Scheduler] Renewal reminder sent successfully to {user_tg_id} ({remaining_days} days)")
                    except Exception as notify_err:
                        logger.error(f"[Scheduler] Failed to send renewal reminder to {user_tg_id}: {notify_err}")
                
            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"[Scheduler] Error processing renewal user data {user_uuid}: {e}")
                
    except Exception as e:
        logger.critical(f"[Scheduler] Unexpected error in renewal reminder check: {e}", exc_info=True)


# --- NEW: Callback handlers for notification buttons ---

async def handle_notification_callbacks(bot: Bot, callback_data: str, user_id: int):
    """Handle callbacks from notification buttons"""
    try:
        if callback_data in ["buy_plan_trial_warning", "view_plans_trial_warning"]:
            # Redirect to buy subscription flow
            from keyboards import create_plan_menu
            await bot.send_message(
                user_id,
                Messages.User.Purchase.CHOOSE_PLAN_PROMPT,
                reply_markup=create_plan_menu("buy_plan")
            )
        elif callback_data in ["renew_plan_reminder", "view_plans_renewal"]:
            # Redirect to renewal flow
            from keyboards import create_plan_menu
            await bot.send_message(
                user_id,
                Messages.User.Renewal.CHOOSE_PLAN_PROMPT,
                reply_markup=create_plan_menu("renew_plan")
            )
        
        logger.info(f"[Scheduler] Handled notification callback {callback_data} for user {user_id}")
    except Exception as e:
        logger.error(f"[Scheduler] Error handling notification callback {callback_data}: {e}")


# --- NEW: Nightly Comprehensive User Backup ---

async def backup_all_users_data_to_json():
    """
    Fetches all user data from both the panel and the local bot database,
    merges them, and saves them to a single JSON file as a backup.
    This task is scheduled to run every night at midnight Tehran time.
    """
    logger.info("[Scheduler] Starting nightly comprehensive user backup to bot-users.json...")
    
    # 1. Fetch data from Panel
    success_panel, panel_users = await fetch_panel_users()
    if not success_panel or not isinstance(panel_users, list):
        logger.error(f"[Backup] Failed to fetch users from Hiddify panel. Aborting backup. Error: {panel_users}")
        return
    logger.info(f"[Backup] Fetched {len(panel_users)} users from the panel.")

    # 2. Fetch data from local Bot DB
    bot_users_tuples = get_all_bot_users()
    logger.info(f"[Backup] Fetched {len(bot_users_tuples)} users from the local database.")
    
    # Create a lookup dictionary for bot users by telegram_id for efficient merging
    bot_users_map = {user[2]: {'telegram_full_name': user[0], 'telegram_username': user[1]} for user in bot_users_tuples}

    # 3. Merge data
    comprehensive_users = []
    for panel_user in panel_users:
        telegram_id_str = panel_user.get("telegram_id")
        
        merged_user_data = {
            "panel_data": panel_user,
            "bot_data": None 
        }

        if telegram_id_str:
            try:
                telegram_id = int(telegram_id_str)
                if telegram_id in bot_users_map:
                    merged_user_data["bot_data"] = bot_users_map.pop(telegram_id) # Pop to find leftovers
            except (ValueError, TypeError):
                logger.warning(f"[Backup] Invalid telegram_id '{telegram_id_str}' in panel data for user {panel_user.get('name')}.")
        
        comprehensive_users.append(merged_user_data)
        
    # Add any bot users that were not found in the panel (e.g., users who started bot but never auth'd)
    if bot_users_map:
        logger.info(f"[Backup] Found {len(bot_users_map)} users in bot DB who were not in the panel data. Adding them as leftovers.")
        for telegram_id, bot_data in bot_users_map.items():
            comprehensive_users.append({
                "panel_data": None,
                "bot_data": {**bot_data, "telegram_id": telegram_id}
            })

    # 4. Save to JSON file
    backup_file_path = os.path.join(PROJECT_DIR, "bot-users.json")
    try:
        with open(backup_file_path, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_users, f, ensure_ascii=False, indent=2)
        logger.info(f"[Backup] Successfully saved comprehensive backup for {len(comprehensive_users)} users to {backup_file_path}")
    except (IOError, TypeError) as e:
        logger.error(f"[Backup] Failed to write to {backup_file_path}: {e}", exc_info=True)


# --- Instant Backup (On-Demand & Scheduled) ---

async def send_instant_backup_to_admin(bot: Bot):
    """Creates a fresh backup on the server and sends it to the admin."""
    logger.info("[Scheduler] Starting instant backup task (manual/scheduled)...")
    if not ADMIN_USER_ID:
        logger.error("[Scheduler] Admin User ID not set. Cannot send instant backup.")
        return

    try:
        success, backup_data, filename = await create_instant_backup_ssh()
        if success and backup_data and filename:
            logger.info(f"[Scheduler] Instant backup '{filename}' fetched. Sending to admin {ADMIN_USER_ID}.")
            backup_time = datetime.datetime.now().strftime("%Y/%m/%d %H:%M")
            proxy_path = f"{PANEL_LINK_PREFIX}{HIDDIFY_API_PROXY_PATH}"
            caption = Messages.Admin.Backup.SENDING_CAPTION(filename=html_decoration.quote(filename), proxy_path=proxy_path)
            document = BufferedInputFile(backup_data.getvalue(), filename=filename)
            await bot.send_document(chat_id=ADMIN_USER_ID, document=document, caption=caption, parse_mode="HTML")
            logger.info("[Scheduler] Instant backup sent to admin successfully.")
        else:
            logger.error(f"[Scheduler] Failed to fetch instant backup. Error: {backup_data}")
            error_msg = html_decoration.quote(str(backup_data))
            await bot.send_message(ADMIN_USER_ID, Messages.Admin.Backup.FETCH_ERROR(error=error_msg), parse_mode="HTML")
    except Exception as e:
        logger.critical(f"[Scheduler] An unexpected error occurred during instant backup: {e}", exc_info=True)
        error_msg = html_decoration.quote(str(e))
        await bot.send_message(ADMIN_USER_ID, Messages.Admin.Backup.FETCH_ERROR(error=error_msg), parse_mode="HTML")